'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { User } from '@/types';
import { localStorageManager } from '@/utils/localStorage';
import { getDeveloperInfo } from '@/utils/appInfo';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile: boolean;
  currentUser: User | null;
}

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  href: string;
  roles?: string[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'لوحة التحكم',
    icon: '🏠',
    href: '/',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'students',
    title: 'إدارة الطلاب',
    icon: '👨‍🎓',
    href: '/students',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'teachers',
    title: 'إدارة المعلمين',
    icon: '👨‍🏫',
    href: '/teachers',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'employees',
    title: 'إدارة الموظفين',
    icon: '👨‍💼',
    href: '/employees',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'classes',
    title: 'إدارة الصفوف',
    icon: '🏫',
    href: '/classes',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'subjects',
    title: 'إدارة المواد',
    icon: '📚',
    href: '/subjects',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'schedule',
    title: 'الجدول الدراسي',
    icon: '📅',
    href: '/schedule',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  },
  {
    id: 'settings',
    title: 'الإعدادات',
    icon: '⚙️',
    href: '/settings',
    roles: ['developer', 'admin', 'teacher', 'student', 'parent']
  }
];

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, isMobile, currentUser }) => {
  const pathname = usePathname();
  const [schoolName, setSchoolName] = useState('نظام إدارة المدرسة');

  // معلومات المطور
  const developerInfo = getDeveloperInfo();

  // تحميل اسم المدرسة من الإعدادات
  useEffect(() => {
    const settings = localStorageManager.getSettings();
    if (settings && settings.schoolName) {
      setSchoolName(settings.schoolName);
    }
  }, []);

  // الاستماع لتحديثات الإعدادات
  useEffect(() => {
    const handleSettingsUpdate = (event: CustomEvent) => {
      const newSettings = event.detail;
      if (newSettings && newSettings.schoolName) {
        setSchoolName(newSettings.schoolName);
      }
    };

    window.addEventListener('settingsUpdated', handleSettingsUpdate as EventListener);

    return () => {
      window.removeEventListener('settingsUpdated', handleSettingsUpdate as EventListener);
    };
  }, []);

  // تصفية العناصر حسب دور المستخدم
  const filteredMenuItems = menuItems.filter(item =>
    !item.roles || !currentUser || item.roles.includes(currentUser.role)
  );

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <>
      {/* الشريط الجانبي */}
      <div className={`
        fixed top-0 right-0 h-full w-64 bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        ${isMobile ? 'w-64' : 'w-64'}
      `}>
        {/* رأس الشريط الجانبي */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">🏫</span>
              </div>
              <div>
                <h2 className="text-lg font-bold text-gray-800" data-sidebar-title>{schoolName}</h2>
                <p className="text-sm text-gray-600">نظام الإدارة</p>
              </div>
            </div>
            {isMobile && (
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span className="text-xl">✕</span>
              </button>
            )}
          </div>
        </div>



        {/* قائمة التنقل */}
        <nav className="flex-1 overflow-y-auto p-4">
          <ul className="space-y-2">
            {filteredMenuItems.map((item) => (
              <li key={item.id}>
                <Link
                  href={item.href}
                  onClick={isMobile ? onClose : undefined}
                  className={`
                    flex items-center space-x-3 space-x-reverse p-3 rounded-xl transition-all duration-200
                    ${isActive(item.href)
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'
                    }
                  `}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span className="font-medium">{item.title}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* تذييل الشريط الجانبي */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-center text-sm text-gray-500">
            <p className="mb-1">© {new Date().getFullYear()} {schoolName}</p>
            <p className="text-xs">تطوير: {developerInfo.name}</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
