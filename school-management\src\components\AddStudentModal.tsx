'use client';

import React, { useState, useEffect } from 'react';
import { Student, Class } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface AddStudentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  editingStudent?: Student | null;
}

const AddStudentModal: React.FC<AddStudentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingStudent
}) => {
  const [formData, setFormData] = useState<Partial<Student>>({
    name: '',
    motherName: '',
    dateOfBirth: '',
    gender: 'male',
    address: '',
    classId: '',
    parentPhone: '',
    emergencyContact: '',
    medicalInfo: ''
  });

  const [classes, setClasses] = useState<Class[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      loadClasses();
      if (editingStudent) {
        setFormData({
          ...editingStudent
        });
      } else {
        resetForm();
      }
    }
  }, [isOpen, editingStudent]);

  const loadClasses = () => {
    const classesData = localStorageManager.getClasses();
    setClasses(classesData);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      motherName: '',
      dateOfBirth: '',
      gender: 'male',
      address: '',
      classId: '',
      parentPhone: '',
      emergencyContact: '',
      medicalInfo: ''
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'اسم الطالب مطلوب';
    } else {
      // التحقق من عدم تكرار اسم الطالب
      const existingStudents = localStorageManager.getStudents();
      const isDuplicate = existingStudents.some(s =>
        s.id !== editingStudent?.id && // استثناء الطالب الحالي عند التعديل
        s.name.toLowerCase().trim() === formData.name!.toLowerCase().trim()
      );

      if (isDuplicate) {
        newErrors.name = 'اسم الطالب موجود مسبقاً';
      }
    }

    if (!formData.classId) {
      newErrors.classId = 'الصف مطلوب';
    }

    if (!formData.parentPhone?.trim()) {
      newErrors.parentPhone = 'رقم هاتف ولي الأمر مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const studentData: Student = {
        id: editingStudent?.id || `student-${Date.now()}`,
        name: formData.name!,
        studentId: editingStudent?.studentId || `STU-${Date.now()}`, // توليد رقم تلقائي
        email: `${formData.name?.replace(/\s+/g, '').toLowerCase()}@school.edu`, // بريد تلقائي
        phone: '', // فارغ
        motherName: formData.motherName || '',
        dateOfBirth: formData.dateOfBirth || '',
        gender: formData.gender! as 'male' | 'female',
        address: formData.address || '',
        classId: formData.classId!,
        parentName: 'ولي الأمر', // قيمة افتراضية
        parentPhone: formData.parentPhone!,
        parentEmail: '', // فارغ
        emergencyContact: formData.emergencyContact || '',
        medicalInfo: formData.medicalInfo || '',
        status: 'active', // افتراضي نشط
        enrollmentDate: editingStudent?.enrollmentDate || new Date(),
        createdAt: editingStudent?.createdAt || new Date(),
        updatedAt: new Date()
      };

      if (editingStudent) {
        localStorageManager.updateStudent(editingStudent.id, studentData);
      } else {
        localStorageManager.addStudent(studentData);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ الطالب:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Student, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // إزالة رسالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900">
              {editingStudent ? 'تعديل الطالب' : 'إضافة طالب جديد'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* معلومات الطالب الأساسية */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات الطالب الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* اسم الطالب */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الطالب *
                  </label>
                  <input
                    type="text"
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="الاسم الكامل"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name}</p>
                  )}
                </div>

                {/* اسم الأم الثلاثي */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الأم الثلاثي
                  </label>
                  <input
                    type="text"
                    value={formData.motherName || ''}
                    onChange={(e) => handleInputChange('motherName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم الأم الثلاثي"
                  />
                </div>

                {/* تاريخ الميلاد */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ الميلاد
                  </label>
                  <input
                    type="date"
                    value={formData.dateOfBirth || ''}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* الجنس */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الجنس
                  </label>
                  <select
                    value={formData.gender || 'male'}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="male">ذكر</option>
                    <option value="female">أنثى</option>
                  </select>
                </div>
              </div>

              {/* العنوان */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  العنوان
                </label>
                <textarea
                  value={formData.address || ''}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="عنوان السكن"
                />
              </div>
            </div>

            {/* معلومات الصف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الصف *
              </label>
              <select
                value={formData.classId || ''}
                onChange={(e) => handleInputChange('classId', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.classId ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">اختر الصف</option>
                {classes.map(classItem => (
                  <option key={classItem.id} value={classItem.id}>
                    {classItem.name}
                  </option>
                ))}
              </select>
              {errors.classId && (
                <p className="text-red-500 text-xs mt-1">{errors.classId}</p>
              )}
            </div>

            {/* معلومات ولي الأمر */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات ولي الأمر</h3>
              <div className="space-y-4">


                {/* رقم هاتف ولي الأمر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم هاتف ولي الأمر *
                  </label>
                  <input
                    type="tel"
                    value={formData.parentPhone || ''}
                    onChange={(e) => handleInputChange('parentPhone', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.parentPhone ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="07xxxxxxxx"
                  />
                  {errors.parentPhone && (
                    <p className="text-red-500 text-xs mt-1">{errors.parentPhone}</p>
                  )}
                </div>



                {/* جهة اتصال الطوارئ */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    جهة اتصال الطوارئ
                  </label>
                  <input
                    type="text"
                    value={formData.emergencyContact || ''}
                    onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم ورقم هاتف"
                  />
                </div>
              </div>
            </div>

            {/* معلومات إضافية */}
            <div>
              {/* المعلومات الطبية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المعلومات الطبية
                </label>
                <textarea
                  value={formData.medicalInfo || ''}
                  onChange={(e) => handleInputChange('medicalInfo', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أي معلومات طبية مهمة..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                )}
                {loading ? 'جاري الحفظ...' : (editingStudent ? 'تحديث' : 'إضافة')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddStudentModal;
