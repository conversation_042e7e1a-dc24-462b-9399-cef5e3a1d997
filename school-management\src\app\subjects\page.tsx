'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import AddSubjectModal from '@/components/AddSubjectModal';
import { Subject } from '@/types';
import { localStorageManager, searchUtils } from '@/utils/localStorage';

export default function SubjectsPage() {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGrade, setSelectedGrade] = useState('');

  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const subjectsData = localStorageManager.getSubjects();
      setSubjects(subjectsData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredSubjects = subjects.filter(subject => {
    const matchesSearch = searchQuery === '' ||
      subject.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesGrade = selectedGrade === '' ||
      subject.grade.toString() === selectedGrade;
    return matchesSearch && matchesGrade;
  });

  const handleDeleteSubject = (subjectId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه المادة؟')) {
      localStorageManager.deleteSubject(subjectId);
      loadData();
    }
  };

  const handleEditSubject = (subject: Subject) => {
    setEditingSubject(subject);
    setShowAddModal(true);
  };

  const getUniqueGrades = () => {
    const grades = subjects.map(s => s.grade);
    return [...new Set(grades)].sort((a, b) => a - b);
  };

  const getGradeName = (grade: number) => {
    if (grade === 0) return 'جميع الصفوف';
    const gradeNames = {
      1: 'الأول',
      2: 'الثاني',
      3: 'الثالث',
      4: 'الرابع',
      5: 'الخامس',
      6: 'السادس'
    };
    return gradeNames[grade as keyof typeof gradeNames] || `الصف ${grade}`;
  };



  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المواد</h1>
            <p className="text-gray-600 mt-1">إدارة وتنظيم المواد الدراسية</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
            <Button 
              variant="primary" 
              onClick={() => setShowAddModal(true)}
            >
              إضافة مادة جديدة
            </Button>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث
              </label>
              <input
                type="text"
                placeholder="ابحث باسم المادة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب الصف
              </label>
              <select
                value={selectedGrade}
                onChange={(e) => setSelectedGrade(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الفئات</option>
                <option value="0">المواد العامة (جميع الصفوف)</option>
                {getUniqueGrades().filter(grade => grade > 0).map(grade => (
                  <option key={grade} value={grade.toString()}>
                    الصف {getGradeName(grade)} فقط
                  </option>
                ))}
              </select>
            </div>
          </div>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">{subjects.length}</div>
              <div className="text-blue-600 text-sm">إجمالي المواد</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">
                {subjects.filter(s => s.grade === 0).length}
              </div>
              <div className="text-green-600 text-sm">المواد العامة</div>
            </div>
          </Card>
        </div>

        {/* قائمة المواد */}
        <Card title="قائمة المواد" subtitle={`عدد النتائج: ${filteredSubjects.length}`}>
          {filteredSubjects.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">📚</div>
              <p className="text-gray-600">لا توجد نتائج مطابقة للبحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-2 font-semibold text-gray-700 w-16">ت</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">اسم المادة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الصف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSubjects.map((subject, index) => (
                    <tr key={subject.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-2 text-center text-sm font-medium text-gray-600 w-16">{index + 1}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-bold">
                              {subject.name.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{subject.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`text-sm px-2 py-1 rounded-full ${
                          subject.grade === 0
                            ? 'bg-blue-100 text-blue-800 font-medium'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {getGradeName(subject.grade)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEditSubject(subject)}
                          >
                            تعديل
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteSubject(subject.id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>

        {/* مكون إضافة/تعديل المادة */}
        <AddSubjectModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setEditingSubject(null);
          }}
          onSave={loadData}
          editingSubject={editingSubject}
        />
      </div>
    </Layout>
  );
}
