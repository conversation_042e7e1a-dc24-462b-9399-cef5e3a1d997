'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import { localStorageManager } from '@/utils/localStorage';

export default function UnauthorizedPage() {
  const router = useRouter();
  const currentUser = localStorageManager.getCurrentUser();

  const handleGoBack = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleLogout = () => {
    localStorageManager.logout();
    router.push('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4" dir="rtl">
      <div className="text-center max-w-md mx-auto">
        {/* أزرار التنقل */}
        <NavigationButtons
          className="mb-6"
          customBackAction={handleGoBack}
          customHomeAction={handleGoHome}
        />
        {/* أيقونة التحذير */}
        <div className="w-24 h-24 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
          <span className="text-white text-4xl">🚫</span>
        </div>

        {/* العنوان الرئيسي */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          غير مخول للوصول
        </h1>

        {/* الرسالة */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 mb-6">
          <p className="text-gray-700 mb-4">
            عذراً، لا تملك الصلاحية للوصول إلى هذه الصفحة.
          </p>
          
          {currentUser && (
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <p className="text-sm text-gray-600 mb-2">معلومات المستخدم الحالي:</p>
              <div className="text-right">
                <p className="font-medium text-gray-900">{currentUser.name}</p>
                <p className="text-sm text-gray-600">{currentUser.email}</p>
                <p className="text-sm text-gray-600">
                  الدور: {
                    currentUser.role === 'developer' ? 'المطور' :
                    currentUser.role === 'admin' ? 'مدير النظام' :
                    currentUser.role === 'teacher' ? 'معلم' :
                    currentUser.role === 'student' ? 'طالب' :
                    currentUser.role === 'parent' ? 'ولي أمر' : 'غير محدد'
                  }
                </p>
              </div>
            </div>
          )}

          <p className="text-sm text-gray-600">
            يرجى التواصل مع مدير النظام للحصول على الصلاحيات المطلوبة.
          </p>
        </div>

        {/* الأزرار */}
        <div className="space-y-3">
          <Button
            variant="primary"
            onClick={handleGoHome}
            fullWidth
            className="h-12"
          >
            العودة للصفحة الرئيسية
          </Button>
          
          <Button
            variant="secondary"
            onClick={handleGoBack}
            fullWidth
            className="h-12"
          >
            العودة للصفحة السابقة
          </Button>
          
          <Button
            variant="danger"
            onClick={handleLogout}
            fullWidth
            className="h-12"
          >
            تسجيل الخروج
          </Button>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500">
            إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الدعم الفني
          </p>
        </div>
      </div>
    </div>
  );
}
