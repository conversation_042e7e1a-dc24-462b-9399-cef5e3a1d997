@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

/* RTL Support */
html {
  direction: rtl;
  font-family: 'Cairo', sans-serif;
}

:root {
  --background: #f0f0f3;
  --foreground: #2d3748;
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --background-light: #f0f0f3;
  --background-dark: #e6e6e9;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --shadow-light: #ffffff;
  --shadow-dark: #d1d9e6;
  --border-radius: 20px;
  --border-radius-small: 12px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Cairo', sans-serif;
  --font-mono: 'Cairo', monospace;
}

body {
  direction: rtl;
  text-align: right;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: var(--foreground);
  font-family: 'Cairo', sans-serif;
}

/* Neumorphic Design Components */
.neumorphic {
  background: var(--background-light);
  border-radius: var(--border-radius);
  box-shadow:
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  transition: all 0.3s ease;
}

.neumorphic:hover {
  box-shadow:
    12px 12px 20px var(--shadow-dark),
    -12px -12px 20px var(--shadow-light);
}

.neumorphic-inset {
  background: var(--background-light);
  border-radius: var(--border-radius-small);
  box-shadow:
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
}

.neumorphic-button {
  background: linear-gradient(145deg, #f0f0f3, #cacaca);
  border-radius: var(--border-radius-small);
  box-shadow:
    6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.neumorphic-button:hover {
  box-shadow:
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  transform: translateY(-2px);
}

.neumorphic-button:active {
  box-shadow:
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
  transform: translateY(0);
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  box-shadow:
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  padding: 1.5rem;
  margin: 1rem;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow:
    12px 12px 20px var(--shadow-dark),
    -12px -12px 20px var(--shadow-light);
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-small);
  box-shadow:
    6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  box-shadow:
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  transform: translateY(-2px);
}

.btn-secondary {
  background: linear-gradient(145deg, #f0f0f3, #cacaca);
  color: var(--text-primary);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-small);
  box-shadow:
    6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-secondary:hover {
  box-shadow:
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  transform: translateY(-2px);
}

/* Input Styles */
.input-field {
  background: var(--background-light);
  border-radius: var(--border-radius-small);
  box-shadow:
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
  padding: 0.75rem 1rem;
  width: 100%;
  text-align: right;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-family: 'Cairo', sans-serif;
}

.input-field::placeholder {
  color: var(--text-secondary);
  text-align: right;
}

.input-field:focus {
  box-shadow:
    inset 6px 6px 12px var(--shadow-dark),
    inset -6px -6px 12px var(--shadow-light),
    0 0 0 2px var(--primary-color);
}

/* أنيميشن للخلفية المتحركة */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
