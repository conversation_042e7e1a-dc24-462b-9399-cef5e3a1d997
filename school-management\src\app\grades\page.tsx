'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import { Grade, Student, Subject, Class } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

export default function GradesPage() {
  const [grades, setGrades] = useState<Grade[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedSemester, setSelectedSemester] = useState('');
  const [selectedExamType, setSelectedExamType] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingGrade, setEditingGrade] = useState<Grade | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const gradesData = localStorageManager.getGrades();
      const studentsData = localStorageManager.getStudents();
      const subjectsData = localStorageManager.getSubjects();
      const classesData = localStorageManager.getClasses();
      
      setGrades(gradesData);
      setStudents(studentsData);
      setSubjects(subjectsData);
      setClasses(classesData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredGrades = grades.filter(grade => {
    const matchesClass = selectedClass === '' || grade.classId === selectedClass;
    const matchesSubject = selectedSubject === '' || grade.subjectId === selectedSubject;
    const matchesSemester = selectedSemester === '' || grade.semester === selectedSemester;
    const matchesExamType = selectedExamType === '' || grade.examType === selectedExamType;
    
    return matchesClass && matchesSubject && matchesSemester && matchesExamType;
  });

  const getStudentName = (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    return student ? student.name : 'غير محدد';
  };

  const getSubjectName = (subjectId: string) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : 'غير محدد';
  };

  const getClassName = (classId: string) => {
    const classData = classes.find(c => c.id === classId);
    return classData ? classData.name : 'غير محدد';
  };

  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600 bg-green-100';
    if (percentage >= 80) return 'text-blue-600 bg-blue-100';
    if (percentage >= 70) return 'text-yellow-600 bg-yellow-100';
    if (percentage >= 60) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getGradeLetter = (percentage: number) => {
    if (percentage >= 90) return 'ممتاز';
    if (percentage >= 80) return 'جيد جداً';
    if (percentage >= 70) return 'جيد';
    if (percentage >= 60) return 'مقبول';
    return 'راسب';
  };

  const handleDeleteGrade = (gradeId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الدرجة؟')) {
      localStorageManager.deleteGrade(gradeId);
      loadData();
    }
  };

  const handleEditGrade = (grade: Grade) => {
    setEditingGrade(grade);
    setShowAddModal(true);
  };

  const calculateClassAverage = () => {
    if (filteredGrades.length === 0) return 0;
    const total = filteredGrades.reduce((sum, grade) => sum + grade.percentage, 0);
    return Math.round(total / filteredGrades.length);
  };

  const getTopPerformers = () => {
    const studentAverages = students.map(student => {
      const studentGrades = filteredGrades.filter(g => g.studentId === student.id);
      if (studentGrades.length === 0) return null;
      
      const average = studentGrades.reduce((sum, g) => sum + g.percentage, 0) / studentGrades.length;
      return { student, average };
    }).filter(item => item !== null).sort((a, b) => b!.average - a!.average);

    return studentAverages.slice(0, 3);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الدرجات</h1>
            <p className="text-gray-600 mt-1">تسجيل ومتابعة درجات الطلاب والامتحانات</p>
          </div>
          <Button 
            variant="primary" 
            onClick={() => setShowAddModal(true)}
            className="mt-4 md:mt-0"
          >
            إضافة درجة جديدة
          </Button>
        </div>

        {/* أدوات التصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الصف
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الصفوف</option>
                {classes.map(classItem => (
                  <option key={classItem.id} value={classItem.id}>
                    {classItem.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المادة
              </label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="input-field"
              >
                <option value="">جميع المواد</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفصل الدراسي
              </label>
              <select
                value={selectedSemester}
                onChange={(e) => setSelectedSemester(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الفصول</option>
                <option value="first">الفصل الأول</option>
                <option value="second">الفصل الثاني</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع الامتحان
              </label>
              <select
                value={selectedExamType}
                onChange={(e) => setSelectedExamType(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الأنواع</option>
                <option value="midterm">امتحان نصف الفصل</option>
                <option value="final">امتحان نهائي</option>
                <option value="quiz">اختبار قصير</option>
                <option value="assignment">واجب</option>
                <option value="participation">مشاركة</option>
              </select>
            </div>
          </div>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">{filteredGrades.length}</div>
              <div className="text-blue-600 text-sm">إجمالي الدرجات</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">{calculateClassAverage()}%</div>
              <div className="text-green-600 text-sm">المتوسط العام</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">
                {filteredGrades.filter(g => g.percentage >= 90).length}
              </div>
              <div className="text-purple-600 text-sm">درجات ممتازة</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-900">
                {filteredGrades.filter(g => g.percentage < 60).length}
              </div>
              <div className="text-red-600 text-sm">درجات راسبة</div>
            </div>
          </Card>
        </div>

        {/* قائمة الدرجات */}
        <Card title="قائمة الدرجات" subtitle={`عدد النتائج: ${filteredGrades.length}`}>
          {filteredGrades.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">📊</div>
              <p className="text-gray-600">لا توجد درجات مطابقة للمرشحات المحددة</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الطالب</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">المادة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الصف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">نوع الامتحان</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الدرجة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">النسبة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">التقدير</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">التاريخ</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredGrades.map((grade) => (
                    <tr key={grade.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">
                          {getStudentName(grade.studentId)}
                        </div>
                      </td>
                      <td className="py-3 px-4">{getSubjectName(grade.subjectId)}</td>
                      <td className="py-3 px-4">{getClassName(grade.classId)}</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                          {grade.examType === 'midterm' && 'نصف الفصل'}
                          {grade.examType === 'final' && 'نهائي'}
                          {grade.examType === 'quiz' && 'اختبار قصير'}
                          {grade.examType === 'assignment' && 'واجب'}
                          {grade.examType === 'participation' && 'مشاركة'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="font-medium">
                          {grade.score} / {grade.maxScore}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(grade.percentage)}`}>
                          {grade.percentage}%
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(grade.percentage)}`}>
                          {getGradeLetter(grade.percentage)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {new Date(grade.examDate).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEditGrade(grade)}
                          >
                            تعديل
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteGrade(grade.id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
}
