@echo off
title School Management System
color 0A

echo.
echo ========================================
echo    School Management System
echo    نظام إدارة المدرسة
echo ========================================
echo.

:: Get the directory where this script is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo Current directory: %CD%
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found!
    echo Make sure you're in the correct directory.
    echo.
    pause
    exit /b 1
)

echo [INFO] Found package.json
echo [INFO] Starting development server...
echo.

:: Start the development server
npm run dev

:: If npm fails, try alternative methods
if errorlevel 1 (
    echo.
    echo [WARNING] npm run dev failed, trying alternative...
    echo.
    npx next dev --port 3000
)

echo.
echo Press any key to exit...
pause >nul
