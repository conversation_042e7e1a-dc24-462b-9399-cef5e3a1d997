// معلومات التطبيق والمطور
export const APP_INFO = {
  name: 'نظام إدارة المدرسة',
  version: '1.0.0',
  description: 'نظام شامل لإدارة المدارس باللغة العربية',
  developer: 'عبيدة العيثاوي',
  developedBy: 'تم التطوير بواسطة عبيدة العيثاوي',
  copyright: `© ${new Date().getFullYear()} عبيدة العيثاوي - جميع الحقوق محفوظة`,
  features: [
    'إدارة الطلاب والمعلمين',
    'إدارة الصفوف والمواد الدراسية',
    'نظام التقييم والدرجات',
    'تقارير شاملة ومفصلة',
    'واجهة عربية متجاوبة',
    'تصميم نيومورفيك عصري'
  ],
  technologies: [
    'Next.js 15',
    'React 19',
    'TypeScript',
    'Tailwind CSS',
    'localStorage'
  ],
  contact: {
    developer: 'عبيدة العيثاوي',
    email: '<EMAIL>',
    phone: '07813332882'
  }
};

// دالة للحصول على معلومات التطبيق
export const getAppInfo = () => APP_INFO;

// دالة للحصول على معلومات المطور
export const getDeveloperInfo = () => ({
  name: APP_INFO.developer,
  developedBy: APP_INFO.developedBy,
  copyright: APP_INFO.copyright,
  contact: APP_INFO.contact
});

// دالة للحصول على معلومات الإصدار
export const getVersionInfo = () => ({
  version: APP_INFO.version,
  name: APP_INFO.name,
  description: APP_INFO.description
});
