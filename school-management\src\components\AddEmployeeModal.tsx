'use client';

import React, { useState, useEffect } from 'react';
import { Employee } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface ExportFunctions {
  exportIndividualPDF: (employee: Employee) => void;
  exportIndividualWord: (employee: Employee) => void;
  exportIndividualHTML: (employee: Employee) => void;
  isExporting: boolean;
}

interface AddEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  editingEmployee?: Employee | null;
  exportFunctions?: ExportFunctions;
}

const AddEmployeeModal: React.FC<AddEmployeeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingEmployee,
  exportFunctions
}) => {
  const [formData, setFormData] = useState<Partial<Employee>>({
    fullName: '',
    title: '',
    department: '',
    firstAppointmentDate: new Date(),
    schoolStartDate: new Date(),
    dateOfBirth: new Date(),
    address: '',
    phone: '',
    employmentType: 'permanent',
    status: 'active',
    responsibilities: []
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // قائمة الأقسام المتاحة
  const departments = [
    'حرفي',
    'كاتب',
    'عامل خدمة',
    'حارس'
  ];



  // قائمة المسؤوليات حسب القسم
  const responsibilitiesByDepartment: Record<string, string[]> = {
    'حرفي': ['صيانة الكهرباء', 'إصلاح السباكة', 'أعمال النجارة', 'صيانة التكييف', 'الإصلاحات العامة', 'صيانة الأجهزة'],
    'كاتب': ['كتابة المراسلات', 'تنظيم الملفات', 'إدخال البيانات', 'استقبال المراجعين', 'إعداد التقارير', 'المتابعة الإدارية'],
    'عامل خدمة': ['تنظيف المباني', 'العناية بالحديقة', 'خدمة المطبخ', 'نقل المواد', 'الصيانة البسيطة', 'إدارة النفايات'],
    'حارس': ['حراسة المبنى', 'مراقبة الدخول والخروج', 'الأمن والسلامة', 'التفتيش الأمني', 'كتابة التقارير الأمنية', 'الاستجابة للطوارئ']
  };

  useEffect(() => {
    if (isOpen) {
      if (editingEmployee) {
        setFormData({
          ...editingEmployee,
          dateOfBirth: new Date(editingEmployee.dateOfBirth),
          firstAppointmentDate: new Date(editingEmployee.firstAppointmentDate),
          schoolStartDate: new Date(editingEmployee.schoolStartDate)
        });
      } else {
        resetForm();
      }
    }
  }, [isOpen, editingEmployee]);

  const resetForm = () => {
    setFormData({
      fullName: '',
      title: '',
      department: '',
      position: '',
      firstAppointmentDate: new Date(),
      schoolStartDate: new Date(),
      dateOfBirth: new Date(),
      address: '',
      phone: '',
      employmentType: 'permanent',
      status: 'active',
      responsibilities: []
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.fullName?.trim()) {
      newErrors.fullName = 'الاسم الرباعي مطلوب';
    } else {
      // التحقق من عدم تكرار اسم الموظف
      const existingEmployees = localStorageManager.getEmployees();
      const isDuplicate = existingEmployees.some(e =>
        e.id !== editingEmployee?.id && // استثناء الموظف الحالي عند التعديل
        e.fullName.toLowerCase().trim() === formData.fullName!.toLowerCase().trim()
      );

      if (isDuplicate) {
        newErrors.fullName = 'اسم الموظف موجود مسبقاً';
      }
    }

    if (!formData.title?.trim()) {
      newErrors.title = 'اللقب مطلوب';
    }

    if (!formData.department?.trim()) {
      newErrors.department = 'القسم مطلوب';
    }

    if (!formData.address?.trim()) {
      newErrors.address = 'عنوان السكن مطلوب';
    }

    if (!formData.phone?.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    } else if (!/^07[0-9]{9}$/.test(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 07 ويحتوي على 11 رقم)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // توليد التسلسل تلقائياً للموظفين الجدد
      let serialNumber = formData.serialNumber;
      if (!editingEmployee) {
        const existingEmployees = localStorageManager.getEmployees();
        const maxSerial = existingEmployees.reduce((max, employee) => {
          const currentSerial = parseInt(employee.serialNumber || employee.employeeId || '0');
          return currentSerial > max ? currentSerial : max;
        }, 0);
        serialNumber = String(maxSerial + 1).padStart(3, '0');
      }

      const employeeData: Employee = {
        id: editingEmployee?.id || `employee-${Date.now()}`,
        serialNumber: serialNumber!,
        fullName: formData.fullName!,
        shortName: formData.shortName,
        title: formData.title!,
        department: formData.department!,
        position: formData.department!, // استخدام القسم كمنصب
        firstAppointmentDate: formData.firstAppointmentDate!,
        schoolStartDate: formData.schoolStartDate!,
        dateOfBirth: formData.dateOfBirth!,
        address: formData.address!,
        phone: formData.phone!,
        employmentType: formData.employmentType!,
        status: formData.status!,
        responsibilities: formData.responsibilities!,
        createdAt: editingEmployee?.createdAt || new Date(),
        updatedAt: new Date(),
        // للتوافق مع النظام القديم
        employeeId: serialNumber,
        name: formData.shortName || formData.fullName
      };

      if (editingEmployee) {
        localStorageManager.updateEmployee(editingEmployee.id, employeeData);
      } else {
        localStorageManager.addEmployee(employeeData);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ الموظف:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Employee, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // إزالة رسالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleResponsibilityToggle = (responsibility: string) => {
    const currentResponsibilities = formData.responsibilities || [];
    const newResponsibilities = currentResponsibilities.includes(responsibility)
      ? currentResponsibilities.filter(r => r !== responsibility)
      : [...currentResponsibilities, responsibility];
    
    handleInputChange('responsibilities', newResponsibilities);
  };

  const getAvailableResponsibilities = () => {
    return formData.department ? responsibilitiesByDepartment[formData.department] || [] : [];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <h2 className="text-xl font-bold text-gray-900">
                {editingEmployee ? 'تعديل الموظف' : 'إضافة موظف جديد'}
              </h2>
              
              {/* أزرار التصدير - تظهر فقط عند التعديل */}
              {editingEmployee && exportFunctions && (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="text-sm text-gray-600">تصدير التقرير:</span>
                  <button
                    type="button"
                    onClick={() => exportFunctions.exportIndividualPDF(editingEmployee)}
                    disabled={exportFunctions.isExporting}
                    className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50 flex items-center space-x-1 space-x-reverse"
                    title="تصدير PDF"
                  >
                    <span>📄</span>
                    <span>PDF</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => exportFunctions.exportIndividualWord(editingEmployee)}
                    disabled={exportFunctions.isExporting}
                    className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50 flex items-center space-x-1 space-x-reverse"
                    title="تصدير Word"
                  >
                    <span>📝</span>
                    <span>Word</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => exportFunctions.exportIndividualHTML(editingEmployee)}
                    disabled={exportFunctions.isExporting}
                    className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 disabled:opacity-50 flex items-center space-x-1 space-x-reverse"
                    title="تصدير HTML"
                  >
                    <span>🌐</span>
                    <span>HTML</span>
                  </button>
                </div>
              )}
            </div>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* المعلومات الشخصية */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات الشخصية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* الاسم الرباعي */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم الرباعي *
                  </label>
                  <input
                    type="text"
                    value={formData.fullName || ''}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.fullName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="أدخل الاسم الرباعي"
                  />
                  {errors.fullName && (
                    <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>
                  )}
                </div>

                {/* اللقب */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اللقب *
                  </label>
                  <input
                    type="text"
                    value={formData.title || ''}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.title ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="السيد، الأستاذ، المهندس..."
                  />
                  {errors.title && (
                    <p className="text-red-500 text-xs mt-1">{errors.title}</p>
                  )}
                </div>
              </div>
            </div>

            {/* اسم الأم الثلاثي */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم الأم الثلاثي
              </label>
              <input
                type="text"
                value={formData.shortName || ''}
                onChange={(e) => handleInputChange('shortName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="اسم الأم الثلاثي"
              />
              <p className="text-xs text-gray-500 mt-1">اسم الأم مطلوب للوثائق الرسمية</p>
            </div>

            {/* تاريخ التولد */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ التولد
              </label>
              <input
                type="date"
                value={formData.dateOfBirth ? formData.dateOfBirth.toISOString().split('T')[0] : ''}
                onChange={(e) => handleInputChange('dateOfBirth', new Date(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* رقم الهاتف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                رقم الهاتف *
              </label>
              <input
                type="tel"
                value={formData.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="07901234567"
              />
              {errors.phone && (
                <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
              )}
            </div>

            {/* عنوان السكن */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                عنوان السكن *
              </label>
              <textarea
                value={formData.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.address ? 'border-red-500' : 'border-gray-300'
                }`}
                rows={2}
                placeholder="أدخل عنوان السكن الكامل"
              />
              {errors.address && (
                <p className="text-red-500 text-xs mt-1">{errors.address}</p>
              )}
            </div>

            {/* المعلومات الوظيفية */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات الوظيفية</h3>
              {/* القسم */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  القسم *
                </label>
                <select
                  value={formData.department || ''}
                  onChange={(e) => {
                    handleInputChange('department', e.target.value);
                    handleInputChange('responsibilities', []); // إعادة تعيين المسؤوليات
                  }}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.department ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">اختر القسم</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
                {errors.department && (
                  <p className="text-red-500 text-xs mt-1">{errors.department}</p>
                )}
              </div>
            </div>

            {/* التواريخ الوظيفية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* تاريخ أول تعيين */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  تاريخ أول تعيين
                </label>
                <input
                  type="date"
                  value={formData.firstAppointmentDate ? formData.firstAppointmentDate.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleInputChange('firstAppointmentDate', new Date(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* تاريخ المباشرة في المدرسة الحالية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  تاريخ المباشرة في المدرسة الحالية
                </label>
                <input
                  type="date"
                  value={formData.schoolStartDate ? formData.schoolStartDate.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleInputChange('schoolStartDate', new Date(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* المسؤوليات */}
            {formData.department && getAvailableResponsibilities().length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المسؤوليات
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 p-4 bg-gray-50 rounded-lg">
                  {getAvailableResponsibilities().map((responsibility) => (
                    <label key={responsibility} className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        checked={(formData.responsibilities || []).includes(responsibility)}
                        onChange={() => handleResponsibilityToggle(responsibility)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{responsibility}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}

            {/* نوع التوظيف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                نوع التوظيف
              </label>
              <select
                value={formData.employmentType || 'permanent'}
                onChange={(e) => handleInputChange('employmentType', e.target.value as 'permanent' | 'assignment' | 'contract')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="permanent">دائم</option>
                <option value="assignment">تنسيب</option>
                <option value="contract">عقد</option>
              </select>
            </div>

            <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                )}
                {loading ? 'جاري الحفظ...' : (editingEmployee ? 'تحديث' : 'إضافة')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddEmployeeModal;
