'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  allowedRoles = [], 
  redirectTo = '/login' 
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = () => {
    try {
      const currentUser = localStorageManager.getCurrentUser();
      
      if (!currentUser) {
        // المستخدم غير مسجل دخول
        router.push(redirectTo);
        return;
      }

      // التحقق من الصلاحيات إذا كانت محددة
      if (allowedRoles.length > 0 && !allowedRoles.includes(currentUser.role)) {
        // المستخدم لا يملك الصلاحية المطلوبة
        router.push('/unauthorized');
        return;
      }

      setUser(currentUser);
      setAuthorized(true);
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error);
      router.push(redirectTo);
    } finally {
      setLoading(false);
    }
  };

  // شاشة التحميل
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <span className="text-white text-2xl">🏫</span>
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من المصادقة...</p>
        </div>
      </div>
    );
  }

  // إذا لم يكن مخولاً، لا نعرض شيئاً (سيتم إعادة التوجيه)
  if (!authorized) {
    return null;
  }

  // عرض المحتوى المحمي
  return <>{children}</>;
};

export default ProtectedRoute;
