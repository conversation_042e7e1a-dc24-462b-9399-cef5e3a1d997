'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import AddEmployeeModal from '@/components/AddEmployeeModal';
import ExportDropdown from '@/components/ExportDropdown';
import EmployeeReportExport from '@/components/EmployeeReportExport';
import { Employee } from '@/types';
import { localStorageManager, searchUtils } from '@/utils/localStorage';

export default function EmployeesPage() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);

  // استخدام مكون التصدير
  const exportFunctions = {
    exportIndividualPDF: () => console.log('تصدير PDF فردي'),
    exportIndividualWord: () => console.log('تصدير Word فردي'),
    exportIndividualHTML: () => console.log('تصدير HTML فردي'),
    exportGroupPDF: () => console.log('تصدير PDF جماعي'),
    exportGroupExcel: () => console.log('تصدير Excel جماعي'),
    exportGroupWord: () => console.log('تصدير Word جماعي'),
    exportGroupHTML: () => console.log('تصدير HTML جماعي'),
    isExporting: false
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const employeesData = localStorageManager.getEmployees();
      setEmployees(employeesData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = searchQuery === '' || 
      searchUtils.searchEmployees(searchQuery, [employee]).length > 0;
    const matchesDepartment = selectedDepartment === '' || 
      employee.department === selectedDepartment;
    return matchesSearch && matchesDepartment;
  });

  const handleDeleteEmployee = (employeeId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
      localStorageManager.deleteEmployee(employeeId);
      loadData();
    }
  };

  const handleEditEmployee = (employee: Employee) => {
    setEditingEmployee(employee);
    setShowAddModal(true);
  };

  const getUniqueDepartments = () => {
    const departments = employees.map(e => e.department);
    return [...new Set(departments)];
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الموظفين</h1>
            <p className="text-gray-600 mt-1">إدارة وتتبع بيانات الموظفين الإداريين</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
            <ExportDropdown
              label={exportFunctions.isExporting ? 'جاري التصدير...' : 'تصدير تقرير جماعي'}
              disabled={exportFunctions.isExporting || employees.length === 0}
              options={[
                {
                  label: 'تصدير PDF',
                  icon: '📄',
                  action: exportFunctions.exportGroupPDF,
                  color: 'text-red-600 hover:text-red-700'
                },
                {
                  label: 'تصدير Excel',
                  icon: '📊',
                  action: exportFunctions.exportGroupExcel,
                  color: 'text-green-600 hover:text-green-700'
                },
                {
                  label: 'تصدير Word',
                  icon: '📝',
                  action: exportFunctions.exportGroupWord,
                  color: 'text-blue-600 hover:text-blue-700'
                },
                {
                  label: 'تصدير HTML',
                  icon: '🌐',
                  action: exportFunctions.exportGroupHTML,
                  color: 'text-purple-600 hover:text-purple-700'
                }
              ]}
            />
            <Button 
              variant="primary" 
              onClick={() => setShowAddModal(true)}
            >
              إضافة موظف جديد
            </Button>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث
              </label>
              <input
                type="text"
                placeholder="ابحث بالاسم أو رقم الموظف أو القسم..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب القسم
              </label>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الأقسام</option>
                {getUniqueDepartments().map(department => (
                  <option key={department} value={department}>
                    {department}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">{employees.length}</div>
              <div className="text-green-600 text-sm">إجمالي الموظفين</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">
                {employees.filter(e => e.employmentType === 'permanent').length}
              </div>
              <div className="text-blue-600 text-sm">الموظفون الدائمون</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-900">
                {employees.filter(e => e.employmentType === 'assignment' || e.employmentType === 'contract').length}
              </div>
              <div className="text-orange-600 text-sm">التنسيب والعقود</div>
            </div>
          </Card>
        </div>

        {/* قائمة الموظفين */}
        <Card title="قائمة الموظفين" subtitle={`عدد النتائج: ${filteredEmployees.length}`}>
          {filteredEmployees.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">👨‍💼</div>
              <p className="text-gray-600">لا توجد نتائج مطابقة للبحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-2 font-semibold text-gray-700 w-16">ت</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الاسم والقسم</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">اللقب</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">رقم الهاتف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">نوع التوظيف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEmployees.map((employee, index) => (
                    <tr key={employee.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-2 text-center text-sm font-medium text-gray-600 w-16">{index + 1}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-bold">
                              {(employee.fullName || employee.name || '').charAt(0)}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{employee.fullName || employee.name}</div>
                            <div className="text-sm text-gray-600">{employee.department}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">{employee.title}</td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-gray-900">{employee.phone}</div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          employee.employmentType === 'permanent'
                            ? 'bg-blue-100 text-blue-800'
                            : employee.employmentType === 'assignment'
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-purple-100 text-purple-800'
                        }`}>
                          {employee.employmentType === 'permanent' ? 'دائم' :
                           employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEditEmployee(employee)}
                          >
                            تعديل
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteEmployee(employee.id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>

        {/* مكون إضافة/تعديل الموظف */}
        <AddEmployeeModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setEditingEmployee(null);
          }}
          onSave={loadData}
          editingEmployee={editingEmployee}
          exportFunctions={{
            exportIndividualPDF: exportFunctions.exportIndividualPDF,
            exportIndividualWord: exportFunctions.exportIndividualWord,
            exportIndividualHTML: exportFunctions.exportIndividualHTML,
            isExporting: exportFunctions.isExporting
          }}
        />
      </div>
    </Layout>
  );
}
