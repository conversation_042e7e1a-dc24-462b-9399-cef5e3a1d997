'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import { localStorageManager } from '@/utils/localStorage';

export default function Reports() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalTeachers: 0,
    totalEmployees: 0,
    totalClasses: 0,
    totalSubjects: 0,
    attendanceRate: 0
  });

  useEffect(() => {
    // التحقق من تسجيل الدخول
    const currentUser = localStorageManager.getCurrentUser();
    if (!currentUser) {
      window.location.href = '/login';
      return;
    }

    loadReportData();
  }, []);

  const loadReportData = () => {
    try {
      const students = localStorageManager.getStudents();
      const teachers = localStorageManager.getTeachers();
      const employees = localStorageManager.getEmployees();
      const classes = localStorageManager.getClasses();
      const subjects = localStorageManager.getSubjects();
      const attendance = localStorageManager.getAttendance();

      // حساب معدل الحضور
      const totalAttendanceRecords = attendance.length;
      const presentRecords = attendance.filter(a => a.status === 'present').length;
      const attendanceRate = totalAttendanceRecords > 0 ?
        Math.round((presentRecords / totalAttendanceRecords) * 100) : 0;

      setStats({
        totalStudents: students.length,
        totalTeachers: teachers.length,
        totalEmployees: employees.length,
        totalClasses: classes.length,
        totalSubjects: subjects.length,
        attendanceRate
      });

      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل بيانات التقارير:', error);
      setLoading(false);
    }
  };

  const generateStudentsReport = () => {
    window.location.href = '/students';
  };

  const generateTeachersReport = () => {
    window.location.href = '/teachers';
  };

  const generateEmployeesReport = () => {
    window.location.href = '/employees';
  };

  const generateAttendanceReport = () => {
    window.location.href = '/attendance';
  };

  const generateGradesReport = () => {
    window.location.href = '/grades';
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* عنوان الصفحة */}
        <div className="mb-8 text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full mb-4 shadow-lg">
            <span className="text-3xl text-white">📈</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            التقارير
          </h1>
          <p className="text-gray-600 text-lg">إنشاء وعرض التقارير المختلفة للنظام</p>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mt-4 rounded-full"></div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
          <Card className="text-center bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-2xl mb-2">👨‍🎓</div>
            <div className="text-2xl font-bold text-blue-600">{stats.totalStudents}</div>
            <div className="text-sm text-gray-600">الطلاب</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-2xl mb-2">👨‍🏫</div>
            <div className="text-2xl font-bold text-green-600">{stats.totalTeachers}</div>
            <div className="text-sm text-gray-600">المعلمين</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="text-2xl mb-2">👨‍💼</div>
            <div className="text-2xl font-bold text-purple-600">{stats.totalEmployees}</div>
            <div className="text-sm text-gray-600">الموظفين</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <div className="text-2xl mb-2">🏫</div>
            <div className="text-2xl font-bold text-orange-600">{stats.totalClasses}</div>
            <div className="text-sm text-gray-600">الصفوف</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200">
            <div className="text-2xl mb-2">📚</div>
            <div className="text-2xl font-bold text-indigo-600">{stats.totalSubjects}</div>
            <div className="text-sm text-gray-600">المواد</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-pink-50 to-pink-100 border-pink-200">
            <div className="text-2xl mb-2">✅</div>
            <div className="text-2xl font-bold text-pink-600">{stats.attendanceRate}%</div>
            <div className="text-sm text-gray-600">الحضور</div>
          </Card>
        </div>

        {/* تقارير النظام */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card hoverable className="text-center" onClick={generateStudentsReport}>
            <div className="text-4xl mb-4">👨‍🎓</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقرير الطلاب</h3>
            <p className="text-gray-600 mb-4">عرض وتصدير بيانات جميع الطلاب</p>
            <Button variant="primary" fullWidth>
              عرض التقرير
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={generateTeachersReport}>
            <div className="text-4xl mb-4">👨‍🏫</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقرير المعلمين</h3>
            <p className="text-gray-600 mb-4">عرض وتصدير بيانات جميع المعلمين</p>
            <Button variant="success" fullWidth>
              عرض التقرير
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={generateEmployeesReport}>
            <div className="text-4xl mb-4">👨‍💼</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقرير الموظفين</h3>
            <p className="text-gray-600 mb-4">عرض وتصدير بيانات جميع الموظفين</p>
            <Button variant="info" fullWidth>
              عرض التقرير
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={generateAttendanceReport}>
            <div className="text-4xl mb-4">✅</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقرير الحضور</h3>
            <p className="text-gray-600 mb-4">عرض وتصدير سجلات الحضور والغياب</p>
            <Button variant="warning" fullWidth>
              عرض التقرير
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={generateGradesReport}>
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقرير الدرجات</h3>
            <p className="text-gray-600 mb-4">عرض وتصدير درجات الطلاب</p>
            <Button variant="secondary" fullWidth>
              عرض التقرير
            </Button>
          </Card>

          <Card hoverable className="text-center">
            <div className="text-4xl mb-4">📋</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقرير شامل</h3>
            <p className="text-gray-600 mb-4">تقرير شامل لجميع بيانات النظام</p>
            <Button variant="primary" fullWidth>
              إنشاء التقرير
            </Button>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
