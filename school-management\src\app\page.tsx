'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import { DashboardStats, Student, Teacher, Class, Grade } from '@/types';
import { localStorageManager } from '@/utils/localStorage';
import { initializeSampleData } from '@/utils/sampleData';

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    totalTeachers: 0,
    totalEmployees: 0,
    totalSubjects: 0,
    presentToday: 0,
    absentToday: 0,
    upcomingExams: 0,
    recentGrades: [],
    attendanceRate: 0,
    topPerformers: []
  });

  const [loading, setLoading] = useState(true);
  const [schoolName, setSchoolName] = useState('نظام إدارة المدرسة');
  const [schoolLogo, setSchoolLogo] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    // تحميل البيانات التجريبية إذا لم تكن موجودة
    initializeSampleData();

    // التحقق من تسجيل الدخول
    const user = localStorageManager.getCurrentUser();
    if (!user) {
      window.location.href = '/login';
      return;
    }
    setCurrentUser(user);

    // تحميل اسم المدرسة والشعار من الإعدادات
    const settings = localStorageManager.getSettings();
    if (settings) {
      if (settings.schoolName) {
        setSchoolName(settings.schoolName);
      }
      if (settings.schoolLogo) {
        setSchoolLogo(settings.schoolLogo);
      }
    }

    loadDashboardData();
  }, []);



  // الاستماع لتحديثات الإعدادات
  useEffect(() => {
    const handleSettingsUpdate = (event: CustomEvent) => {
      const newSettings = event.detail;
      if (newSettings) {
        if (newSettings.schoolName) {
          setSchoolName(newSettings.schoolName);
        }
        if (newSettings.schoolLogo) {
          setSchoolLogo(newSettings.schoolLogo);
        } else if (newSettings.schoolLogo === undefined) {
          setSchoolLogo(null);
        }
      }
    };

    window.addEventListener('settingsUpdated', handleSettingsUpdate as EventListener);

    return () => {
      window.removeEventListener('settingsUpdated', handleSettingsUpdate as EventListener);
    };
  }, []);

  const loadDashboardData = () => {
    try {
      const students = localStorageManager.getStudents();
      const teachers = localStorageManager.getTeachers();
      const employees = localStorageManager.getEmployees();
      const subjects = localStorageManager.getSubjects();
      const grades = localStorageManager.getGrades();
      const attendance = localStorageManager.getAttendance();

      // حساب الحضور اليوم
      const today = new Date();
      const todayAttendance = attendance.filter(a =>
        new Date(a.date).toDateString() === today.toDateString()
      );

      const presentToday = todayAttendance.filter(a => a.status === 'present').length;
      const absentToday = todayAttendance.filter(a => a.status === 'absent').length;

      // حساب معدل الحضور
      const totalAttendanceRecords = attendance.length;
      const presentRecords = attendance.filter(a => a.status === 'present').length;
      const attendanceRate = totalAttendanceRecords > 0 ?
        Math.round((presentRecords / totalAttendanceRecords) * 100) : 0;

      // أحدث الدرجات
      const recentGrades = grades
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5);

      // أفضل الطلاب (حسب متوسط الدرجات)
      const studentGrades = students.map(student => {
        const studentGradesList = grades.filter(g => g.studentId === student.id);
        const average = studentGradesList.length > 0 ?
          studentGradesList.reduce((sum, g) => sum + g.percentage, 0) / studentGradesList.length : 0;
        return { ...student, average };
      });

      const topPerformers = studentGrades
        .filter(s => s.average > 0)
        .sort((a, b) => b.average - a.average)
        .slice(0, 5);

      setStats({
        totalStudents: students.length,
        totalTeachers: teachers.length,
        totalEmployees: employees.length,
        totalSubjects: subjects.length,
        presentToday,
        absentToday,
        upcomingExams: 0, // يمكن تحديثه لاحقاً
        recentGrades,
        attendanceRate,
        topPerformers
      });

      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* ترحيب */}
        <div className="text-center mb-16 relative">
          {/* شعار المدرسة على اليمين - مكبر وبارز */}
          {schoolLogo && (
            <img
              src={schoolLogo}
              alt={`شعار ${schoolName}`}
              className="absolute right-0 top-0 w-24 h-24 object-contain rounded-xl shadow-lg bg-white p-2 border-2 border-gray-100"
            />
          )}

          <h1 className="text-3xl font-bold text-gray-900 mb-1">
            مرحباً بك {currentUser?.name || 'المستخدم'} في {schoolName}
          </h1>

          <p className="text-gray-600">
            لوحة التحكم الرئيسية - نظرة عامة على أداء المدرسة
          </p>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card
            className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
            hoverable
            onClick={() => window.location.href = '/students'}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">إجمالي الطلاب</p>
                <p className="text-2xl font-bold text-blue-900">{stats.totalStudents}</p>
              </div>
              <div className="text-3xl">👨‍🎓</div>
            </div>
          </Card>

          <Card
            className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
            hoverable
            onClick={() => window.location.href = '/teachers'}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">إجمالي المعلمين</p>
                <p className="text-2xl font-bold text-green-900">{stats.totalTeachers}</p>
              </div>
              <div className="text-3xl">👨‍🏫</div>
            </div>
          </Card>

          <Card
            className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
            hoverable
            onClick={() => window.location.href = '/employees'}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">إجمالي الموظفين</p>
                <p className="text-2xl font-bold text-purple-900">{stats.totalEmployees}</p>
              </div>
              <div className="text-3xl">👨‍💼</div>
            </div>
          </Card>
        </div>



        {/* الأقسام الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card hoverable className="text-center" onClick={() => window.location.href = '/students'}>
            <div className="text-4xl mb-4">👨‍🎓</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة الطلاب</h3>
            <p className="text-gray-600 mb-4">إضافة وتعديل وإدارة بيانات الطلاب</p>
            <Button variant="primary" fullWidth>
              إدارة الطلاب
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={() => window.location.href = '/teachers'}>
            <div className="text-4xl mb-4">👨‍🏫</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة المعلمين</h3>
            <p className="text-gray-600 mb-4">إضافة وتعديل وإدارة بيانات المعلمين</p>
            <Button variant="success" fullWidth>
              إدارة المعلمين
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={() => window.location.href = '/employees'}>
            <div className="text-4xl mb-4">👨‍💼</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة الموظفين</h3>
            <p className="text-gray-600 mb-4">إدارة وتتبع بيانات الموظفين الإداريين</p>
            <Button variant="info" fullWidth>
              إدارة الموظفين
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={() => window.location.href = '/grades'}>
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة الدرجات</h3>
            <p className="text-gray-600 mb-4">تسجيل ومتابعة درجات الطلاب</p>
            <Button variant="info" fullWidth>
              إدارة الدرجات
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={() => window.location.href = '/users'}>
            <div className="text-4xl mb-4">👥</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة المستخدمين</h3>
            <p className="text-gray-600 mb-4">إنشاء وإدارة حسابات المستخدمين</p>
            <Button variant="warning" fullWidth>
              إدارة المستخدمين
            </Button>
          </Card>

          <Card hoverable className="text-center" onClick={() => window.location.href = '/reports'}>
            <div className="text-4xl mb-4">📈</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">التقارير</h3>
            <p className="text-gray-600 mb-4">إنشاء وعرض التقارير المختلفة</p>
            <Button variant="secondary" fullWidth>
              عرض التقارير
            </Button>
          </Card>
        </div>


      </div>
    </Layout>
  );
}
