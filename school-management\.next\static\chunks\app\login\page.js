/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/login/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(app-pages-browser)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1QyVEOSU4NiVEOCVCOCVEOCVBNyVEOSU4NSUyMCVEOSU4NSVEOCVBRiVEOCVCMSVEOCVCMyVEOCVBOSU1QyU1Q3NjaG9vbC1tYW5hZ2VtZW50JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Button */ \"(app-pages-browser)/./src/components/Button.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _utils_sampleData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/sampleData */ \"(app-pages-browser)/./src/utils/sampleData.ts\");\n/* harmony import */ var _utils_appInfo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/appInfo */ \"(app-pages-browser)/./src/utils/appInfo.ts\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/localStorage */ \"(app-pages-browser)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // إزالة وضع إنشاء الحسابات - متاح للمطور فقط من صفحة إدارة المستخدمين\n    const [isDeveloperMode, setIsDeveloperMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // حقول إنشاء الحساب الجديد\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, loading, isAuthenticated } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // معلومات المطور والتطبيق\n    const developerInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_6__.getDeveloperInfo)();\n    const appInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_6__.getAppInfo)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            // تحميل البيانات التجريبية إذا لم تكن موجودة\n            (0,_utils_sampleData__WEBPACK_IMPORTED_MODULE_5__.initializeSampleData)();\n            // التحقق من وجود مستخدم مسجل دخول مسبقاً\n            if (isAuthenticated) {\n                router.push('/');\n            }\n            // التحقق من وضع المطور من URL\n            const urlParams = new URLSearchParams(window.location.search);\n            if (urlParams.get('developer') === 'true') {\n                setIsDeveloperMode(true);\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        isAuthenticated,\n        router\n    ]);\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setError('');\n        const result = await login(username, password);\n        if (result.success) {\n            // حفظ حالة \"تذكرني\" إذا كانت مفعلة\n            if (rememberMe) {\n                localStorage.setItem('rememberLogin', 'true');\n            }\n            // التحقق من آخر تسجيل دخول\n            const lastLogin = localStorage.getItem('lastLogin');\n            const isFirstLogin = !lastLogin;\n            // حفظ وقت تسجيل الدخول الحالي\n            localStorage.setItem('lastLogin', new Date().toISOString());\n            // الانتقال إلى صفحة الترحيب للمستخدمين الجدد أو الصفحة الرئيسية\n            if (isFirstLogin) {\n                router.push('/welcome');\n            } else {\n                router.push('/');\n            }\n        } else {\n            setError(result.error || 'حدث خطأ أثناء تسجيل الدخول');\n        }\n    };\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setError('');\n        // التحقق من صحة البيانات\n        if (!fullName.trim()) {\n            setError('يرجى إدخال الاسم الكامل');\n            return;\n        }\n        if (!email.trim()) {\n            setError('يرجى إدخال البريد الإلكتروني');\n            return;\n        }\n        if (!username.trim()) {\n            setError('يرجى إدخال اسم المستخدم');\n            return;\n        }\n        if (password.length < 4) {\n            setError('كلمة المرور يجب أن تكون 4 أحرف على الأقل');\n            return;\n        }\n        if (password !== confirmPassword) {\n            setError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');\n            return;\n        }\n        // التحقق من عدم وجود المستخدم مسبقاً\n        const existingUsers = _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.getUsers();\n        const userExists = existingUsers.some((u)=>u.email === email || u.name === username);\n        if (userExists) {\n            setError('المستخدم موجود بالفعل، يرجى استخدام بريد إلكتروني أو اسم مستخدم مختلف');\n            return;\n        }\n        try {\n            // إنشاء المستخدم الجديد\n            const newUser = {\n                id: \"user-\".concat(Date.now()),\n                name: fullName,\n                email: email,\n                password: password,\n                role: 'student',\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            // حفظ المستخدم الجديد\n            const updatedUsers = [\n                ...existingUsers,\n                newUser\n            ];\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.saveUsers(updatedUsers);\n            // تسجيل دخول تلقائي\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.setCurrentUser(newUser);\n            // إضافة إشعار ترحيب\n            const notification = {\n                id: \"notification-\".concat(Date.now()),\n                title: 'مرحباً بك في النظام',\n                message: \"تم إنشاء حسابك بنجاح، مرحباً \".concat(fullName),\n                type: 'success',\n                recipientId: newUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.addNotification(notification);\n            // الانتقال إلى صفحة الترحيب\n            router.push('/welcome');\n        } catch (error) {\n            console.error('خطأ في إنشاء الحساب:', error);\n            setError('حدث خطأ أثناء إنشاء الحساب');\n        }\n    };\n    const resetForm = ()=>{\n        setUsername('');\n        setPassword('');\n        setFullName('');\n        setEmail('');\n        setConfirmPassword('');\n        setError('');\n        setRememberMe(false);\n    };\n    // تم إزالة وضع إنشاء الحسابات\n    // const demoAccounts = [\n    //   { username: 'admin', role: 'مدير النظام', name: 'عبيدة العيثاوي' },\n    //   { username: 'teacher', role: 'معلمة', name: 'فاطمة أحمد المعلمة' },\n    //   { username: 'student', role: 'طالب', name: 'علي أحمد محمد' },\n    //   { username: 'parent', role: 'ولي أمر', name: 'أحمد محمد علي' }\n    // ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-20 animate-pulse delay-1000\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-br from-green-400 to-blue-600 rounded-full opacity-10 animate-pulse delay-500\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 min-h-[600px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 p-8 lg:p-12 flex flex-col justify-center items-center text-white relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-10 right-10 w-32 h-32 border-2 border-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-10 left-10 w-24 h-24 border-2 border-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-4xl\",\n                                                        children: \"\\uD83C\\uDFEB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl lg:text-4xl font-bold mb-4\",\n                                                    children: \"نظام إدارة المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg lg:text-xl mb-6 text-white/90\",\n                                                    children: \"نظام شامل لإدارة المدارس باللغة العربية\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 text-white/80 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: \"✨\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"إدارة متكاملة للطلاب والمعلمين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: \"\\uD83D\\uDCCA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"تقارير مفصلة ومتقدمة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: \"\\uD83D\\uDD12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"أمان وحماية عالية للبيانات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center ml-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white text-xl\",\n                                                                            children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 232,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-white font-bold text-sm\",\n                                                                                children: appInfo.contact.developer\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 235,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-xs\",\n                                                                                children: \"مطور النظام\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 236,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-left\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white/80 text-xs ml-2\",\n                                                                                children: \"\\uD83D\\uDCF1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white text-xs font-medium\",\n                                                                                children: appInfo.contact.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white/80 text-xs ml-2\",\n                                                                                children: \"✈️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white/80 text-xs\",\n                                                                                children: \"@ob992\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 248,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 lg:p-12 flex flex-col justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md mx-auto w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                        children: \"مرحباً بك\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"يرجى تسجيل الدخول للمتابعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleLogin,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"البريد الإلكتروني أو اسم المستخدم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: username,\n                                                                        onChange: (e)=>setUsername(e.target.value),\n                                                                        className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                                        placeholder: \"أدخل البريد الإلكتروني أو اسم المستخدم\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"كلمة المرور\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: showPassword ? 'text' : 'password',\n                                                                        value: password,\n                                                                        onChange: (e)=>setPassword(e.target.value),\n                                                                        className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                                        placeholder: \"أدخل كلمة المرور\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"hover:text-gray-600 transition-colors\",\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-5 h-5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 317,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-5 h-5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 322,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 323,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: rememberMe,\n                                                                        onChange: (e)=>setRememberMe(e.target.checked),\n                                                                        className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2 text-sm text-gray-600\",\n                                                                        children: \"تذكرني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm text-blue-600 hover:text-blue-800 transition-colors\",\n                                                                children: \"نسيت كلمة المرور؟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-red-400 ml-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-red-700\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        type: \"submit\",\n                                                        variant: \"primary\",\n                                                        loading: loading,\n                                                        fullWidth: true,\n                                                        className: \"h-12 text-lg font-semibold\",\n                                                        children: loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-800 mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"للحصول على حساب جديد:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-700\",\n                                                            children: \"يرجى التواصل مع المطور لإنشاء حساب جديد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-600 mt-2\",\n                                                            children: [\n                                                                \"\\uD83D\\uDCF1 \",\n                                                                appInfo.contact.phone,\n                                                                \" | ✈️ @ob992\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/reset-data\",\n                            className: \"text-sm text-red-600 hover:text-red-800 underline\",\n                            children: \"نسيت بيانات تسجيل الدخول؟ إعادة تعيين البيانات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"جميع الحقوق محفوظة \\xa9 2025 - نظام إدارة المدرسة\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"KPcTGMRO/6/4k6SVJIXxixw1OLQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Button = (param)=>{\n    let { children, onClick, type = 'button', variant = 'primary', size = 'md', disabled = false, loading = false, fullWidth = false, className = '', icon, iconPosition = 'right', title } = param;\n    const baseClasses = \"\\n    inline-flex items-center justify-center font-medium rounded-lg\\n    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\\n    disabled:opacity-50 disabled:cursor-not-allowed\\n    \".concat(fullWidth ? 'w-full' : '', \"\\n  \");\n    const sizeClasses = {\n        sm: 'px-3 py-2 text-sm',\n        md: 'px-4 py-2.5 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    const variantClasses = {\n        primary: \"\\n      bg-gradient-to-r from-blue-500 to-purple-600 text-white\\n      hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500\\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\\n    \",\n        secondary: \"\\n      bg-gray-100 text-gray-700 border border-gray-300\\n      hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500\\n      shadow-md hover:shadow-lg\\n    \",\n        success: \"\\n      bg-gradient-to-r from-green-500 to-emerald-600 text-white\\n      hover:from-green-600 hover:to-emerald-700 focus:ring-green-500\\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\\n    \",\n        danger: \"\\n      bg-gradient-to-r from-red-500 to-pink-600 text-white\\n      hover:from-red-600 hover:to-pink-700 focus:ring-red-500\\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\\n    \",\n        warning: \"\\n      bg-gradient-to-r from-yellow-500 to-orange-600 text-white\\n      hover:from-yellow-600 hover:to-orange-700 focus:ring-yellow-500\\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\\n    \",\n        info: \"\\n      bg-gradient-to-r from-cyan-500 to-blue-600 text-white\\n      hover:from-cyan-600 hover:to-blue-700 focus:ring-cyan-500\\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\\n    \"\n    };\n    const combinedClasses = \"\\n    \".concat(baseClasses, \"\\n    \").concat(sizeClasses[size], \"\\n    \").concat(variantClasses[variant], \"\\n    \").concat(className, \"\\n  \");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled || loading,\n        className: combinedClasses,\n        title: title,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-current\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            icon && iconPosition === 'right' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            icon && iconPosition === 'left' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Button;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\nvar _c;\n$RefreshReg$(_c, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/localStorage */ \"(app-pages-browser)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"useAuth.useEffect\"], []);\n    const checkAuth = ()=>{\n        try {\n            const currentUser = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getCurrentUser();\n            setUser(currentUser);\n        } catch (error) {\n            console.error('خطأ في التحقق من المصادقة:', error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (emailOrUsername, password)=>{\n        try {\n            setLoading(true);\n            // محاكاة تأخير الشبكة\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const foundUser = users.find((u)=>(u.email === emailOrUsername || u.username === emailOrUsername) && u.password === password);\n            if (!foundUser) {\n                return {\n                    success: false,\n                    error: 'البريد الإلكتروني/اسم المستخدم أو كلمة المرور غير صحيحة'\n                };\n            }\n            // تسجيل الدخول بنجاح\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(foundUser);\n            setUser(foundUser);\n            // إضافة إشعار نجاح تسجيل الدخول\n            const notification = {\n                id: \"notification-\".concat(Date.now()),\n                title: 'تم تسجيل الدخول بنجاح',\n                message: \"مرحباً \".concat(foundUser.name, \"، تم تسجيل دخولك بنجاح\"),\n                type: 'success',\n                recipientId: foundUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: 'حدث خطأ أثناء تسجيل الدخول'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        try {\n            // إضافة إشعار تسجيل الخروج\n            if (user) {\n                const notification = {\n                    id: \"notification-\".concat(Date.now()),\n                    title: 'تم تسجيل الخروج',\n                    message: \"تم تسجيل خروجك من النظام بنجاح\",\n                    type: 'info',\n                    recipientId: user.id,\n                    recipientType: 'user',\n                    isRead: false,\n                    createdAt: new Date()\n                };\n                _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            }\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.logout();\n            setUser(null);\n            router.push('/login');\n        } catch (error) {\n            console.error('خطأ في تسجيل الخروج:', error);\n        }\n    };\n    const hasRole = (roles)=>{\n        if (!user) return false;\n        return roles.includes(user.role);\n    };\n    const updatePassword = async (currentPassword, newPassword)=>{\n        try {\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'المستخدم غير مسجل الدخول'\n                };\n            }\n            // التحقق من كلمة المرور الحالية\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const currentUser = users.find((u)=>u.id === user.id);\n            if (!currentUser || currentUser.password !== currentPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الحالية غير صحيحة'\n                };\n            }\n            // التحقق من قوة كلمة المرور الجديدة\n            if (newPassword.length < 3) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة قصيرة جداً (الحد الأدنى 3 أحرف)'\n                };\n            }\n            if (currentPassword === newPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية'\n                };\n            }\n            // تحديث كلمة المرور\n            const updatedUsers = users.map((u)=>u.id === user.id ? {\n                    ...u,\n                    password: newPassword,\n                    updatedAt: new Date()\n                } : u);\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.saveUsers(updatedUsers);\n            // تحديث المستخدم الحالي\n            const updatedUser = {\n                ...user,\n                password: newPassword,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(updatedUser);\n            setUser(updatedUser);\n            // إضافة إشعار نجاح تغيير كلمة المرور\n            const notification = {\n                id: \"notification-\".concat(Date.now()),\n                title: 'تم تغيير كلمة المرور',\n                message: 'تم تغيير كلمة المرور بنجاح',\n                type: 'success',\n                recipientId: user.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تحديث كلمة المرور:', error);\n            return {\n                success: false,\n                error: 'فشل في تحديث كلمة المرور'\n            };\n        }\n    };\n    return {\n        user,\n        loading,\n        isAuthenticated: !!user,\n        login,\n        logout,\n        checkAuth,\n        hasRole,\n        updatePassword\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUU0QztBQUNBO0FBRWU7QUFlcEQsTUFBTUksVUFBVTtJQUNyQixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR04sK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDTyxTQUFTQyxXQUFXLEdBQUdSLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU1TLFNBQVNQLDBEQUFTQTtJQUV4QkQsZ0RBQVNBOzZCQUFDO1lBQ1JTO1FBQ0Y7NEJBQUcsRUFBRTtJQUVMLE1BQU1BLFlBQVk7UUFDaEIsSUFBSTtZQUNGLE1BQU1DLGNBQWNSLG9FQUFtQkEsQ0FBQ1MsY0FBYztZQUN0RE4sUUFBUUs7UUFDVixFQUFFLE9BQU9FLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNQLFFBQVE7UUFDVixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTU8sUUFBUSxPQUFPQyxpQkFBeUJDO1FBQzVDLElBQUk7WUFDRlQsV0FBVztZQUVYLHNCQUFzQjtZQUN0QixNQUFNLElBQUlVLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7WUFFakQsdURBQXVEO1lBQ3ZELE1BQU1FLFFBQVFsQixvRUFBbUJBLENBQUNtQixRQUFRO1lBQzFDLE1BQU1DLFlBQVlGLE1BQU1HLElBQUksQ0FBQ0MsQ0FBQUEsSUFDM0IsQ0FBQ0EsRUFBRUMsS0FBSyxLQUFLVixtQkFBbUJTLEVBQUVFLFFBQVEsS0FBS1gsZUFBYyxLQUM3RFMsRUFBRVIsUUFBUSxLQUFLQTtZQUdqQixJQUFJLENBQUNNLFdBQVc7Z0JBQ2QsT0FBTztvQkFBRUssU0FBUztvQkFBT2YsT0FBTztnQkFBMEQ7WUFDNUY7WUFFQSxxQkFBcUI7WUFDckJWLG9FQUFtQkEsQ0FBQzBCLGNBQWMsQ0FBQ047WUFDbkNqQixRQUFRaUI7WUFFUixnQ0FBZ0M7WUFDaEMsTUFBTU8sZUFBZTtnQkFDbkJDLElBQUksZ0JBQTJCLE9BQVhDLEtBQUtDLEdBQUc7Z0JBQzVCQyxPQUFPO2dCQUNQQyxTQUFTLFVBQXlCLE9BQWZaLFVBQVVhLElBQUksRUFBQztnQkFDbENDLE1BQU07Z0JBQ05DLGFBQWFmLFVBQVVRLEVBQUU7Z0JBQ3pCUSxlQUFlO2dCQUNmQyxRQUFRO2dCQUNSQyxXQUFXLElBQUlUO1lBQ2pCO1lBQ0E3QixvRUFBbUJBLENBQUN1QyxlQUFlLENBQUNaO1lBRXBDLE9BQU87Z0JBQUVGLFNBQVM7WUFBSztRQUN6QixFQUFFLE9BQU9mLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsT0FBTztnQkFBRWUsU0FBUztnQkFBT2YsT0FBTztZQUE2QjtRQUMvRCxTQUFVO1lBQ1JMLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTW1DLFNBQVM7UUFDYixJQUFJO1lBQ0YsMkJBQTJCO1lBQzNCLElBQUl0QyxNQUFNO2dCQUNSLE1BQU15QixlQUFlO29CQUNuQkMsSUFBSSxnQkFBMkIsT0FBWEMsS0FBS0MsR0FBRztvQkFDNUJDLE9BQU87b0JBQ1BDLFNBQVU7b0JBQ1ZFLE1BQU07b0JBQ05DLGFBQWFqQyxLQUFLMEIsRUFBRTtvQkFDcEJRLGVBQWU7b0JBQ2ZDLFFBQVE7b0JBQ1JDLFdBQVcsSUFBSVQ7Z0JBQ2pCO2dCQUNBN0Isb0VBQW1CQSxDQUFDdUMsZUFBZSxDQUFDWjtZQUN0QztZQUVBM0Isb0VBQW1CQSxDQUFDd0MsTUFBTTtZQUMxQnJDLFFBQVE7WUFDUkcsT0FBT21DLElBQUksQ0FBQztRQUNkLEVBQUUsT0FBTy9CLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7UUFDeEM7SUFDRjtJQUVBLE1BQU1nQyxVQUFVLENBQUNDO1FBQ2YsSUFBSSxDQUFDekMsTUFBTSxPQUFPO1FBQ2xCLE9BQU95QyxNQUFNQyxRQUFRLENBQUMxQyxLQUFLMkMsSUFBSTtJQUNqQztJQUVBLE1BQU1DLGlCQUFpQixPQUFPQyxpQkFBeUJDO1FBQ3JELElBQUk7WUFDRixJQUFJLENBQUM5QyxNQUFNO2dCQUNULE9BQU87b0JBQUV1QixTQUFTO29CQUFPZixPQUFPO2dCQUEyQjtZQUM3RDtZQUVBLGdDQUFnQztZQUNoQyxNQUFNUSxRQUFRbEIsb0VBQW1CQSxDQUFDbUIsUUFBUTtZQUMxQyxNQUFNWCxjQUFjVSxNQUFNRyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVNLEVBQUUsS0FBSzFCLEtBQUswQixFQUFFO1lBRXBELElBQUksQ0FBQ3BCLGVBQWVBLFlBQVlNLFFBQVEsS0FBS2lDLGlCQUFpQjtnQkFDNUQsT0FBTztvQkFBRXRCLFNBQVM7b0JBQU9mLE9BQU87Z0JBQWdDO1lBQ2xFO1lBRUEsb0NBQW9DO1lBQ3BDLElBQUlzQyxZQUFZQyxNQUFNLEdBQUcsR0FBRztnQkFDMUIsT0FBTztvQkFBRXhCLFNBQVM7b0JBQU9mLE9BQU87Z0JBQXNEO1lBQ3hGO1lBRUEsSUFBSXFDLG9CQUFvQkMsYUFBYTtnQkFDbkMsT0FBTztvQkFBRXZCLFNBQVM7b0JBQU9mLE9BQU87Z0JBQW9EO1lBQ3RGO1lBRUEsb0JBQW9CO1lBQ3BCLE1BQU13QyxlQUFlaEMsTUFBTWlDLEdBQUcsQ0FBQzdCLENBQUFBLElBQzdCQSxFQUFFTSxFQUFFLEtBQUsxQixLQUFLMEIsRUFBRSxHQUNaO29CQUFFLEdBQUdOLENBQUM7b0JBQUVSLFVBQVVrQztvQkFBYUksV0FBVyxJQUFJdkI7Z0JBQU8sSUFDckRQO1lBR050QixvRUFBbUJBLENBQUNxRCxTQUFTLENBQUNIO1lBRTlCLHdCQUF3QjtZQUN4QixNQUFNSSxjQUFjO2dCQUFFLEdBQUdwRCxJQUFJO2dCQUFFWSxVQUFVa0M7Z0JBQWFJLFdBQVcsSUFBSXZCO1lBQU87WUFDNUU3QixvRUFBbUJBLENBQUMwQixjQUFjLENBQUM0QjtZQUNuQ25ELFFBQVFtRDtZQUVSLHFDQUFxQztZQUNyQyxNQUFNM0IsZUFBZTtnQkFDbkJDLElBQUksZ0JBQTJCLE9BQVhDLEtBQUtDLEdBQUc7Z0JBQzVCQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNURSxNQUFNO2dCQUNOQyxhQUFhakMsS0FBSzBCLEVBQUU7Z0JBQ3BCUSxlQUFlO2dCQUNmQyxRQUFRO2dCQUNSQyxXQUFXLElBQUlUO1lBQ2pCO1lBQ0E3QixvRUFBbUJBLENBQUN1QyxlQUFlLENBQUNaO1lBRXBDLE9BQU87Z0JBQUVGLFNBQVM7WUFBSztRQUN6QixFQUFFLE9BQU9mLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsT0FBTztnQkFBRWUsU0FBUztnQkFBT2YsT0FBTztZQUEyQjtRQUM3RDtJQUNGO0lBRUEsT0FBTztRQUNMUjtRQUNBRTtRQUNBbUQsaUJBQWlCLENBQUMsQ0FBQ3JEO1FBQ25CVTtRQUNBNEI7UUFDQWpDO1FBQ0FtQztRQUNBSTtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxzcmNcXGhvb2tzXFx1c2VBdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBVc2VyIH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyBsb2NhbFN0b3JhZ2VNYW5hZ2VyIH0gZnJvbSAnQC91dGlscy9sb2NhbFN0b3JhZ2UnO1xuXG5pbnRlcmZhY2UgQXV0aFN0YXRlIHtcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbn1cblxuaW50ZXJmYWNlIEF1dGhBY3Rpb25zIHtcbiAgbG9naW46ICh1c2VybmFtZTogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT47XG4gIGxvZ291dDogKCkgPT4gdm9pZDtcbiAgY2hlY2tBdXRoOiAoKSA9PiB2b2lkO1xuICBoYXNSb2xlOiAocm9sZXM6IHN0cmluZ1tdKSA9PiBib29sZWFuO1xufVxuXG5leHBvcnQgY29uc3QgdXNlQXV0aCA9ICgpOiBBdXRoU3RhdGUgJiBBdXRoQWN0aW9ucyA9PiB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2hlY2tBdXRoKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBjaGVja0F1dGggPSAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGN1cnJlbnRVc2VyID0gbG9jYWxTdG9yYWdlTWFuYWdlci5nZXRDdXJyZW50VXNlcigpO1xuICAgICAgc2V0VXNlcihjdXJyZW50VXNlcik7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINin2YTYqtit2YLZgiDZhdmGINin2YTZhdi12KfYr9mC2Kk6JywgZXJyb3IpO1xuICAgICAgc2V0VXNlcihudWxsKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKGVtYWlsT3JVc2VybmFtZTogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+ID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcblxuICAgICAgLy8g2YXYrdin2YPYp9ipINiq2KPYrtmK2LEg2KfZhNi02KjZg9ipXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuXG4gICAgICAvLyDYp9mE2KjYrdirINi52YYg2KfZhNmF2LPYqtiu2K/ZhSDYqNin2YTYqNix2YrYryDYp9mE2KXZhNmD2KrYsdmI2YbZiiDYo9mIINin2LPZhSDYp9mE2YXYs9iq2K7Yr9mFXG4gICAgICBjb25zdCB1c2VycyA9IGxvY2FsU3RvcmFnZU1hbmFnZXIuZ2V0VXNlcnMoKTtcbiAgICAgIGNvbnN0IGZvdW5kVXNlciA9IHVzZXJzLmZpbmQodSA9PlxuICAgICAgICAodS5lbWFpbCA9PT0gZW1haWxPclVzZXJuYW1lIHx8IHUudXNlcm5hbWUgPT09IGVtYWlsT3JVc2VybmFtZSkgJiZcbiAgICAgICAgdS5wYXNzd29yZCA9PT0gcGFzc3dvcmRcbiAgICAgICk7XG5cbiAgICAgIGlmICghZm91bmRVc2VyKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ9in2YTYqNix2YrYryDYp9mE2KXZhNmD2KrYsdmI2YbZii/Yp9iz2YUg2KfZhNmF2LPYqtiu2K/ZhSDYo9mIINmD2YTZhdipINin2YTZhdix2YjYsSDYutmK2LEg2LXYrdmK2K3YqScgfTtcbiAgICAgIH1cblxuICAgICAgLy8g2KrYs9is2YrZhCDYp9mE2K/YrtmI2YQg2KjZhtis2KfYrVxuICAgICAgbG9jYWxTdG9yYWdlTWFuYWdlci5zZXRDdXJyZW50VXNlcihmb3VuZFVzZXIpO1xuICAgICAgc2V0VXNlcihmb3VuZFVzZXIpO1xuXG4gICAgICAvLyDYpdi22KfZgdipINil2LTYudin2LEg2YbYrNin2K0g2KrYs9is2YrZhCDYp9mE2K/YrtmI2YRcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IHtcbiAgICAgICAgaWQ6IGBub3RpZmljYXRpb24tJHtEYXRlLm5vdygpfWAsXG4gICAgICAgIHRpdGxlOiAn2KrZhSDYqtiz2KzZitmEINin2YTYr9iu2YjZhCDYqNmG2KzYp9itJyxcbiAgICAgICAgbWVzc2FnZTogYNmF2LHYrdio2KfZiyAke2ZvdW5kVXNlci5uYW1lfdiMINiq2YUg2KrYs9is2YrZhCDYr9iu2YjZhNmDINio2YbYrNin2K1gLFxuICAgICAgICB0eXBlOiAnc3VjY2VzcycgYXMgY29uc3QsXG4gICAgICAgIHJlY2lwaWVudElkOiBmb3VuZFVzZXIuaWQsXG4gICAgICAgIHJlY2lwaWVudFR5cGU6ICd1c2VyJyBhcyBjb25zdCxcbiAgICAgICAgaXNSZWFkOiBmYWxzZSxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICB9O1xuICAgICAgbG9jYWxTdG9yYWdlTWFuYWdlci5hZGROb3RpZmljYXRpb24obm90aWZpY2F0aW9uKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYqtiz2KzZitmEINin2YTYr9iu2YjZhDonLCBlcnJvcik7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KrYs9is2YrZhCDYp9mE2K/YrtmI2YQnIH07XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dvdXQgPSAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vINil2LbYp9mB2Kkg2KXYtNi52KfYsSDYqtiz2KzZitmEINin2YTYrtix2YjYrFxuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgY29uc3Qgbm90aWZpY2F0aW9uID0ge1xuICAgICAgICAgIGlkOiBgbm90aWZpY2F0aW9uLSR7RGF0ZS5ub3coKX1gLFxuICAgICAgICAgIHRpdGxlOiAn2KrZhSDYqtiz2KzZitmEINin2YTYrtix2YjYrCcsXG4gICAgICAgICAgbWVzc2FnZTogYNiq2YUg2KrYs9is2YrZhCDYrtix2YjYrNmDINmF2YYg2KfZhNmG2LjYp9mFINio2YbYrNin2K1gLFxuICAgICAgICAgIHR5cGU6ICdpbmZvJyBhcyBjb25zdCxcbiAgICAgICAgICByZWNpcGllbnRJZDogdXNlci5pZCxcbiAgICAgICAgICByZWNpcGllbnRUeXBlOiAndXNlcicgYXMgY29uc3QsXG4gICAgICAgICAgaXNSZWFkOiBmYWxzZSxcbiAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKClcbiAgICAgICAgfTtcbiAgICAgICAgbG9jYWxTdG9yYWdlTWFuYWdlci5hZGROb3RpZmljYXRpb24obm90aWZpY2F0aW9uKTtcbiAgICAgIH1cblxuICAgICAgbG9jYWxTdG9yYWdlTWFuYWdlci5sb2dvdXQoKTtcbiAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINiq2LPYrNmK2YQg2KfZhNiu2LHZiNisOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFzUm9sZSA9IChyb2xlczogc3RyaW5nW10pOiBib29sZWFuID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybiBmYWxzZTtcbiAgICByZXR1cm4gcm9sZXMuaW5jbHVkZXModXNlci5yb2xlKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVQYXNzd29yZCA9IGFzeW5jIChjdXJyZW50UGFzc3dvcmQ6IHN0cmluZywgbmV3UGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICfYp9mE2YXYs9iq2K7Yr9mFINi62YrYsSDZhdiz2KzZhCDYp9mE2K/YrtmI2YQnIH07XG4gICAgICB9XG5cbiAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINmD2YTZhdipINin2YTZhdix2YjYsSDYp9mE2K3Yp9mE2YrYqVxuICAgICAgY29uc3QgdXNlcnMgPSBsb2NhbFN0b3JhZ2VNYW5hZ2VyLmdldFVzZXJzKCk7XG4gICAgICBjb25zdCBjdXJyZW50VXNlciA9IHVzZXJzLmZpbmQodSA9PiB1LmlkID09PSB1c2VyLmlkKTtcblxuICAgICAgaWYgKCFjdXJyZW50VXNlciB8fCBjdXJyZW50VXNlci5wYXNzd29yZCAhPT0gY3VycmVudFBhc3N3b3JkKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ9mD2YTZhdipINin2YTZhdix2YjYsSDYp9mE2K3Yp9mE2YrYqSDYutmK2LEg2LXYrdmK2K3YqScgfTtcbiAgICAgIH1cblxuICAgICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2YLZiNipINmD2YTZhdipINin2YTZhdix2YjYsSDYp9mE2KzYr9mK2K/YqVxuICAgICAgaWYgKG5ld1Bhc3N3b3JkLmxlbmd0aCA8IDMpIHtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAn2YPZhNmF2Kkg2KfZhNmF2LHZiNixINin2YTYrNiv2YrYr9ipINmC2LXZitix2Kkg2KzYr9in2YsgKNin2YTYrdivINin2YTYo9iv2YbZiSAzINij2K3YsdmBKScgfTtcbiAgICAgIH1cblxuICAgICAgaWYgKGN1cnJlbnRQYXNzd29yZCA9PT0gbmV3UGFzc3dvcmQpIHtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAn2YPZhNmF2Kkg2KfZhNmF2LHZiNixINin2YTYrNiv2YrYr9ipINmK2KzYqCDYo9mGINiq2YPZiNmGINmF2K7YqtmE2YHYqSDYudmGINin2YTYrdin2YTZitipJyB9O1xuICAgICAgfVxuXG4gICAgICAvLyDYqtit2K/ZitirINmD2YTZhdipINin2YTZhdix2YjYsVxuICAgICAgY29uc3QgdXBkYXRlZFVzZXJzID0gdXNlcnMubWFwKHUgPT5cbiAgICAgICAgdS5pZCA9PT0gdXNlci5pZFxuICAgICAgICAgID8geyAuLi51LCBwYXNzd29yZDogbmV3UGFzc3dvcmQsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9XG4gICAgICAgICAgOiB1XG4gICAgICApO1xuXG4gICAgICBsb2NhbFN0b3JhZ2VNYW5hZ2VyLnNhdmVVc2Vycyh1cGRhdGVkVXNlcnMpO1xuXG4gICAgICAvLyDYqtit2K/ZitirINin2YTZhdiz2KrYrtiv2YUg2KfZhNit2KfZhNmKXG4gICAgICBjb25zdCB1cGRhdGVkVXNlciA9IHsgLi4udXNlciwgcGFzc3dvcmQ6IG5ld1Bhc3N3b3JkLCB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkgfTtcbiAgICAgIGxvY2FsU3RvcmFnZU1hbmFnZXIuc2V0Q3VycmVudFVzZXIodXBkYXRlZFVzZXIpO1xuICAgICAgc2V0VXNlcih1cGRhdGVkVXNlcik7XG5cbiAgICAgIC8vINil2LbYp9mB2Kkg2KXYtNi52KfYsSDZhtis2KfYrSDYqti62YrZitixINmD2YTZhdipINin2YTZhdix2YjYsVxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uID0ge1xuICAgICAgICBpZDogYG5vdGlmaWNhdGlvbi0ke0RhdGUubm93KCl9YCxcbiAgICAgICAgdGl0bGU6ICfYqtmFINiq2LrZitmK2LEg2YPZhNmF2Kkg2KfZhNmF2LHZiNixJyxcbiAgICAgICAgbWVzc2FnZTogJ9iq2YUg2KrYutmK2YrYsSDZg9mE2YXYqSDYp9mE2YXYsdmI2LEg2KjZhtis2KfYrScsXG4gICAgICAgIHR5cGU6ICdzdWNjZXNzJyBhcyBjb25zdCxcbiAgICAgICAgcmVjaXBpZW50SWQ6IHVzZXIuaWQsXG4gICAgICAgIHJlY2lwaWVudFR5cGU6ICd1c2VyJyBhcyBjb25zdCxcbiAgICAgICAgaXNSZWFkOiBmYWxzZSxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICB9O1xuICAgICAgbG9jYWxTdG9yYWdlTWFuYWdlci5hZGROb3RpZmljYXRpb24obm90aWZpY2F0aW9uKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYqtit2K/ZitirINmD2YTZhdipINin2YTZhdix2YjYsTonLCBlcnJvcik7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICfZgdi02YQg2YHZiiDYqtit2K/ZitirINmD2YTZhdipINin2YTZhdix2YjYsScgfTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIHtcbiAgICB1c2VyLFxuICAgIGxvYWRpbmcsXG4gICAgaXNBdXRoZW50aWNhdGVkOiAhIXVzZXIsXG4gICAgbG9naW4sXG4gICAgbG9nb3V0LFxuICAgIGNoZWNrQXV0aCxcbiAgICBoYXNSb2xlLFxuICAgIHVwZGF0ZVBhc3N3b3JkXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwibG9jYWxTdG9yYWdlTWFuYWdlciIsInVzZUF1dGgiLCJ1c2VyIiwic2V0VXNlciIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicm91dGVyIiwiY2hlY2tBdXRoIiwiY3VycmVudFVzZXIiLCJnZXRDdXJyZW50VXNlciIsImVycm9yIiwiY29uc29sZSIsImxvZ2luIiwiZW1haWxPclVzZXJuYW1lIiwicGFzc3dvcmQiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJ1c2VycyIsImdldFVzZXJzIiwiZm91bmRVc2VyIiwiZmluZCIsInUiLCJlbWFpbCIsInVzZXJuYW1lIiwic3VjY2VzcyIsInNldEN1cnJlbnRVc2VyIiwibm90aWZpY2F0aW9uIiwiaWQiLCJEYXRlIiwibm93IiwidGl0bGUiLCJtZXNzYWdlIiwibmFtZSIsInR5cGUiLCJyZWNpcGllbnRJZCIsInJlY2lwaWVudFR5cGUiLCJpc1JlYWQiLCJjcmVhdGVkQXQiLCJhZGROb3RpZmljYXRpb24iLCJsb2dvdXQiLCJwdXNoIiwiaGFzUm9sZSIsInJvbGVzIiwiaW5jbHVkZXMiLCJyb2xlIiwidXBkYXRlUGFzc3dvcmQiLCJjdXJyZW50UGFzc3dvcmQiLCJuZXdQYXNzd29yZCIsImxlbmd0aCIsInVwZGF0ZWRVc2VycyIsIm1hcCIsInVwZGF0ZWRBdCIsInNhdmVVc2VycyIsInVwZGF0ZWRVc2VyIiwiaXNBdXRoZW50aWNhdGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/appInfo.ts":
/*!******************************!*\
  !*** ./src/utils/appInfo.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_INFO: () => (/* binding */ APP_INFO),\n/* harmony export */   getAppInfo: () => (/* binding */ getAppInfo),\n/* harmony export */   getDeveloperInfo: () => (/* binding */ getDeveloperInfo),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo)\n/* harmony export */ });\n// معلومات التطبيق والمطور\nconst APP_INFO = {\n    name: 'نظام إدارة المدرسة',\n    version: '1.0.0',\n    description: 'نظام شامل لإدارة المدارس باللغة العربية',\n    developer: 'عبيدة العيثاوي',\n    developedBy: 'تم التطوير بواسطة عبيدة العيثاوي',\n    copyright: \"\\xa9 \".concat(new Date().getFullYear(), \" عبيدة العيثاوي - جميع الحقوق محفوظة\"),\n    features: [\n        'إدارة الطلاب والمعلمين',\n        'إدارة الصفوف والمواد الدراسية',\n        'نظام التقييم والدرجات',\n        'تقارير شاملة ومفصلة',\n        'واجهة عربية متجاوبة',\n        'تصميم نيومورفيك عصري'\n    ],\n    technologies: [\n        'Next.js 15',\n        'React 19',\n        'TypeScript',\n        'Tailwind CSS',\n        'localStorage'\n    ],\n    contact: {\n        developer: 'عبيدة العيثاوي',\n        email: '<EMAIL>',\n        phone: '07813332882'\n    }\n};\n// دالة للحصول على معلومات التطبيق\nconst getAppInfo = ()=>APP_INFO;\n// دالة للحصول على معلومات المطور\nconst getDeveloperInfo = ()=>({\n        name: APP_INFO.developer,\n        developedBy: APP_INFO.developedBy,\n        copyright: APP_INFO.copyright,\n        contact: APP_INFO.contact\n    });\n// دالة للحصول على معلومات الإصدار\nconst getVersionInfo = ()=>({\n        version: APP_INFO.version,\n        name: APP_INFO.name,\n        description: APP_INFO.description\n    });\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/appInfo.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/localStorage.ts":
/*!***********************************!*\
  !*** ./src/utils/localStorage.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageManager: () => (/* binding */ localStorageManager),\n/* harmony export */   searchUtils: () => (/* binding */ searchUtils)\n/* harmony export */ });\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    STUDENTS: 'school_students',\n    TEACHERS: 'school_teachers',\n    EMPLOYEES: 'school_employees',\n    CLASSES: 'school_classes',\n    SUBJECTS: 'school_subjects',\n    GRADES: 'school_grades',\n    ATTENDANCE: 'school_attendance',\n    USERS: 'school_users',\n    SETTINGS: 'school_settings',\n    EVENTS: 'school_events',\n    NOTIFICATIONS: 'school_notifications',\n    CURRENT_USER: 'school_current_user',\n    ACADEMIC_YEAR: 'school_academic_year',\n    CURRENT_SEMESTER: 'school_current_semester'\n};\n// فئة إدارة التخزين المحلي\nclass LocalStorageManager {\n    // حفظ البيانات\n    setItem(key, data) {\n        try {\n            const serializedData = JSON.stringify(data);\n            localStorage.setItem(key, serializedData);\n        } catch (error) {\n            console.error(\"خطأ في حفظ البيانات للمفتاح \".concat(key, \":\"), error);\n        }\n    }\n    // استرجاع البيانات\n    getItem(key) {\n        try {\n            const serializedData = localStorage.getItem(key);\n            if (serializedData === null) {\n                return null;\n            }\n            return JSON.parse(serializedData);\n        } catch (error) {\n            console.error(\"خطأ في استرجاع البيانات للمفتاح \".concat(key, \":\"), error);\n            return null;\n        }\n    }\n    // حذف البيانات\n    removeItem(key) {\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(\"خطأ في حذف البيانات للمفتاح \".concat(key, \":\"), error);\n        }\n    }\n    // مسح جميع البيانات\n    clearAll() {\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            this.removeItem(key);\n        });\n    }\n    // === إدارة الطلاب ===\n    getStudents() {\n        return this.getItem(STORAGE_KEYS.STUDENTS) || [];\n    }\n    saveStudents(students) {\n        this.setItem(STORAGE_KEYS.STUDENTS, students);\n    }\n    addStudent(student) {\n        const students = this.getStudents();\n        students.push(student);\n        this.saveStudents(students);\n    }\n    updateStudent(studentId, updatedStudent) {\n        const students = this.getStudents();\n        const index = students.findIndex((s)=>s.id === studentId);\n        if (index !== -1) {\n            students[index] = {\n                ...students[index],\n                ...updatedStudent,\n                updatedAt: new Date()\n            };\n            this.saveStudents(students);\n        }\n    }\n    deleteStudent(studentId) {\n        const students = this.getStudents();\n        const filteredStudents = students.filter((s)=>s.id !== studentId);\n        this.saveStudents(filteredStudents);\n    }\n    getStudentById(studentId) {\n        const students = this.getStudents();\n        return students.find((s)=>s.id === studentId) || null;\n    }\n    // === إدارة المعلمين ===\n    getTeachers() {\n        return this.getItem(STORAGE_KEYS.TEACHERS) || [];\n    }\n    saveTeachers(teachers) {\n        this.setItem(STORAGE_KEYS.TEACHERS, teachers);\n    }\n    addTeacher(teacher) {\n        const teachers = this.getTeachers();\n        teachers.push(teacher);\n        this.saveTeachers(teachers);\n    }\n    updateTeacher(teacherId, updatedTeacher) {\n        const teachers = this.getTeachers();\n        const index = teachers.findIndex((t)=>t.id === teacherId);\n        if (index !== -1) {\n            teachers[index] = {\n                ...teachers[index],\n                ...updatedTeacher,\n                updatedAt: new Date()\n            };\n            this.saveTeachers(teachers);\n        }\n    }\n    deleteTeacher(teacherId) {\n        const teachers = this.getTeachers();\n        const filteredTeachers = teachers.filter((t)=>t.id !== teacherId);\n        this.saveTeachers(filteredTeachers);\n    }\n    getTeacherById(teacherId) {\n        const teachers = this.getTeachers();\n        return teachers.find((t)=>t.id === teacherId) || null;\n    }\n    // === إدارة الموظفين ===\n    getEmployees() {\n        return this.getItem(STORAGE_KEYS.EMPLOYEES) || [];\n    }\n    saveEmployees(employees) {\n        this.setItem(STORAGE_KEYS.EMPLOYEES, employees);\n    }\n    addEmployee(employee) {\n        const employees = this.getEmployees();\n        employees.push(employee);\n        this.saveEmployees(employees);\n    }\n    updateEmployee(employeeId, updatedEmployee) {\n        const employees = this.getEmployees();\n        const index = employees.findIndex((e)=>e.id === employeeId);\n        if (index !== -1) {\n            employees[index] = {\n                ...employees[index],\n                ...updatedEmployee,\n                updatedAt: new Date()\n            };\n            this.saveEmployees(employees);\n        }\n    }\n    deleteEmployee(employeeId) {\n        const employees = this.getEmployees();\n        const filteredEmployees = employees.filter((e)=>e.id !== employeeId);\n        this.saveEmployees(filteredEmployees);\n    }\n    getEmployeeById(employeeId) {\n        const employees = this.getEmployees();\n        return employees.find((e)=>e.id === employeeId) || null;\n    }\n    // === إدارة الصفوف ===\n    getClasses() {\n        return this.getItem(STORAGE_KEYS.CLASSES) || [];\n    }\n    saveClasses(classes) {\n        this.setItem(STORAGE_KEYS.CLASSES, classes);\n    }\n    addClass(classData) {\n        const classes = this.getClasses();\n        classes.push(classData);\n        this.saveClasses(classes);\n    }\n    updateClass(classId, updatedClass) {\n        const classes = this.getClasses();\n        const index = classes.findIndex((c)=>c.id === classId);\n        if (index !== -1) {\n            classes[index] = {\n                ...classes[index],\n                ...updatedClass,\n                updatedAt: new Date()\n            };\n            this.saveClasses(classes);\n        }\n    }\n    deleteClass(classId) {\n        const classes = this.getClasses();\n        const filteredClasses = classes.filter((c)=>c.id !== classId);\n        this.saveClasses(filteredClasses);\n    }\n    getClassById(classId) {\n        const classes = this.getClasses();\n        return classes.find((c)=>c.id === classId) || null;\n    }\n    // === إدارة المواد ===\n    getSubjects() {\n        return this.getItem(STORAGE_KEYS.SUBJECTS) || [];\n    }\n    saveSubjects(subjects) {\n        this.setItem(STORAGE_KEYS.SUBJECTS, subjects);\n    }\n    addSubject(subject) {\n        const subjects = this.getSubjects();\n        subjects.push(subject);\n        this.saveSubjects(subjects);\n    }\n    updateSubject(subjectId, updatedSubject) {\n        const subjects = this.getSubjects();\n        const index = subjects.findIndex((s)=>s.id === subjectId);\n        if (index !== -1) {\n            subjects[index] = {\n                ...subjects[index],\n                ...updatedSubject,\n                updatedAt: new Date()\n            };\n            this.saveSubjects(subjects);\n        }\n    }\n    deleteSubject(subjectId) {\n        const subjects = this.getSubjects();\n        const filteredSubjects = subjects.filter((s)=>s.id !== subjectId);\n        this.saveSubjects(filteredSubjects);\n    }\n    getSubjectById(subjectId) {\n        const subjects = this.getSubjects();\n        return subjects.find((s)=>s.id === subjectId) || null;\n    }\n    // === إدارة الدرجات ===\n    getGrades() {\n        return this.getItem(STORAGE_KEYS.GRADES) || [];\n    }\n    saveGrades(grades) {\n        this.setItem(STORAGE_KEYS.GRADES, grades);\n    }\n    addGrade(grade) {\n        const grades = this.getGrades();\n        grades.push(grade);\n        this.saveGrades(grades);\n    }\n    updateGrade(gradeId, updatedGrade) {\n        const grades = this.getGrades();\n        const index = grades.findIndex((g)=>g.id === gradeId);\n        if (index !== -1) {\n            grades[index] = {\n                ...grades[index],\n                ...updatedGrade,\n                updatedAt: new Date()\n            };\n            this.saveGrades(grades);\n        }\n    }\n    deleteGrade(gradeId) {\n        const grades = this.getGrades();\n        const filteredGrades = grades.filter((g)=>g.id !== gradeId);\n        this.saveGrades(filteredGrades);\n    }\n    getGradesByStudent(studentId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.studentId === studentId);\n    }\n    getGradesByClass(classId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.classId === classId);\n    }\n    // === إدارة الحضور ===\n    getAttendance() {\n        return this.getItem(STORAGE_KEYS.ATTENDANCE) || [];\n    }\n    saveAttendance(attendance) {\n        this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);\n    }\n    addAttendance(attendance) {\n        const attendanceRecords = this.getAttendance();\n        attendanceRecords.push(attendance);\n        this.saveAttendance(attendanceRecords);\n    }\n    updateAttendance(attendanceId, updatedAttendance) {\n        const attendanceRecords = this.getAttendance();\n        const index = attendanceRecords.findIndex((a)=>a.id === attendanceId);\n        if (index !== -1) {\n            attendanceRecords[index] = {\n                ...attendanceRecords[index],\n                ...updatedAttendance,\n                updatedAt: new Date()\n            };\n            this.saveAttendance(attendanceRecords);\n        }\n    }\n    getAttendanceByStudent(studentId) {\n        const attendance = this.getAttendance();\n        return attendance.filter((a)=>a.studentId === studentId);\n    }\n    getAttendanceByDate(date) {\n        const attendance = this.getAttendance();\n        const dateString = date.toDateString();\n        return attendance.filter((a)=>new Date(a.date).toDateString() === dateString);\n    }\n    // === إدارة المستخدمين ===\n    getUsers() {\n        return this.getItem(STORAGE_KEYS.USERS) || [];\n    }\n    saveUsers(users) {\n        this.setItem(STORAGE_KEYS.USERS, users);\n    }\n    getCurrentUser() {\n        return this.getItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    setCurrentUser(user) {\n        this.setItem(STORAGE_KEYS.CURRENT_USER, user);\n    }\n    logout() {\n        this.removeItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    // === إدارة الإعدادات ===\n    getSettings() {\n        return this.getItem(STORAGE_KEYS.SETTINGS);\n    }\n    saveSettings(settings) {\n        this.setItem(STORAGE_KEYS.SETTINGS, settings);\n    }\n    // === إدارة الأحداث ===\n    getEvents() {\n        return this.getItem(STORAGE_KEYS.EVENTS) || [];\n    }\n    saveEvents(events) {\n        this.setItem(STORAGE_KEYS.EVENTS, events);\n    }\n    addEvent(event) {\n        const events = this.getEvents();\n        events.push(event);\n        this.saveEvents(events);\n    }\n    // === إدارة الإشعارات ===\n    getNotifications() {\n        return this.getItem(STORAGE_KEYS.NOTIFICATIONS) || [];\n    }\n    saveNotifications(notifications) {\n        this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);\n    }\n    addNotification(notification) {\n        const notifications = this.getNotifications();\n        notifications.push(notification);\n        this.saveNotifications(notifications);\n    }\n    markNotificationAsRead(notificationId) {\n        const notifications = this.getNotifications();\n        const index = notifications.findIndex((n)=>n.id === notificationId);\n        if (index !== -1) {\n            notifications[index].isRead = true;\n            notifications[index].readAt = new Date();\n            this.saveNotifications(notifications);\n        }\n    }\n}\n// إنشاء مثيل واحد للاستخدام في التطبيق\nconst localStorageManager = new LocalStorageManager();\n// دوال مساعدة للبحث والتصفية\nconst searchUtils = {\n    searchStudents: (query, students)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return students.filter((student)=>{\n            var _student_email;\n            return student.name.toLowerCase().includes(lowercaseQuery) || student.studentId.toLowerCase().includes(lowercaseQuery) || ((_student_email = student.email) === null || _student_email === void 0 ? void 0 : _student_email.toLowerCase().includes(lowercaseQuery));\n        });\n    },\n    searchTeachers: (query, teachers)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return teachers.filter((teacher)=>{\n            var _teacher_name, _teacher_fullName, _teacher_teacherId, _teacher_serialNumber, _teacher_email, _teacher_specialization;\n            return ((_teacher_name = teacher.name) === null || _teacher_name === void 0 ? void 0 : _teacher_name.toLowerCase().includes(lowercaseQuery)) || ((_teacher_fullName = teacher.fullName) === null || _teacher_fullName === void 0 ? void 0 : _teacher_fullName.toLowerCase().includes(lowercaseQuery)) || ((_teacher_teacherId = teacher.teacherId) === null || _teacher_teacherId === void 0 ? void 0 : _teacher_teacherId.toLowerCase().includes(lowercaseQuery)) || ((_teacher_serialNumber = teacher.serialNumber) === null || _teacher_serialNumber === void 0 ? void 0 : _teacher_serialNumber.toLowerCase().includes(lowercaseQuery)) || ((_teacher_email = teacher.email) === null || _teacher_email === void 0 ? void 0 : _teacher_email.toLowerCase().includes(lowercaseQuery)) || ((_teacher_specialization = teacher.specialization) === null || _teacher_specialization === void 0 ? void 0 : _teacher_specialization.toLowerCase().includes(lowercaseQuery));\n        });\n    },\n    searchEmployees: (query, employees)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return employees.filter((employee)=>{\n            var _employee_name, _employee_fullName, _employee_employeeId, _employee_serialNumber, _employee_email, _employee_department, _employee_position;\n            return ((_employee_name = employee.name) === null || _employee_name === void 0 ? void 0 : _employee_name.toLowerCase().includes(lowercaseQuery)) || ((_employee_fullName = employee.fullName) === null || _employee_fullName === void 0 ? void 0 : _employee_fullName.toLowerCase().includes(lowercaseQuery)) || ((_employee_employeeId = employee.employeeId) === null || _employee_employeeId === void 0 ? void 0 : _employee_employeeId.toLowerCase().includes(lowercaseQuery)) || ((_employee_serialNumber = employee.serialNumber) === null || _employee_serialNumber === void 0 ? void 0 : _employee_serialNumber.toLowerCase().includes(lowercaseQuery)) || ((_employee_email = employee.email) === null || _employee_email === void 0 ? void 0 : _employee_email.toLowerCase().includes(lowercaseQuery)) || ((_employee_department = employee.department) === null || _employee_department === void 0 ? void 0 : _employee_department.toLowerCase().includes(lowercaseQuery)) || ((_employee_position = employee.position) === null || _employee_position === void 0 ? void 0 : _employee_position.toLowerCase().includes(lowercaseQuery));\n        });\n    },\n    filterStudentsByClass: (classId, students)=>{\n        return students.filter((student)=>student.classId === classId);\n    },\n    filterGradesByDateRange: (startDate, endDate, grades)=>{\n        return grades.filter((grade)=>{\n            const gradeDate = new Date(grade.examDate);\n            return gradeDate >= startDate && gradeDate <= endDate;\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/localStorage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/sampleData.ts":
/*!*********************************!*\
  !*** ./src/utils/sampleData.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeveloperAccount: () => (/* binding */ createDeveloperAccount),\n/* harmony export */   initializeSampleData: () => (/* binding */ initializeSampleData),\n/* harmony export */   resetSampleData: () => (/* binding */ resetSampleData)\n/* harmony export */ });\n/* harmony import */ var _localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./localStorage */ \"(app-pages-browser)/./src/utils/localStorage.ts\");\n\n// إنشاء بيانات تجريبية للنظام\nconst initializeSampleData = ()=>{\n    // التحقق من وجود بيانات مسبقة\n    const existingUsers = _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.getUsers();\n    if (existingUsers.length > 0) {\n        return; // البيانات موجودة بالفعل\n    }\n    createSampleData();\n};\n// إعادة تعيين البيانات التجريبية (حذف البيانات الموجودة وإنشاء بيانات جديدة)\nconst resetSampleData = ()=>{\n    // مسح جميع البيانات الموجودة\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.clearAll();\n    // إنشاء البيانات التجريبية الجديدة\n    createSampleData();\n    console.log('تم إعادة تعيين البيانات التجريبية بنجاح');\n};\n// دالة منفصلة لإنشاء البيانات\nconst createSampleData = ()=>{\n    // إنشاء المستخدمين\n    const users = [\n        {\n            id: 'user-1',\n            name: 'عبيدة العيثاوي',\n            username: 'obeida',\n            email: '<EMAIL>',\n            password: '12345',\n            role: 'developer',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'user-2',\n            name: 'admin',\n            username: 'admin',\n            email: '<EMAIL>',\n            password: 'admin',\n            role: 'admin',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المواد الدراسية\n    const subjects = [\n        {\n            id: 'subject-1',\n            name: 'الرياضيات',\n            code: 'MATH101',\n            description: 'مادة الرياضيات للصف الأول',\n            grade: 1,\n            credits: 4,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-2',\n            name: 'اللغة العربية',\n            code: 'ARAB101',\n            description: 'مادة اللغة العربية للصف الأول',\n            grade: 1,\n            credits: 5,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-3',\n            name: 'العلوم',\n            code: 'SCI101',\n            description: 'مادة العلوم للصف الأول',\n            grade: 1,\n            credits: 3,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-4',\n            name: 'التاريخ',\n            code: 'HIST101',\n            description: 'مادة التاريخ للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-5',\n            name: 'الجغرافيا',\n            code: 'GEO101',\n            description: 'مادة الجغرافيا للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المعلمين (بيانات تجريبية)\n    const teachers = [\n        {\n            id: 'teacher-1',\n            serialNumber: '001',\n            fullName: 'فاطمة أحمد محمد علي',\n            shortName: 'زينب حسن محمد',\n            title: 'أستاذة',\n            specialization: 'الرياضيات',\n            firstAppointmentDate: new Date('2018-09-01'),\n            schoolStartDate: new Date('2020-09-01'),\n            dateOfBirth: new Date('1985-05-15'),\n            address: 'بغداد - الكرادة - شارع الجامعة',\n            phone: '07901234567',\n            employmentType: 'permanent',\n            status: 'active',\n            subjects: [\n                'subject-1'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '001',\n            name: 'فاطمة أحمد محمد',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس رياضيات',\n            experience: 8,\n            salary: 800000,\n            hireDate: new Date('2020-09-01')\n        },\n        {\n            id: 'teacher-2',\n            serialNumber: '002',\n            fullName: 'محمد علي حسن الأستاذ',\n            shortName: 'فاطمة أحمد علي',\n            title: 'أستاذ',\n            specialization: 'اللغة العربية',\n            firstAppointmentDate: new Date('2016-09-01'),\n            schoolStartDate: new Date('2018-09-01'),\n            dateOfBirth: new Date('1980-03-20'),\n            address: 'بغداد - الجادرية - المنطقة الثانية',\n            phone: '07801234567',\n            employmentType: 'assignment',\n            status: 'active',\n            subjects: [\n                'subject-2'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '002',\n            name: 'محمد علي حسن الأستاذ',\n            email: '<EMAIL>',\n            gender: 'male',\n            qualification: 'ماجستير لغة عربية',\n            experience: 12,\n            salary: 900000,\n            hireDate: new Date('2018-09-01')\n        },\n        {\n            id: 'teacher-3',\n            serialNumber: '003',\n            fullName: 'سارة خالد أحمد المدرسة',\n            shortName: 'مريم حسين محمد',\n            title: 'مدرسة',\n            specialization: 'العلوم',\n            firstAppointmentDate: new Date('2021-09-01'),\n            schoolStartDate: new Date('2021-09-01'),\n            dateOfBirth: new Date('1990-08-12'),\n            address: 'بغداد - الكاظمية - حي الأطباء',\n            phone: '07701234567',\n            employmentType: 'contract',\n            status: 'active',\n            subjects: [\n                'subject-3'\n            ],\n            classes: [\n                'class-2'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '003',\n            name: 'سارة خالد أحمد المدرسة',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس علوم',\n            experience: 3,\n            salary: 700000,\n            hireDate: new Date('2021-09-01')\n        }\n    ];\n    // إنشاء الصفوف\n    const classes = [\n        {\n            id: 'class-1',\n            name: 'الصف الأول أ',\n            grade: 1,\n            section: 'أ',\n            capacity: 30,\n            currentStudents: 20,\n            classTeacherId: 'teacher-1',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'class-2',\n            name: 'الصف الأول ب',\n            grade: 1,\n            section: 'ب',\n            capacity: 30,\n            currentStudents: 18,\n            classTeacherId: 'teacher-2',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الطلاب (بيانات تجريبية)\n    const students = [\n        {\n            id: 'student-1',\n            studentId: 'S001',\n            name: 'علي أحمد محمد',\n            email: '<EMAIL>',\n            phone: '07701234567',\n            dateOfBirth: new Date('2012-01-15'),\n            gender: 'male',\n            address: 'بغداد - الكرادة - شارع الرشيد',\n            parentName: 'أحمد محمد علي',\n            parentPhone: '07901234567',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالب متفوق',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-2',\n            studentId: 'S002',\n            name: 'فاطمة حسن علي',\n            email: '<EMAIL>',\n            phone: '07701234568',\n            dateOfBirth: new Date('2012-03-20'),\n            gender: 'female',\n            address: 'بغداد - الجادرية - شارع الجامعة',\n            parentName: 'حسن علي محمد',\n            parentPhone: '07901234568',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'حساسية من الفول السوداني',\n            notes: 'طالبة نشيطة',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-3',\n            studentId: 'S003',\n            name: 'زينب محمد حسن',\n            email: '<EMAIL>',\n            phone: '07701234570',\n            dateOfBirth: new Date('2012-07-25'),\n            gender: 'female',\n            address: 'بغداد - الكاظمية - شارع الإمام',\n            parentName: 'محمد حسن علي',\n            parentPhone: '07901234570',\n            parentEmail: '<EMAIL>',\n            classId: 'class-2',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالبة متميزة في اللغة العربية',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الإعدادات\n    const settings = {\n        id: 'settings-1',\n        schoolName: 'مدرسة النور الابتدائية',\n        address: 'بغداد - الكرادة - شارع الرشيد',\n        phone: '07901234567',\n        email: '<EMAIL>',\n        website: 'www.alnoor-school.edu.iq',\n        academicYear: '2024-2025',\n        currentSemester: 'first',\n        gradeSystem: 'percentage',\n        attendanceRequired: true,\n        maxAbsences: 10,\n        workingDays: [\n            'sunday',\n            'monday',\n            'tuesday',\n            'wednesday',\n            'thursday'\n        ],\n        schoolStartTime: '08:00',\n        schoolEndTime: '14:00',\n        updatedAt: new Date()\n    };\n    // حفظ البيانات في localStorage\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSubjects(subjects);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveTeachers(teachers);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveClasses(classes);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveStudents(students);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSettings(settings);\n    console.log('تم إنشاء البيانات التجريبية بنجاح');\n};\n// إنشاء حساب مطور جديد\nconst createDeveloperAccount = function(username, email, password) {\n    let name = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'المطور';\n    const users = _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.getUsers();\n    // التحقق من عدم وجود مطور آخر\n    const existingDeveloper = users.find((u)=>u.role === 'developer');\n    if (existingDeveloper) {\n        // تحديث بيانات المطور الموجود\n        existingDeveloper.username = username;\n        existingDeveloper.email = email;\n        existingDeveloper.password = password;\n        existingDeveloper.name = name;\n        existingDeveloper.updatedAt = new Date();\n        _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n        console.log('تم تحديث بيانات المطور بنجاح');\n        return existingDeveloper;\n    } else {\n        // إنشاء مطور جديد\n        const newDeveloper = {\n            id: \"user-dev-\".concat(Date.now()),\n            name,\n            username,\n            email,\n            password,\n            role: 'developer',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        users.push(newDeveloper);\n        _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n        console.log('تم إنشاء حساب المطور بنجاح');\n        return newDeveloper;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/sampleData.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);