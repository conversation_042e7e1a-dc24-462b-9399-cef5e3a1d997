// مكون لإظهار جميع المستخدمين وكلمات المرور الخاصة بهم (للمطور فقط)
'use client';
import React, { useState, useEffect } from 'react';
import { localStorageManager } from '@/utils/localStorage';
import Card from '@/components/Card';
import Button from '@/components/Button';
import { User } from '@/types';

export default function ShowAllUserPasswords() {
  const [users, setUsers] = useState<User[]>([]);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const users = localStorageManager.getUsers();
    setUsers(users);
  }, []);

  return (
    <Card className="my-8 max-w-2xl mx-auto">
      <div className="flex flex-col items-center space-y-4">
        <div className="text-xl font-bold text-blue-700">جميع المستخدمين وكلمات المرور</div>
        <Button variant="primary" onClick={() => setShow(!show)}>
          {show ? 'إخفاء كلمات المرور' : 'إظهار كلمات المرور'}
        </Button>
        {show && (
          <div className="w-full overflow-x-auto mt-4">
            <table className="min-w-full border text-center">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 border">الاسم</th>
                  <th className="py-2 px-4 border">اسم المستخدم</th>
                  <th className="py-2 px-4 border">البريد الإلكتروني</th>
                  <th className="py-2 px-4 border">الدور</th>
                  <th className="py-2 px-4 border">كلمة المرور</th>
                </tr>
              </thead>
              <tbody>
                {users.map(user => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="py-1 px-2 border">{user.name}</td>
                    <td className="py-1 px-2 border">{user.username || '-'}</td>
                    <td className="py-1 px-2 border">{user.email}</td>
                    <td className="py-1 px-2 border">{user.role}</td>
                    <td className="py-1 px-2 border font-mono text-red-700">{user.password || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Card>
  );
}
