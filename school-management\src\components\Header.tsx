'use client';

import React, { useState, useEffect } from 'react';
import { User } from '@/types';
import { useAuth } from '@/hooks/useAuth';

interface HeaderProps {
  onToggleSidebar: () => void;
  currentUser: User | null;
  onLogout?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar, currentUser, onLogout }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { logout } = useAuth();

  useEffect(() => {
    // تحديث الوقت كل ثانية
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatWeekday = (date: Date) => {
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long'
    });
  };

  const formatGregorianDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };



  return (
    <header className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-40">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* الجانب الأيمن - زر القائمة والعنوان */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={onToggleSidebar}
              className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <div className="hidden md:block">
              <h1 className="text-xl font-bold text-gray-900">نظام إدارة المدرسة</h1>
            </div>
          </div>

          {/* الوسط - التاريخ والوقت */}
          <div className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {/* اليوم */}
            <div className="text-center">
              <div className="text-xs text-gray-500">اليوم</div>
              <div className="text-sm text-gray-700 font-medium">
                {formatWeekday(currentTime)}
              </div>
            </div>

            {/* فاصل */}
            <div className="w-px h-10 bg-gray-300"></div>

            {/* التاريخ الهجري */}
            <div className="text-center">
              <div className="text-xs text-gray-500">هجري</div>
              <div className="text-sm text-gray-700 font-medium">
                {formatDate(currentTime)}
              </div>
            </div>

            {/* فاصل */}
            <div className="w-px h-10 bg-gray-300"></div>

            {/* التاريخ الميلادي */}
            <div className="text-center">
              <div className="text-xs text-gray-500">ميلادي</div>
              <div className="text-sm text-gray-700 font-medium">
                {formatGregorianDate(currentTime)}
              </div>
            </div>

            {/* فاصل */}
            <div className="w-px h-10 bg-gray-300 ml-4"></div>

            {/* الوقت */}
            <div className="text-center ml-8">
              <div className="text-xs text-gray-500">الوقت</div>
              <div className="text-lg font-bold text-blue-600">
                {formatTime(currentTime)}
              </div>
            </div>
          </div>

          {/* الجانب الأيسر - قائمة المستخدم */}
          <div className="flex items-center space-x-4 space-x-reverse">

            {/* قائمة المستخدم */}
            {currentUser && (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {currentUser.name.charAt(0)}
                    </span>
                  </div>
                  <div className="hidden md:block text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {currentUser.name}
                    </div>
                    <div className="text-xs text-gray-600">
                      {currentUser.role === 'developer' && 'المطور'}
                      {currentUser.role === 'admin' && 'مدير النظام'}
                      {currentUser.role === 'teacher' && 'معلم'}
                      {currentUser.role === 'student' && 'طالب'}
                      {currentUser.role === 'parent' && 'ولي أمر'}
                    </div>
                  </div>
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* قائمة المستخدم المنسدلة */}
                {showUserMenu && (
                  <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                    <div className="py-2">
                      <button
                        onClick={() => window.location.href = '/profile'}
                        className="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        الملف الشخصي
                      </button>
                      <button
                        onClick={() => window.location.href = '/settings'}
                        className="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        الإعدادات
                      </button>
                      <hr className="my-2" />
                      <button
                        onClick={() => {
                          if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                            if (onLogout) {
                              onLogout();
                            } else {
                              logout();
                            }
                          }
                        }}
                        className="w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors flex items-center space-x-2 space-x-reverse"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span>تسجيل الخروج</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* إغلاق القوائم عند النقر خارجها */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => {
            setShowUserMenu(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;
