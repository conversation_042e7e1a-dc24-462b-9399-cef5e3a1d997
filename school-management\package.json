{"name": "school-management", "version": "0.1.0", "private": true, "description": "نظام شامل لإدارة المدارس باللغة العربية", "author": "عبيدة العيثاوي", "scripts": {"dev": "next dev --port 3000", "dev:debug": "next dev --port 3000 --inspect", "build": "next build", "start": "next start --port 3000", "start:prod": "npm run build && npm run start", "lint": "next lint", "clean": "rm -rf .next node_modules/.cache", "reinstall": "rm -rf node_modules package-lock.json && npm install"}, "dependencies": {"docx": "^9.5.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5", "wait-on": "^8.0.3"}}