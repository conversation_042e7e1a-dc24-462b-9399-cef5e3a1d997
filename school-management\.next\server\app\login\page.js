/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e7050786830a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc2YbYuNin2YUg2YXYr9ix2LPYqVxcc2Nob29sLW1hbmFnZW1lbnRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU3MDUwNzg2ODMwYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"نظام إدارة المدرسة\",\n    description: \"نظام شامل لإدارة المدارس باللغة العربية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\نظام مدرسة\\school-management\\src\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Button */ \"(ssr)/./src/components/Button.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _utils_sampleData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/sampleData */ \"(ssr)/./src/utils/sampleData.ts\");\n/* harmony import */ var _utils_appInfo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/appInfo */ \"(ssr)/./src/utils/appInfo.ts\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LoginPage() {\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // إزالة وضع إنشاء الحسابات - متاح للمطور فقط من صفحة إدارة المستخدمين\n    const [isDeveloperMode, setIsDeveloperMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // حقول إنشاء الحساب الجديد\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, loading, isAuthenticated } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // معلومات المطور والتطبيق\n    const developerInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_6__.getDeveloperInfo)();\n    const appInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_6__.getAppInfo)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            // تحميل البيانات التجريبية إذا لم تكن موجودة\n            (0,_utils_sampleData__WEBPACK_IMPORTED_MODULE_5__.initializeSampleData)();\n            // التحقق من وجود مستخدم مسجل دخول مسبقاً\n            if (isAuthenticated) {\n                router.push('/');\n            }\n            // التحقق من وضع المطور من URL\n            const urlParams = new URLSearchParams(window.location.search);\n            if (urlParams.get('developer') === 'true') {\n                setIsDeveloperMode(true);\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        isAuthenticated,\n        router\n    ]);\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setError('');\n        const result = await login(username, password);\n        if (result.success) {\n            // حفظ حالة \"تذكرني\" إذا كانت مفعلة\n            if (rememberMe) {\n                localStorage.setItem('rememberLogin', 'true');\n            }\n            // التحقق من آخر تسجيل دخول\n            const lastLogin = localStorage.getItem('lastLogin');\n            const isFirstLogin = !lastLogin;\n            // حفظ وقت تسجيل الدخول الحالي\n            localStorage.setItem('lastLogin', new Date().toISOString());\n            // الانتقال إلى صفحة الترحيب للمستخدمين الجدد أو الصفحة الرئيسية\n            if (isFirstLogin) {\n                router.push('/welcome');\n            } else {\n                router.push('/');\n            }\n        } else {\n            setError(result.error || 'حدث خطأ أثناء تسجيل الدخول');\n        }\n    };\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setError('');\n        // التحقق من صحة البيانات\n        if (!fullName.trim()) {\n            setError('يرجى إدخال الاسم الكامل');\n            return;\n        }\n        if (!email.trim()) {\n            setError('يرجى إدخال البريد الإلكتروني');\n            return;\n        }\n        if (!username.trim()) {\n            setError('يرجى إدخال اسم المستخدم');\n            return;\n        }\n        if (password.length < 4) {\n            setError('كلمة المرور يجب أن تكون 4 أحرف على الأقل');\n            return;\n        }\n        if (password !== confirmPassword) {\n            setError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');\n            return;\n        }\n        // التحقق من عدم وجود المستخدم مسبقاً\n        const existingUsers = _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.getUsers();\n        const userExists = existingUsers.some((u)=>u.email === email || u.name === username);\n        if (userExists) {\n            setError('المستخدم موجود بالفعل، يرجى استخدام بريد إلكتروني أو اسم مستخدم مختلف');\n            return;\n        }\n        try {\n            // إنشاء المستخدم الجديد\n            const newUser = {\n                id: `user-${Date.now()}`,\n                name: fullName,\n                email: email,\n                password: password,\n                role: 'student',\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            // حفظ المستخدم الجديد\n            const updatedUsers = [\n                ...existingUsers,\n                newUser\n            ];\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.saveUsers(updatedUsers);\n            // تسجيل دخول تلقائي\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.setCurrentUser(newUser);\n            // إضافة إشعار ترحيب\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'مرحباً بك في النظام',\n                message: `تم إنشاء حسابك بنجاح، مرحباً ${fullName}`,\n                type: 'success',\n                recipientId: newUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_7__.localStorageManager.addNotification(notification);\n            // الانتقال إلى صفحة الترحيب\n            router.push('/welcome');\n        } catch (error) {\n            console.error('خطأ في إنشاء الحساب:', error);\n            setError('حدث خطأ أثناء إنشاء الحساب');\n        }\n    };\n    const resetForm = ()=>{\n        setUsername('');\n        setPassword('');\n        setFullName('');\n        setEmail('');\n        setConfirmPassword('');\n        setError('');\n        setRememberMe(false);\n    };\n    // تم إزالة وضع إنشاء الحسابات\n    // const demoAccounts = [\n    //   { username: 'admin', role: 'مدير النظام', name: 'عبيدة العيثاوي' },\n    //   { username: 'teacher', role: 'معلمة', name: 'فاطمة أحمد المعلمة' },\n    //   { username: 'student', role: 'طالب', name: 'علي أحمد محمد' },\n    //   { username: 'parent', role: 'ولي أمر', name: 'أحمد محمد علي' }\n    // ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-20 animate-pulse delay-1000\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-br from-green-400 to-blue-600 rounded-full opacity-10 animate-pulse delay-500\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 min-h-[600px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 p-8 lg:p-12 flex flex-col justify-center items-center text-white relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-10 right-10 w-32 h-32 border-2 border-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-10 left-10 w-24 h-24 border-2 border-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-4xl\",\n                                                        children: \"\\uD83C\\uDFEB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl lg:text-4xl font-bold mb-4\",\n                                                    children: \"نظام إدارة المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg lg:text-xl mb-6 text-white/90\",\n                                                    children: \"نظام شامل لإدارة المدارس باللغة العربية\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 text-white/80 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: \"✨\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"إدارة متكاملة للطلاب والمعلمين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: \"\\uD83D\\uDCCA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"تقارير مفصلة ومتقدمة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2\",\n                                                                    children: \"\\uD83D\\uDD12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"أمان وحماية عالية للبيانات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center ml-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white text-xl\",\n                                                                            children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 232,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-white font-bold text-sm\",\n                                                                                children: appInfo.contact.developer\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 235,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-xs\",\n                                                                                children: \"مطور النظام\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 236,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-left\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white/80 text-xs ml-2\",\n                                                                                children: \"\\uD83D\\uDCF1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white text-xs font-medium\",\n                                                                                children: appInfo.contact.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white/80 text-xs ml-2\",\n                                                                                children: \"✈️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white/80 text-xs\",\n                                                                                children: \"@ob992\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 248,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 lg:p-12 flex flex-col justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md mx-auto w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                        children: \"مرحباً بك\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"يرجى تسجيل الدخول للمتابعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleLogin,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"البريد الإلكتروني أو اسم المستخدم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: username,\n                                                                        onChange: (e)=>setUsername(e.target.value),\n                                                                        className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                                        placeholder: \"أدخل البريد الإلكتروني أو اسم المستخدم\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"كلمة المرور\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: showPassword ? 'text' : 'password',\n                                                                        value: password,\n                                                                        onChange: (e)=>setPassword(e.target.value),\n                                                                        className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                                        placeholder: \"أدخل كلمة المرور\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"hover:text-gray-600 transition-colors\",\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-5 h-5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 317,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-5 h-5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 322,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 323,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: rememberMe,\n                                                                        onChange: (e)=>setRememberMe(e.target.checked),\n                                                                        className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2 text-sm text-gray-600\",\n                                                                        children: \"تذكرني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-sm text-blue-600 hover:text-blue-800 transition-colors\",\n                                                                children: \"نسيت كلمة المرور؟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-red-400 ml-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-red-700\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        type: \"submit\",\n                                                        variant: \"primary\",\n                                                        loading: loading,\n                                                        fullWidth: true,\n                                                        className: \"h-12 text-lg font-semibold\",\n                                                        children: loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-800 mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"للحصول على حساب جديد:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-700\",\n                                                            children: \"يرجى التواصل مع المطور لإنشاء حساب جديد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-600 mt-2\",\n                                                            children: [\n                                                                \"\\uD83D\\uDCF1 \",\n                                                                appInfo.contact.phone,\n                                                                \" | ✈️ @ob992\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"جميع الحقوق محفوظة \\xa9 2025 - نظام إدارة المدرسة\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Button = ({ children, onClick, type = 'button', variant = 'primary', size = 'md', disabled = false, loading = false, fullWidth = false, className = '', icon, iconPosition = 'right', title })=>{\n    const baseClasses = `\n    inline-flex items-center justify-center font-medium rounded-lg\n    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${fullWidth ? 'w-full' : ''}\n  `;\n    const sizeClasses = {\n        sm: 'px-3 py-2 text-sm',\n        md: 'px-4 py-2.5 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    const variantClasses = {\n        primary: `\n      bg-gradient-to-r from-blue-500 to-purple-600 text-white\n      hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        secondary: `\n      bg-gray-100 text-gray-700 border border-gray-300\n      hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500\n      shadow-md hover:shadow-lg\n    `,\n        success: `\n      bg-gradient-to-r from-green-500 to-emerald-600 text-white\n      hover:from-green-600 hover:to-emerald-700 focus:ring-green-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        danger: `\n      bg-gradient-to-r from-red-500 to-pink-600 text-white\n      hover:from-red-600 hover:to-pink-700 focus:ring-red-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        warning: `\n      bg-gradient-to-r from-yellow-500 to-orange-600 text-white\n      hover:from-yellow-600 hover:to-orange-700 focus:ring-yellow-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        info: `\n      bg-gradient-to-r from-cyan-500 to-blue-600 text-white\n      hover:from-cyan-600 hover:to-blue-700 focus:ring-cyan-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `\n    };\n    const combinedClasses = `\n    ${baseClasses}\n    ${sizeClasses[size]}\n    ${variantClasses[variant]}\n    ${className}\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled || loading,\n        className: combinedClasses,\n        title: title,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-current\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            icon && iconPosition === 'right' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            icon && iconPosition === 'left' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9CdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUUwQjtBQWlCMUIsTUFBTUMsU0FBZ0MsQ0FBQyxFQUNyQ0MsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLE9BQU8sUUFBUSxFQUNmQyxVQUFVLFNBQVMsRUFDbkJDLE9BQU8sSUFBSSxFQUNYQyxXQUFXLEtBQUssRUFDaEJDLFVBQVUsS0FBSyxFQUNmQyxZQUFZLEtBQUssRUFDakJDLFlBQVksRUFBRSxFQUNkQyxJQUFJLEVBQ0pDLGVBQWUsT0FBTyxFQUN0QkMsS0FBSyxFQUNOO0lBQ0MsTUFBTUMsY0FBYyxDQUFDOzs7O0lBSW5CLEVBQUVMLFlBQVksV0FBVyxHQUFHO0VBQzlCLENBQUM7SUFFRCxNQUFNTSxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCQyxTQUFTLENBQUM7Ozs7SUFJVixDQUFDO1FBQ0RDLFdBQVcsQ0FBQzs7OztJQUlaLENBQUM7UUFDREMsU0FBUyxDQUFDOzs7O0lBSVYsQ0FBQztRQUNEQyxRQUFRLENBQUM7Ozs7SUFJVCxDQUFDO1FBQ0RDLFNBQVMsQ0FBQzs7OztJQUlWLENBQUM7UUFDREMsTUFBTSxDQUFDOzs7O0lBSVAsQ0FBQztJQUNIO0lBRUEsTUFBTUMsa0JBQWtCLENBQUM7SUFDdkIsRUFBRVosWUFBWTtJQUNkLEVBQUVDLFdBQVcsQ0FBQ1QsS0FBSyxDQUFDO0lBQ3BCLEVBQUVhLGNBQWMsQ0FBQ2QsUUFBUSxDQUFDO0lBQzFCLEVBQUVLLFVBQVU7RUFDZCxDQUFDO0lBRUQscUJBQ0UsOERBQUNpQjtRQUNDdkIsTUFBTUE7UUFDTkQsU0FBU0E7UUFDVEksVUFBVUEsWUFBWUM7UUFDdEJFLFdBQVdnQjtRQUNYYixPQUFPQTs7WUFFTkwseUJBQ0MsOERBQUNvQjtnQkFDQ2xCLFdBQVU7Z0JBQ1ZtQixPQUFNO2dCQUNOQyxNQUFLO2dCQUNMQyxTQUFROztrQ0FFUiw4REFBQ0M7d0JBQ0N0QixXQUFVO3dCQUNWdUIsSUFBRzt3QkFDSEMsSUFBRzt3QkFDSEMsR0FBRTt3QkFDRkMsUUFBTzt3QkFDUEMsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDQzt3QkFDQzVCLFdBQVU7d0JBQ1ZvQixNQUFLO3dCQUNMUyxHQUFFOzs7Ozs7Ozs7Ozs7WUFLUDVCLFFBQVFDLGlCQUFpQixXQUFXLENBQUNKLHlCQUNwQyw4REFBQ2dDO2dCQUFLOUIsV0FBVTswQkFBUUM7Ozs7OzswQkFHMUIsOERBQUM2QjswQkFBTXRDOzs7Ozs7WUFFTlMsUUFBUUMsaUJBQWlCLFVBQVUsQ0FBQ0oseUJBQ25DLDhEQUFDZ0M7Z0JBQUs5QixXQUFVOzBCQUFRQzs7Ozs7Ozs7Ozs7O0FBSWhDO0FBRUEsaUVBQWVWLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxjb21wb25lbnRzXFxCdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIEJ1dHRvblByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgb25DbGljaz86ICgpID0+IHZvaWQ7XG4gIHR5cGU/OiAnYnV0dG9uJyB8ICdzdWJtaXQnIHwgJ3Jlc2V0JztcbiAgdmFyaWFudD86ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ3N1Y2Nlc3MnIHwgJ2RhbmdlcicgfCAnd2FybmluZycgfCAnaW5mbyc7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZyc7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgbG9hZGluZz86IGJvb2xlYW47XG4gIGZ1bGxXaWR0aD86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgaWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgaWNvblBvc2l0aW9uPzogJ2xlZnQnIHwgJ3JpZ2h0JztcbiAgdGl0bGU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IEJ1dHRvbjogUmVhY3QuRkM8QnV0dG9uUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIG9uQ2xpY2ssXG4gIHR5cGUgPSAnYnV0dG9uJyxcbiAgdmFyaWFudCA9ICdwcmltYXJ5JyxcbiAgc2l6ZSA9ICdtZCcsXG4gIGRpc2FibGVkID0gZmFsc2UsXG4gIGxvYWRpbmcgPSBmYWxzZSxcbiAgZnVsbFdpZHRoID0gZmFsc2UsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBpY29uLFxuICBpY29uUG9zaXRpb24gPSAncmlnaHQnLFxuICB0aXRsZVxufSkgPT4ge1xuICBjb25zdCBiYXNlQ2xhc3NlcyA9IGBcbiAgICBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZm9udC1tZWRpdW0gcm91bmRlZC1sZ1xuICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTJcbiAgICBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFxuICAgICR7ZnVsbFdpZHRoID8gJ3ctZnVsbCcgOiAnJ31cbiAgYDtcblxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbTogJ3B4LTMgcHktMiB0ZXh0LXNtJyxcbiAgICBtZDogJ3B4LTQgcHktMi41IHRleHQtc20nLFxuICAgIGxnOiAncHgtNiBweS0zIHRleHQtYmFzZSdcbiAgfTtcblxuICBjb25zdCB2YXJpYW50Q2xhc3NlcyA9IHtcbiAgICBwcmltYXJ5OiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlXG4gICAgICBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLXB1cnBsZS03MDAgZm9jdXM6cmluZy1ibHVlLTUwMFxuICAgICAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2Zvcm0gaG92ZXI6LXRyYW5zbGF0ZS15LTAuNVxuICAgIGAsXG4gICAgc2Vjb25kYXJ5OiBgXG4gICAgICBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS0zMDBcbiAgICAgIGhvdmVyOmJnLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ncmF5LTQwMCBmb2N1czpyaW5nLWdyYXktNTAwXG4gICAgICBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnXG4gICAgYCxcbiAgICBzdWNjZXNzOiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAwIHRvLWVtZXJhbGQtNjAwIHRleHQtd2hpdGVcbiAgICAgIGhvdmVyOmZyb20tZ3JlZW4tNjAwIGhvdmVyOnRvLWVtZXJhbGQtNzAwIGZvY3VzOnJpbmctZ3JlZW4tNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYCxcbiAgICBkYW5nZXI6IGBcbiAgICAgIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNTAwIHRvLXBpbmstNjAwIHRleHQtd2hpdGVcbiAgICAgIGhvdmVyOmZyb20tcmVkLTYwMCBob3Zlcjp0by1waW5rLTcwMCBmb2N1czpyaW5nLXJlZC01MDBcbiAgICAgIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0wLjVcbiAgICBgLFxuICAgIHdhcm5pbmc6IGBcbiAgICAgIGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNTAwIHRvLW9yYW5nZS02MDAgdGV4dC13aGl0ZVxuICAgICAgaG92ZXI6ZnJvbS15ZWxsb3ctNjAwIGhvdmVyOnRvLW9yYW5nZS03MDAgZm9jdXM6cmluZy15ZWxsb3ctNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYCxcbiAgICBpbmZvOiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tY3lhbi01MDAgdG8tYmx1ZS02MDAgdGV4dC13aGl0ZVxuICAgICAgaG92ZXI6ZnJvbS1jeWFuLTYwMCBob3Zlcjp0by1ibHVlLTcwMCBmb2N1czpyaW5nLWN5YW4tNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYFxuICB9O1xuXG4gIGNvbnN0IGNvbWJpbmVkQ2xhc3NlcyA9IGBcbiAgICAke2Jhc2VDbGFzc2VzfVxuICAgICR7c2l6ZUNsYXNzZXNbc2l6ZV19XG4gICAgJHt2YXJpYW50Q2xhc3Nlc1t2YXJpYW50XX1cbiAgICAke2NsYXNzTmFtZX1cbiAgYDtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkIHx8IGxvYWRpbmd9XG4gICAgICBjbGFzc05hbWU9e2NvbWJpbmVkQ2xhc3Nlc31cbiAgICAgIHRpdGxlPXt0aXRsZX1cbiAgICA+XG4gICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgIDxzdmdcbiAgICAgICAgICBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gLW1sLTEgbXItMiBoLTQgdy00IHRleHQtY3VycmVudFwiXG4gICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICA+XG4gICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiXG4gICAgICAgICAgICBjeD1cIjEyXCJcbiAgICAgICAgICAgIGN5PVwiMTJcIlxuICAgICAgICAgICAgcj1cIjEwXCJcbiAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgIC8+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIlxuICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIHtpY29uICYmIGljb25Qb3NpdGlvbiA9PT0gJ3JpZ2h0JyAmJiAhbG9hZGluZyAmJiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTJcIj57aWNvbn08L3NwYW4+XG4gICAgICApfVxuICAgICAgXG4gICAgICA8c3Bhbj57Y2hpbGRyZW59PC9zcGFuPlxuICAgICAgXG4gICAgICB7aWNvbiAmJiBpY29uUG9zaXRpb24gPT09ICdsZWZ0JyAmJiAhbG9hZGluZyAmJiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj57aWNvbn08L3NwYW4+XG4gICAgICApfVxuICAgIDwvYnV0dG9uPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQnV0dG9uO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiY2hpbGRyZW4iLCJvbkNsaWNrIiwidHlwZSIsInZhcmlhbnQiLCJzaXplIiwiZGlzYWJsZWQiLCJsb2FkaW5nIiwiZnVsbFdpZHRoIiwiY2xhc3NOYW1lIiwiaWNvbiIsImljb25Qb3NpdGlvbiIsInRpdGxlIiwiYmFzZUNsYXNzZXMiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInZhcmlhbnRDbGFzc2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsInN1Y2Nlc3MiLCJkYW5nZXIiLCJ3YXJuaW5nIiwiaW5mbyIsImNvbWJpbmVkQ2xhc3NlcyIsImJ1dHRvbiIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwicGF0aCIsImQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"useAuth.useEffect\"], []);\n    const checkAuth = ()=>{\n        try {\n            const currentUser = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getCurrentUser();\n            setUser(currentUser);\n        } catch (error) {\n            console.error('خطأ في التحقق من المصادقة:', error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (emailOrUsername, password)=>{\n        try {\n            setLoading(true);\n            // محاكاة تأخير الشبكة\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const foundUser = users.find((u)=>(u.email === emailOrUsername || u.username === emailOrUsername) && u.password === password);\n            if (!foundUser) {\n                return {\n                    success: false,\n                    error: 'البريد الإلكتروني/اسم المستخدم أو كلمة المرور غير صحيحة'\n                };\n            }\n            // تسجيل الدخول بنجاح\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(foundUser);\n            setUser(foundUser);\n            // إضافة إشعار نجاح تسجيل الدخول\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'تم تسجيل الدخول بنجاح',\n                message: `مرحباً ${foundUser.name}، تم تسجيل دخولك بنجاح`,\n                type: 'success',\n                recipientId: foundUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: 'حدث خطأ أثناء تسجيل الدخول'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        try {\n            // إضافة إشعار تسجيل الخروج\n            if (user) {\n                const notification = {\n                    id: `notification-${Date.now()}`,\n                    title: 'تم تسجيل الخروج',\n                    message: `تم تسجيل خروجك من النظام بنجاح`,\n                    type: 'info',\n                    recipientId: user.id,\n                    recipientType: 'user',\n                    isRead: false,\n                    createdAt: new Date()\n                };\n                _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            }\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.logout();\n            setUser(null);\n            router.push('/login');\n        } catch (error) {\n            console.error('خطأ في تسجيل الخروج:', error);\n        }\n    };\n    const hasRole = (roles)=>{\n        if (!user) return false;\n        return roles.includes(user.role);\n    };\n    const updatePassword = async (currentPassword, newPassword)=>{\n        try {\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'المستخدم غير مسجل الدخول'\n                };\n            }\n            // التحقق من كلمة المرور الحالية\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const currentUser = users.find((u)=>u.id === user.id);\n            if (!currentUser || currentUser.password !== currentPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الحالية غير صحيحة'\n                };\n            }\n            // التحقق من قوة كلمة المرور الجديدة\n            if (newPassword.length < 3) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة قصيرة جداً (الحد الأدنى 3 أحرف)'\n                };\n            }\n            if (currentPassword === newPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية'\n                };\n            }\n            // تحديث كلمة المرور\n            const updatedUsers = users.map((u)=>u.id === user.id ? {\n                    ...u,\n                    password: newPassword,\n                    updatedAt: new Date()\n                } : u);\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.saveUsers(updatedUsers);\n            // تحديث المستخدم الحالي\n            const updatedUser = {\n                ...user,\n                password: newPassword,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(updatedUser);\n            setUser(updatedUser);\n            // إضافة إشعار نجاح تغيير كلمة المرور\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'تم تغيير كلمة المرور',\n                message: 'تم تغيير كلمة المرور بنجاح',\n                type: 'success',\n                recipientId: user.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تحديث كلمة المرور:', error);\n            return {\n                success: false,\n                error: 'فشل في تحديث كلمة المرور'\n            };\n        }\n    };\n    return {\n        user,\n        loading,\n        isAuthenticated: !!user,\n        login,\n        logout,\n        checkAuth,\n        hasRole,\n        updatePassword\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/appInfo.ts":
/*!******************************!*\
  !*** ./src/utils/appInfo.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_INFO: () => (/* binding */ APP_INFO),\n/* harmony export */   getAppInfo: () => (/* binding */ getAppInfo),\n/* harmony export */   getDeveloperInfo: () => (/* binding */ getDeveloperInfo),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo)\n/* harmony export */ });\n// معلومات التطبيق والمطور\nconst APP_INFO = {\n    name: 'نظام إدارة المدرسة',\n    version: '1.0.0',\n    description: 'نظام شامل لإدارة المدارس باللغة العربية',\n    developer: 'عبيدة العيثاوي',\n    developedBy: 'تم التطوير بواسطة عبيدة العيثاوي',\n    copyright: `© ${new Date().getFullYear()} عبيدة العيثاوي - جميع الحقوق محفوظة`,\n    features: [\n        'إدارة الطلاب والمعلمين',\n        'إدارة الصفوف والمواد الدراسية',\n        'نظام التقييم والدرجات',\n        'تقارير شاملة ومفصلة',\n        'واجهة عربية متجاوبة',\n        'تصميم نيومورفيك عصري'\n    ],\n    technologies: [\n        'Next.js 15',\n        'React 19',\n        'TypeScript',\n        'Tailwind CSS',\n        'localStorage'\n    ],\n    contact: {\n        developer: 'عبيدة العيثاوي',\n        email: '<EMAIL>',\n        phone: '07813332882'\n    }\n};\n// دالة للحصول على معلومات التطبيق\nconst getAppInfo = ()=>APP_INFO;\n// دالة للحصول على معلومات المطور\nconst getDeveloperInfo = ()=>({\n        name: APP_INFO.developer,\n        developedBy: APP_INFO.developedBy,\n        copyright: APP_INFO.copyright,\n        contact: APP_INFO.contact\n    });\n// دالة للحصول على معلومات الإصدار\nconst getVersionInfo = ()=>({\n        version: APP_INFO.version,\n        name: APP_INFO.name,\n        description: APP_INFO.description\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/appInfo.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/localStorage.ts":
/*!***********************************!*\
  !*** ./src/utils/localStorage.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageManager: () => (/* binding */ localStorageManager),\n/* harmony export */   searchUtils: () => (/* binding */ searchUtils)\n/* harmony export */ });\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    STUDENTS: 'school_students',\n    TEACHERS: 'school_teachers',\n    EMPLOYEES: 'school_employees',\n    CLASSES: 'school_classes',\n    SUBJECTS: 'school_subjects',\n    GRADES: 'school_grades',\n    ATTENDANCE: 'school_attendance',\n    USERS: 'school_users',\n    SETTINGS: 'school_settings',\n    EVENTS: 'school_events',\n    NOTIFICATIONS: 'school_notifications',\n    CURRENT_USER: 'school_current_user',\n    ACADEMIC_YEAR: 'school_academic_year',\n    CURRENT_SEMESTER: 'school_current_semester'\n};\n// فئة إدارة التخزين المحلي\nclass LocalStorageManager {\n    // حفظ البيانات\n    setItem(key, data) {\n        try {\n            const serializedData = JSON.stringify(data);\n            localStorage.setItem(key, serializedData);\n        } catch (error) {\n            console.error(`خطأ في حفظ البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // استرجاع البيانات\n    getItem(key) {\n        try {\n            const serializedData = localStorage.getItem(key);\n            if (serializedData === null) {\n                return null;\n            }\n            return JSON.parse(serializedData);\n        } catch (error) {\n            console.error(`خطأ في استرجاع البيانات للمفتاح ${key}:`, error);\n            return null;\n        }\n    }\n    // حذف البيانات\n    removeItem(key) {\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(`خطأ في حذف البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // مسح جميع البيانات\n    clearAll() {\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            this.removeItem(key);\n        });\n    }\n    // === إدارة الطلاب ===\n    getStudents() {\n        return this.getItem(STORAGE_KEYS.STUDENTS) || [];\n    }\n    saveStudents(students) {\n        this.setItem(STORAGE_KEYS.STUDENTS, students);\n    }\n    addStudent(student) {\n        const students = this.getStudents();\n        students.push(student);\n        this.saveStudents(students);\n    }\n    updateStudent(studentId, updatedStudent) {\n        const students = this.getStudents();\n        const index = students.findIndex((s)=>s.id === studentId);\n        if (index !== -1) {\n            students[index] = {\n                ...students[index],\n                ...updatedStudent,\n                updatedAt: new Date()\n            };\n            this.saveStudents(students);\n        }\n    }\n    deleteStudent(studentId) {\n        const students = this.getStudents();\n        const filteredStudents = students.filter((s)=>s.id !== studentId);\n        this.saveStudents(filteredStudents);\n    }\n    getStudentById(studentId) {\n        const students = this.getStudents();\n        return students.find((s)=>s.id === studentId) || null;\n    }\n    // === إدارة المعلمين ===\n    getTeachers() {\n        return this.getItem(STORAGE_KEYS.TEACHERS) || [];\n    }\n    saveTeachers(teachers) {\n        this.setItem(STORAGE_KEYS.TEACHERS, teachers);\n    }\n    addTeacher(teacher) {\n        const teachers = this.getTeachers();\n        teachers.push(teacher);\n        this.saveTeachers(teachers);\n    }\n    updateTeacher(teacherId, updatedTeacher) {\n        const teachers = this.getTeachers();\n        const index = teachers.findIndex((t)=>t.id === teacherId);\n        if (index !== -1) {\n            teachers[index] = {\n                ...teachers[index],\n                ...updatedTeacher,\n                updatedAt: new Date()\n            };\n            this.saveTeachers(teachers);\n        }\n    }\n    deleteTeacher(teacherId) {\n        const teachers = this.getTeachers();\n        const filteredTeachers = teachers.filter((t)=>t.id !== teacherId);\n        this.saveTeachers(filteredTeachers);\n    }\n    getTeacherById(teacherId) {\n        const teachers = this.getTeachers();\n        return teachers.find((t)=>t.id === teacherId) || null;\n    }\n    // === إدارة الموظفين ===\n    getEmployees() {\n        return this.getItem(STORAGE_KEYS.EMPLOYEES) || [];\n    }\n    saveEmployees(employees) {\n        this.setItem(STORAGE_KEYS.EMPLOYEES, employees);\n    }\n    addEmployee(employee) {\n        const employees = this.getEmployees();\n        employees.push(employee);\n        this.saveEmployees(employees);\n    }\n    updateEmployee(employeeId, updatedEmployee) {\n        const employees = this.getEmployees();\n        const index = employees.findIndex((e)=>e.id === employeeId);\n        if (index !== -1) {\n            employees[index] = {\n                ...employees[index],\n                ...updatedEmployee,\n                updatedAt: new Date()\n            };\n            this.saveEmployees(employees);\n        }\n    }\n    deleteEmployee(employeeId) {\n        const employees = this.getEmployees();\n        const filteredEmployees = employees.filter((e)=>e.id !== employeeId);\n        this.saveEmployees(filteredEmployees);\n    }\n    getEmployeeById(employeeId) {\n        const employees = this.getEmployees();\n        return employees.find((e)=>e.id === employeeId) || null;\n    }\n    // === إدارة الصفوف ===\n    getClasses() {\n        return this.getItem(STORAGE_KEYS.CLASSES) || [];\n    }\n    saveClasses(classes) {\n        this.setItem(STORAGE_KEYS.CLASSES, classes);\n    }\n    addClass(classData) {\n        const classes = this.getClasses();\n        classes.push(classData);\n        this.saveClasses(classes);\n    }\n    updateClass(classId, updatedClass) {\n        const classes = this.getClasses();\n        const index = classes.findIndex((c)=>c.id === classId);\n        if (index !== -1) {\n            classes[index] = {\n                ...classes[index],\n                ...updatedClass,\n                updatedAt: new Date()\n            };\n            this.saveClasses(classes);\n        }\n    }\n    deleteClass(classId) {\n        const classes = this.getClasses();\n        const filteredClasses = classes.filter((c)=>c.id !== classId);\n        this.saveClasses(filteredClasses);\n    }\n    getClassById(classId) {\n        const classes = this.getClasses();\n        return classes.find((c)=>c.id === classId) || null;\n    }\n    // === إدارة المواد ===\n    getSubjects() {\n        return this.getItem(STORAGE_KEYS.SUBJECTS) || [];\n    }\n    saveSubjects(subjects) {\n        this.setItem(STORAGE_KEYS.SUBJECTS, subjects);\n    }\n    addSubject(subject) {\n        const subjects = this.getSubjects();\n        subjects.push(subject);\n        this.saveSubjects(subjects);\n    }\n    updateSubject(subjectId, updatedSubject) {\n        const subjects = this.getSubjects();\n        const index = subjects.findIndex((s)=>s.id === subjectId);\n        if (index !== -1) {\n            subjects[index] = {\n                ...subjects[index],\n                ...updatedSubject,\n                updatedAt: new Date()\n            };\n            this.saveSubjects(subjects);\n        }\n    }\n    deleteSubject(subjectId) {\n        const subjects = this.getSubjects();\n        const filteredSubjects = subjects.filter((s)=>s.id !== subjectId);\n        this.saveSubjects(filteredSubjects);\n    }\n    getSubjectById(subjectId) {\n        const subjects = this.getSubjects();\n        return subjects.find((s)=>s.id === subjectId) || null;\n    }\n    // === إدارة الدرجات ===\n    getGrades() {\n        return this.getItem(STORAGE_KEYS.GRADES) || [];\n    }\n    saveGrades(grades) {\n        this.setItem(STORAGE_KEYS.GRADES, grades);\n    }\n    addGrade(grade) {\n        const grades = this.getGrades();\n        grades.push(grade);\n        this.saveGrades(grades);\n    }\n    updateGrade(gradeId, updatedGrade) {\n        const grades = this.getGrades();\n        const index = grades.findIndex((g)=>g.id === gradeId);\n        if (index !== -1) {\n            grades[index] = {\n                ...grades[index],\n                ...updatedGrade,\n                updatedAt: new Date()\n            };\n            this.saveGrades(grades);\n        }\n    }\n    deleteGrade(gradeId) {\n        const grades = this.getGrades();\n        const filteredGrades = grades.filter((g)=>g.id !== gradeId);\n        this.saveGrades(filteredGrades);\n    }\n    getGradesByStudent(studentId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.studentId === studentId);\n    }\n    getGradesByClass(classId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.classId === classId);\n    }\n    // === إدارة الحضور ===\n    getAttendance() {\n        return this.getItem(STORAGE_KEYS.ATTENDANCE) || [];\n    }\n    saveAttendance(attendance) {\n        this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);\n    }\n    addAttendance(attendance) {\n        const attendanceRecords = this.getAttendance();\n        attendanceRecords.push(attendance);\n        this.saveAttendance(attendanceRecords);\n    }\n    updateAttendance(attendanceId, updatedAttendance) {\n        const attendanceRecords = this.getAttendance();\n        const index = attendanceRecords.findIndex((a)=>a.id === attendanceId);\n        if (index !== -1) {\n            attendanceRecords[index] = {\n                ...attendanceRecords[index],\n                ...updatedAttendance,\n                updatedAt: new Date()\n            };\n            this.saveAttendance(attendanceRecords);\n        }\n    }\n    getAttendanceByStudent(studentId) {\n        const attendance = this.getAttendance();\n        return attendance.filter((a)=>a.studentId === studentId);\n    }\n    getAttendanceByDate(date) {\n        const attendance = this.getAttendance();\n        const dateString = date.toDateString();\n        return attendance.filter((a)=>new Date(a.date).toDateString() === dateString);\n    }\n    // === إدارة المستخدمين ===\n    getUsers() {\n        return this.getItem(STORAGE_KEYS.USERS) || [];\n    }\n    saveUsers(users) {\n        this.setItem(STORAGE_KEYS.USERS, users);\n    }\n    getCurrentUser() {\n        return this.getItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    setCurrentUser(user) {\n        this.setItem(STORAGE_KEYS.CURRENT_USER, user);\n    }\n    logout() {\n        this.removeItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    // === إدارة الإعدادات ===\n    getSettings() {\n        return this.getItem(STORAGE_KEYS.SETTINGS);\n    }\n    saveSettings(settings) {\n        this.setItem(STORAGE_KEYS.SETTINGS, settings);\n    }\n    // === إدارة الأحداث ===\n    getEvents() {\n        return this.getItem(STORAGE_KEYS.EVENTS) || [];\n    }\n    saveEvents(events) {\n        this.setItem(STORAGE_KEYS.EVENTS, events);\n    }\n    addEvent(event) {\n        const events = this.getEvents();\n        events.push(event);\n        this.saveEvents(events);\n    }\n    // === إدارة الإشعارات ===\n    getNotifications() {\n        return this.getItem(STORAGE_KEYS.NOTIFICATIONS) || [];\n    }\n    saveNotifications(notifications) {\n        this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);\n    }\n    addNotification(notification) {\n        const notifications = this.getNotifications();\n        notifications.push(notification);\n        this.saveNotifications(notifications);\n    }\n    markNotificationAsRead(notificationId) {\n        const notifications = this.getNotifications();\n        const index = notifications.findIndex((n)=>n.id === notificationId);\n        if (index !== -1) {\n            notifications[index].isRead = true;\n            notifications[index].readAt = new Date();\n            this.saveNotifications(notifications);\n        }\n    }\n}\n// إنشاء مثيل واحد للاستخدام في التطبيق\nconst localStorageManager = new LocalStorageManager();\n// دوال مساعدة للبحث والتصفية\nconst searchUtils = {\n    searchStudents: (query, students)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return students.filter((student)=>student.name.toLowerCase().includes(lowercaseQuery) || student.studentId.toLowerCase().includes(lowercaseQuery) || student.email?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchTeachers: (query, teachers)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return teachers.filter((teacher)=>teacher.name?.toLowerCase().includes(lowercaseQuery) || teacher.fullName?.toLowerCase().includes(lowercaseQuery) || teacher.teacherId?.toLowerCase().includes(lowercaseQuery) || teacher.serialNumber?.toLowerCase().includes(lowercaseQuery) || teacher.email?.toLowerCase().includes(lowercaseQuery) || teacher.specialization?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchEmployees: (query, employees)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return employees.filter((employee)=>employee.name?.toLowerCase().includes(lowercaseQuery) || employee.fullName?.toLowerCase().includes(lowercaseQuery) || employee.employeeId?.toLowerCase().includes(lowercaseQuery) || employee.serialNumber?.toLowerCase().includes(lowercaseQuery) || employee.email?.toLowerCase().includes(lowercaseQuery) || employee.department?.toLowerCase().includes(lowercaseQuery) || employee.position?.toLowerCase().includes(lowercaseQuery));\n    },\n    filterStudentsByClass: (classId, students)=>{\n        return students.filter((student)=>student.classId === classId);\n    },\n    filterGradesByDateRange: (startDate, endDate, grades)=>{\n        return grades.filter((grade)=>{\n            const gradeDate = new Date(grade.examDate);\n            return gradeDate >= startDate && gradeDate <= endDate;\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/localStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/sampleData.ts":
/*!*********************************!*\
  !*** ./src/utils/sampleData.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeSampleData: () => (/* binding */ initializeSampleData)\n/* harmony export */ });\n/* harmony import */ var _localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n\n// إنشاء بيانات تجريبية للنظام\nconst initializeSampleData = ()=>{\n    // التحقق من وجود بيانات مسبقة\n    const existingUsers = _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.getUsers();\n    if (existingUsers.length > 0) {\n        return; // البيانات موجودة بالفعل\n    }\n    // إنشاء المستخدمين\n    const users = [\n        {\n            id: 'user-1',\n            name: 'عبيدة العيثاوي',\n            username: 'obeida',\n            email: '<EMAIL>',\n            password: '12345',\n            role: 'developer',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'user-2',\n            name: 'admin',\n            username: 'admin',\n            email: '<EMAIL>',\n            password: 'admin',\n            role: 'admin',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المواد الدراسية\n    const subjects = [\n        {\n            id: 'subject-1',\n            name: 'الرياضيات',\n            code: 'MATH101',\n            description: 'مادة الرياضيات للصف الأول',\n            grade: 1,\n            credits: 4,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-2',\n            name: 'اللغة العربية',\n            code: 'ARAB101',\n            description: 'مادة اللغة العربية للصف الأول',\n            grade: 1,\n            credits: 5,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-3',\n            name: 'العلوم',\n            code: 'SCI101',\n            description: 'مادة العلوم للصف الأول',\n            grade: 1,\n            credits: 3,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-4',\n            name: 'التاريخ',\n            code: 'HIST101',\n            description: 'مادة التاريخ للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-5',\n            name: 'الجغرافيا',\n            code: 'GEO101',\n            description: 'مادة الجغرافيا للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المعلمين (بيانات تجريبية)\n    const teachers = [\n        {\n            id: 'teacher-1',\n            serialNumber: '001',\n            fullName: 'فاطمة أحمد محمد علي',\n            shortName: 'زينب حسن محمد',\n            title: 'أستاذة',\n            specialization: 'الرياضيات',\n            firstAppointmentDate: new Date('2018-09-01'),\n            schoolStartDate: new Date('2020-09-01'),\n            dateOfBirth: new Date('1985-05-15'),\n            address: 'بغداد - الكرادة - شارع الجامعة',\n            phone: '07901234567',\n            employmentType: 'permanent',\n            status: 'active',\n            subjects: [\n                'subject-1'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '001',\n            name: 'فاطمة أحمد محمد',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس رياضيات',\n            experience: 8,\n            salary: 800000,\n            hireDate: new Date('2020-09-01')\n        },\n        {\n            id: 'teacher-2',\n            serialNumber: '002',\n            fullName: 'محمد علي حسن الأستاذ',\n            shortName: 'فاطمة أحمد علي',\n            title: 'أستاذ',\n            specialization: 'اللغة العربية',\n            firstAppointmentDate: new Date('2016-09-01'),\n            schoolStartDate: new Date('2018-09-01'),\n            dateOfBirth: new Date('1980-03-20'),\n            address: 'بغداد - الجادرية - المنطقة الثانية',\n            phone: '07801234567',\n            employmentType: 'assignment',\n            status: 'active',\n            subjects: [\n                'subject-2'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '002',\n            name: 'محمد علي حسن الأستاذ',\n            email: '<EMAIL>',\n            gender: 'male',\n            qualification: 'ماجستير لغة عربية',\n            experience: 12,\n            salary: 900000,\n            hireDate: new Date('2018-09-01')\n        },\n        {\n            id: 'teacher-3',\n            serialNumber: '003',\n            fullName: 'سارة خالد أحمد المدرسة',\n            shortName: 'مريم حسين محمد',\n            title: 'مدرسة',\n            specialization: 'العلوم',\n            firstAppointmentDate: new Date('2021-09-01'),\n            schoolStartDate: new Date('2021-09-01'),\n            dateOfBirth: new Date('1990-08-12'),\n            address: 'بغداد - الكاظمية - حي الأطباء',\n            phone: '07701234567',\n            employmentType: 'contract',\n            status: 'active',\n            subjects: [\n                'subject-3'\n            ],\n            classes: [\n                'class-2'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '003',\n            name: 'سارة خالد أحمد المدرسة',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس علوم',\n            experience: 3,\n            salary: 700000,\n            hireDate: new Date('2021-09-01')\n        }\n    ];\n    // إنشاء الصفوف\n    const classes = [\n        {\n            id: 'class-1',\n            name: 'الصف الأول أ',\n            grade: 1,\n            section: 'أ',\n            capacity: 30,\n            currentStudents: 20,\n            classTeacherId: 'teacher-1',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'class-2',\n            name: 'الصف الأول ب',\n            grade: 1,\n            section: 'ب',\n            capacity: 30,\n            currentStudents: 18,\n            classTeacherId: 'teacher-2',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الطلاب (بيانات تجريبية)\n    const students = [\n        {\n            id: 'student-1',\n            studentId: 'S001',\n            name: 'علي أحمد محمد',\n            email: '<EMAIL>',\n            phone: '07701234567',\n            dateOfBirth: new Date('2012-01-15'),\n            gender: 'male',\n            address: 'بغداد - الكرادة - شارع الرشيد',\n            parentName: 'أحمد محمد علي',\n            parentPhone: '07901234567',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالب متفوق',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-2',\n            studentId: 'S002',\n            name: 'فاطمة حسن علي',\n            email: '<EMAIL>',\n            phone: '07701234568',\n            dateOfBirth: new Date('2012-03-20'),\n            gender: 'female',\n            address: 'بغداد - الجادرية - شارع الجامعة',\n            parentName: 'حسن علي محمد',\n            parentPhone: '07901234568',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'حساسية من الفول السوداني',\n            notes: 'طالبة نشيطة',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-3',\n            studentId: 'S003',\n            name: 'زينب محمد حسن',\n            email: '<EMAIL>',\n            phone: '07701234570',\n            dateOfBirth: new Date('2012-07-25'),\n            gender: 'female',\n            address: 'بغداد - الكاظمية - شارع الإمام',\n            parentName: 'محمد حسن علي',\n            parentPhone: '07901234570',\n            parentEmail: '<EMAIL>',\n            classId: 'class-2',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالبة متميزة في اللغة العربية',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الإعدادات\n    const settings = {\n        id: 'settings-1',\n        schoolName: 'مدرسة النور الابتدائية',\n        address: 'بغداد - الكرادة - شارع الرشيد',\n        phone: '07901234567',\n        email: '<EMAIL>',\n        website: 'www.alnoor-school.edu.iq',\n        academicYear: '2024-2025',\n        currentSemester: 'first',\n        gradeSystem: 'percentage',\n        attendanceRequired: true,\n        maxAbsences: 10,\n        workingDays: [\n            'sunday',\n            'monday',\n            'tuesday',\n            'wednesday',\n            'thursday'\n        ],\n        schoolStartTime: '08:00',\n        schoolEndTime: '14:00',\n        updatedAt: new Date()\n    };\n    // حفظ البيانات في localStorage\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSubjects(subjects);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveTeachers(teachers);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveClasses(classes);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveStudents(students);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSettings(settings);\n    console.log('تم إنشاء البيانات التجريبية بنجاح');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/sampleData.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();