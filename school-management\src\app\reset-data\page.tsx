'use client';

import React, { useState } from 'react';
import Button from '@/components/Button';
import Card from '@/components/Card';
import { resetSampleData, createDeveloperAccount } from '@/utils/sampleData';
import { localStorageManager } from '@/utils/localStorage';

export default function ResetDataPage() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');
  
  // بيانات إنشاء حساب المطور
  const [developerData, setDeveloperData] = useState({
    name: 'عبيدة العيثاوي',
    username: 'obeida',
    email: '<EMAIL>',
    password: '12345'
  });

  const handleResetData = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم حذف جميع البيانات الموجودة!')) {
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      // إعادة تعيين البيانات
      resetSampleData();
      
      setMessage('تم إعادة تعيين البيانات بنجاح! يمكنك الآن تسجيل الدخول باستخدام البيانات الافتراضية.');
      setMessageType('success');
      
      // إعادة توجيه إلى صفحة تسجيل الدخول بعد 3 ثوان
      setTimeout(() => {
        window.location.href = '/login';
      }, 3000);
      
    } catch (error) {
      console.error('خطأ في إعادة تعيين البيانات:', error);
      setMessage('حدث خطأ أثناء إعادة تعيين البيانات');
      setMessageType('error');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateDeveloper = async () => {
    if (!developerData.username || !developerData.email || !developerData.password) {
      setMessage('يرجى ملء جميع الحقول المطلوبة');
      setMessageType('error');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      // إنشاء حساب المطور
      createDeveloperAccount(
        developerData.username,
        developerData.email,
        developerData.password,
        developerData.name
      );
      
      setMessage(`تم إنشاء حساب المطور بنجاح!\nاسم المستخدم: ${developerData.username}\nكلمة المرور: ${developerData.password}`);
      setMessageType('success');
      
      // إعادة توجيه إلى صفحة تسجيل الدخول بعد 3 ثوان
      setTimeout(() => {
        window.location.href = '/login';
      }, 3000);
      
    } catch (error) {
      console.error('خطأ في إنشاء حساب المطور:', error);
      setMessage('حدث خطأ أثناء إنشاء حساب المطور');
      setMessageType('error');
    } finally {
      setLoading(false);
    }
  };

  const showCurrentUsers = () => {
    const users = localStorageManager.getUsers();
    if (users.length === 0) {
      setMessage('لا يوجد مستخدمين في النظام');
      setMessageType('error');
    } else {
      const usersList = users.map(u => `${u.name} (${u.username || u.email}) - ${u.role}`).join('\n');
      setMessage(`المستخدمين الموجودين:\n${usersList}`);
      setMessageType('success');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-6">
        
        {/* العنوان */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">إعادة تعيين البيانات</h1>
          <p className="text-gray-600">أدوات لإعادة تعيين البيانات وإنشاء حساب مطور جديد</p>
        </div>

        {/* عرض المستخدمين الحاليين */}
        <Card>
          <h2 className="text-xl font-bold mb-4">المستخدمين الحاليين</h2>
          <Button onClick={showCurrentUsers} variant="secondary" fullWidth>
            عرض المستخدمين الموجودين
          </Button>
        </Card>

        {/* إنشاء حساب مطور */}
        <Card>
          <h2 className="text-xl font-bold mb-4">إنشاء/تحديث حساب المطور</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الاسم</label>
              <input
                type="text"
                value={developerData.name}
                onChange={(e) => setDeveloperData({...developerData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="اسم المطور"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
              <input
                type="text"
                value={developerData.username}
                onChange={(e) => setDeveloperData({...developerData, username: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="اسم المستخدم"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
              <input
                type="email"
                value={developerData.email}
                onChange={(e) => setDeveloperData({...developerData, email: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="البريد الإلكتروني"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
              <input
                type="text"
                value={developerData.password}
                onChange={(e) => setDeveloperData({...developerData, password: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="كلمة المرور"
              />
            </div>
            
            <Button 
              onClick={handleCreateDeveloper} 
              variant="primary" 
              fullWidth
              loading={loading}
            >
              إنشاء/تحديث حساب المطور
            </Button>
          </div>
        </Card>

        {/* إعادة تعيين البيانات */}
        <Card>
          <h2 className="text-xl font-bold mb-4 text-red-600">إعادة تعيين جميع البيانات</h2>
          <p className="text-gray-600 mb-4">
            سيتم حذف جميع البيانات الموجودة وإنشاء بيانات تجريبية جديدة تتضمن:
          </p>
          <ul className="list-disc list-inside text-gray-600 mb-4 space-y-1">
            <li>المطور: obeida / 12345</li>
            <li>الأدمن: admin / admin</li>
            <li>بيانات تجريبية للطلاب والمعلمين</li>
          </ul>
          <Button 
            onClick={handleResetData} 
            variant="danger" 
            fullWidth
            loading={loading}
          >
            إعادة تعيين جميع البيانات
          </Button>
        </Card>

        {/* رسالة النتيجة */}
        {message && (
          <Card className={messageType === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <div className={`text-sm whitespace-pre-line ${messageType === 'success' ? 'text-green-700' : 'text-red-700'}`}>
              {message}
            </div>
          </Card>
        )}

        {/* رابط العودة */}
        <div className="text-center">
          <a 
            href="/login" 
            className="text-blue-600 hover:text-blue-800 underline"
          >
            العودة إلى صفحة تسجيل الدخول
          </a>
        </div>

      </div>
    </div>
  );
}
