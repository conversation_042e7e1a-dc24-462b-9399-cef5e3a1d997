// نوع المستخدم
export interface User {
  id: string;
  name: string;
  username?: string; // اسم المستخدم
  email: string;
  password?: string; // كلمة المرور (اختيارية للأمان)
  role: 'developer' | 'admin' | 'teacher' | 'student' | 'parent';
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// نوع الطالب
export interface Student {
  id: string;
  studentId: string; // رقم الطالب
  name: string;
  motherName?: string; // اسم الأم الثلاثي
  email?: string;
  phone?: string;
  dateOfBirth: Date;
  gender: 'male' | 'female';
  address: string;
  parentName: string;
  parentPhone: string;
  parentEmail?: string;
  classId: string;
  enrollmentDate: Date;
  status: 'active' | 'inactive' | 'graduated' | 'transferred';
  avatar?: string;
  medicalInfo?: string;
  notes?: string;
  emergencyContact?: string;
  createdAt: Date;
  updatedAt: Date;
}

// نوع المعلم
export interface Teacher {
  id: string;
  serialNumber: string; // التسلسل
  fullName: string; // الاسم الرباعي
  shortName?: string; // اسم الأم الثلاثي
  title: string; // اللقب
  specialization: string; // الاختصاص
  firstAppointmentDate: Date; // تاريخ أول تعيين
  schoolStartDate: Date; // تاريخ المباشرة في المدرسة
  dateOfBirth: Date; // تاريخ التولد
  address: string; // عنوان السكن
  phone: string; // رقم الهاتف
  employmentType: 'permanent' | 'assignment' | 'contract'; // دائم / تنسيب / عقد
  status: 'active' | 'inactive' | 'on_leave';
  subjects: string[]; // المواد التي يدرسها
  classes: string[]; // الصفوف التي يدرسها
  avatar?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;

  // حقول اختيارية للتوافق مع النظام القديم
  teacherId?: string; // للتوافق مع الكود القديم
  name?: string; // للتوافق مع الكود القديم
  email?: string;
  gender?: 'male' | 'female';
  qualification?: string;
  experience?: number;
  salary?: number;
  hireDate?: Date;
}

// نوع الموظف
export interface Employee {
  id: string;
  serialNumber: string; // التسلسل
  fullName: string; // الاسم الرباعي
  shortName?: string; // اسم الأم الثلاثي
  title: string; // اللقب
  department: string; // القسم أو الإدارة
  position: string; // المنصب الوظيفي
  firstAppointmentDate: Date; // تاريخ أول تعيين
  schoolStartDate: Date; // تاريخ المباشرة في المدرسة
  dateOfBirth: Date; // تاريخ التولد
  address: string; // عنوان السكن
  phone: string; // رقم الهاتف
  employmentType: 'permanent' | 'assignment' | 'contract'; // دائم / تنسيب / عقد
  status: 'active' | 'inactive' | 'on_leave';
  responsibilities: string[]; // المسؤوليات
  avatar?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;

  // حقول اختيارية للتوافق مع النظام القديم
  employeeId?: string; // للتوافق مع الكود القديم
  name?: string; // للتوافق مع الكود القديم
  email?: string;
  gender?: 'male' | 'female';
  qualification?: string;
  experience?: number;
  salary?: number;
  hireDate?: Date;
}

// نوع الصف
export interface Class {
  id: string;
  name: string; // اسم الصف (مثل: الصف الأول أ)
  grade: number; // المرحلة (1-12)
  section: string; // الشعبة (أ، ب، ج)
  capacity: number; // السعة القصوى
  currentStudents: number; // عدد الطلاب الحاليين
  classTeacherId?: string; // معلم الصف (اختياري)
  subjects: string[]; // المواد المدرسة
  schedule: ClassSchedule[];
  academicYear: string;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

// نوع جدول الحصص
export interface ClassSchedule {
  id: string;
  classId: string;
  subjectId: string;
  teacherId: string;
  dayOfWeek: 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday';
  startTime: string; // HH:MM
  endTime: string; // HH:MM
  room?: string;
}

// نوع المادة
export interface Subject {
  id: string;
  name: string;
  code: string; // رمز المادة
  description?: string;
  grade: number; // المرحلة
  credits: number; // عدد الساعات
  type: 'core' | 'elective' | 'activity'; // أساسية، اختيارية، نشاط
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

// نوع الدرجات
export interface Grade {
  id: string;
  studentId: string;
  subjectId: string;
  teacherId: string;
  classId: string;
  academicYear: string;
  semester: 'first' | 'second';
  examType: 'midterm' | 'final' | 'quiz' | 'assignment' | 'participation';
  score: number;
  maxScore: number;
  percentage: number;
  grade: string; // A, B, C, D, F
  examDate: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// نوع الحضور
export interface Attendance {
  id: string;
  studentId: string;
  classId: string;
  date: Date;
  status: 'present' | 'absent' | 'late' | 'excused';
  checkInTime?: string;
  checkOutTime?: string;
  notes?: string;
  recordedBy: string; // معرف المعلم الذي سجل الحضور
  createdAt: Date;
  updatedAt: Date;
}

// نوع التقرير
export interface Report {
  id: string;
  title: string;
  type: 'student' | 'class' | 'teacher' | 'attendance' | 'grades' | 'financial';
  description?: string;
  data: any; // بيانات التقرير
  generatedBy: string; // معرف المستخدم الذي أنشأ التقرير
  generatedAt: Date;
  filters?: ReportFilters;
}

// نوع مرشحات التقرير
export interface ReportFilters {
  dateFrom?: Date;
  dateTo?: Date;
  classId?: string;
  subjectId?: string;
  teacherId?: string;
  studentId?: string;
  academicYear?: string;
  semester?: 'first' | 'second';
}

// نوع الإشعار
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  recipientId: string;
  recipientType: 'user' | 'class' | 'all';
  isRead: boolean;
  createdAt: Date;
  readAt?: Date;
}

// نوع الحدث
export interface Event {
  id: string;
  title: string;
  description?: string;
  type: 'exam' | 'holiday' | 'meeting' | 'activity' | 'other';
  startDate: Date;
  endDate: Date;
  location?: string;
  participants?: string[]; // معرفات المشاركين
  createdBy: string;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

// نوع الإعدادات
export interface Settings {
  id: string;
  schoolName: string;
  schoolLogo?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  academicYear: string;
  currentSemester: 'first' | 'second';
  gradeSystem: 'percentage' | 'letter' | 'points';
  attendanceRequired: boolean;
  maxAbsences: number;
  workingDays: string[];
  schoolStartTime: string;
  schoolEndTime: string;
  updatedAt: Date;
}

// نوع إحصائيات لوحة التحكم
export interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalEmployees: number;
  totalSubjects: number;
  presentToday: number;
  absentToday: number;
  upcomingExams: number;
  recentGrades: Grade[];
  attendanceRate: number;
  topPerformers: Student[];
}

// نوع استجابة API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// نوع معايير البحث
export interface SearchCriteria {
  query?: string;
  filters?: {
    [key: string]: any;
  };
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// نوع نتائج البحث
export interface SearchResults<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
