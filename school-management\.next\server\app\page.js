/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e7050786830a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc2YbYuNin2YUg2YXYr9ix2LPYqVxcc2Nob29sLW1hbmFnZW1lbnRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU3MDUwNzg2ODMwYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"نظام إدارة المدرسة\",\n    description: \"نظام شامل لإدارة المدارس باللغة العربية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\نظام مدرسة\\school-management\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"(ssr)/./src/components/Layout.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./src/components/Card.tsx\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Button */ \"(ssr)/./src/components/Button.tsx\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* harmony import */ var _utils_sampleData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/sampleData */ \"(ssr)/./src/utils/sampleData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Dashboard() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        totalTeachers: 0,\n        totalEmployees: 0,\n        totalSubjects: 0,\n        presentToday: 0,\n        absentToday: 0,\n        upcomingExams: 0,\n        recentGrades: [],\n        attendanceRate: 0,\n        topPerformers: []\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [schoolName, setSchoolName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('نظام إدارة المدرسة');\n    const [schoolLogo, setSchoolLogo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // تحميل البيانات التجريبية إذا لم تكن موجودة\n            (0,_utils_sampleData__WEBPACK_IMPORTED_MODULE_6__.initializeSampleData)();\n            // التحقق من تسجيل الدخول\n            const user = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getCurrentUser();\n            if (!user) {\n                window.location.href = '/login';\n                return;\n            }\n            setCurrentUser(user);\n            // تحميل اسم المدرسة والشعار من الإعدادات\n            const settings = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getSettings();\n            if (settings) {\n                if (settings.schoolName) {\n                    setSchoolName(settings.schoolName);\n                }\n                if (settings.schoolLogo) {\n                    setSchoolLogo(settings.schoolLogo);\n                }\n            }\n            loadDashboardData();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // الاستماع لتحديثات الإعدادات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const handleSettingsUpdate = {\n                \"Dashboard.useEffect.handleSettingsUpdate\": (event)=>{\n                    const newSettings = event.detail;\n                    if (newSettings) {\n                        if (newSettings.schoolName) {\n                            setSchoolName(newSettings.schoolName);\n                        }\n                        if (newSettings.schoolLogo) {\n                            setSchoolLogo(newSettings.schoolLogo);\n                        } else if (newSettings.schoolLogo === undefined) {\n                            setSchoolLogo(null);\n                        }\n                    }\n                }\n            }[\"Dashboard.useEffect.handleSettingsUpdate\"];\n            window.addEventListener('settingsUpdated', handleSettingsUpdate);\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    window.removeEventListener('settingsUpdated', handleSettingsUpdate);\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const loadDashboardData = ()=>{\n        try {\n            const students = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getStudents();\n            const teachers = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getTeachers();\n            const employees = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getEmployees();\n            const subjects = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getSubjects();\n            const grades = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getGrades();\n            const attendance = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getAttendance();\n            // حساب الحضور اليوم\n            const today = new Date();\n            const todayAttendance = attendance.filter((a)=>new Date(a.date).toDateString() === today.toDateString());\n            const presentToday = todayAttendance.filter((a)=>a.status === 'present').length;\n            const absentToday = todayAttendance.filter((a)=>a.status === 'absent').length;\n            // حساب معدل الحضور\n            const totalAttendanceRecords = attendance.length;\n            const presentRecords = attendance.filter((a)=>a.status === 'present').length;\n            const attendanceRate = totalAttendanceRecords > 0 ? Math.round(presentRecords / totalAttendanceRecords * 100) : 0;\n            // أحدث الدرجات\n            const recentGrades = grades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5);\n            // أفضل الطلاب (حسب متوسط الدرجات)\n            const studentGrades = students.map((student)=>{\n                const studentGradesList = grades.filter((g)=>g.studentId === student.id);\n                const average = studentGradesList.length > 0 ? studentGradesList.reduce((sum, g)=>sum + g.percentage, 0) / studentGradesList.length : 0;\n                return {\n                    ...student,\n                    average\n                };\n            });\n            const topPerformers = studentGrades.filter((s)=>s.average > 0).sort((a, b)=>b.average - a.average).slice(0, 5);\n            setStats({\n                totalStudents: students.length,\n                totalTeachers: teachers.length,\n                totalEmployees: employees.length,\n                totalSubjects: subjects.length,\n                presentToday,\n                absentToday,\n                upcomingExams: 0,\n                recentGrades,\n                attendanceRate,\n                topPerformers\n            });\n            setLoading(false);\n        } catch (error) {\n            console.error('خطأ في تحميل بيانات لوحة التحكم:', error);\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"جاري تحميل البيانات...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16 relative\",\n                    children: [\n                        schoolLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: schoolLogo,\n                            alt: `شعار ${schoolName}`,\n                            className: \"absolute right-0 top-0 w-24 h-24 object-contain rounded-xl shadow-lg bg-white p-2 border-2 border-gray-100\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-1\",\n                            children: [\n                                \"مرحباً بك \",\n                                currentUser?.name || 'المستخدم',\n                                \" في \",\n                                schoolName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"لوحة التحكم الرئيسية - نظرة عامة على أداء المدرسة\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105\",\n                            hoverable: true,\n                            onClick: ()=>window.location.href = '/students',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm font-medium\",\n                                                children: \"إجمالي الطلاب\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900\",\n                                                children: stats.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl\",\n                                        children: \"\\uD83D\\uDC68‍\\uD83C\\uDF93\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105\",\n                            hoverable: true,\n                            onClick: ()=>window.location.href = '/teachers',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 text-sm font-medium\",\n                                                children: \"إجمالي المعلمين\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900\",\n                                                children: stats.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl\",\n                                        children: \"\\uD83D\\uDC68‍\\uD83C\\uDFEB\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105\",\n                            hoverable: true,\n                            onClick: ()=>window.location.href = '/employees',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-600 text-sm font-medium\",\n                                                children: \"إجمالي الموظفين\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-900\",\n                                                children: stats.totalEmployees\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl\",\n                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            hoverable: true,\n                            className: \"text-center\",\n                            onClick: ()=>window.location.href = '/students',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDC68‍\\uD83C\\uDF93\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"إدارة الطلاب\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"إضافة وتعديل وإدارة بيانات الطلاب\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"primary\",\n                                    fullWidth: true,\n                                    children: \"إدارة الطلاب\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            hoverable: true,\n                            className: \"text-center\",\n                            onClick: ()=>window.location.href = '/teachers',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDC68‍\\uD83C\\uDFEB\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"إدارة المعلمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"إضافة وتعديل وإدارة بيانات المعلمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"success\",\n                                    fullWidth: true,\n                                    children: \"إدارة المعلمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            hoverable: true,\n                            className: \"text-center\",\n                            onClick: ()=>window.location.href = '/employees',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"إدارة الموظفين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"إدارة وتتبع بيانات الموظفين الإداريين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"info\",\n                                    fullWidth: true,\n                                    children: \"إدارة الموظفين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            hoverable: true,\n                            className: \"text-center\",\n                            onClick: ()=>window.location.href = '/grades',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDCCA\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"إدارة الدرجات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"تسجيل ومتابعة درجات الطلاب\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"info\",\n                                    fullWidth: true,\n                                    children: \"إدارة الدرجات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            hoverable: true,\n                            className: \"text-center\",\n                            onClick: ()=>window.location.href = '/users',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"إدارة المستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"إنشاء وإدارة حسابات المستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"warning\",\n                                    fullWidth: true,\n                                    children: \"إدارة المستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            hoverable: true,\n                            className: \"text-center\",\n                            onClick: ()=>window.location.href = '/reports',\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDCC8\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"التقارير\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"إنشاء وعرض التقارير المختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"secondary\",\n                                    fullWidth: true,\n                                    children: \"عرض التقارير\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Button = ({ children, onClick, type = 'button', variant = 'primary', size = 'md', disabled = false, loading = false, fullWidth = false, className = '', icon, iconPosition = 'right', title })=>{\n    const baseClasses = `\n    inline-flex items-center justify-center font-medium rounded-lg\n    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${fullWidth ? 'w-full' : ''}\n  `;\n    const sizeClasses = {\n        sm: 'px-3 py-2 text-sm',\n        md: 'px-4 py-2.5 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    const variantClasses = {\n        primary: `\n      bg-gradient-to-r from-blue-500 to-purple-600 text-white\n      hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        secondary: `\n      bg-gray-100 text-gray-700 border border-gray-300\n      hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500\n      shadow-md hover:shadow-lg\n    `,\n        success: `\n      bg-gradient-to-r from-green-500 to-emerald-600 text-white\n      hover:from-green-600 hover:to-emerald-700 focus:ring-green-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        danger: `\n      bg-gradient-to-r from-red-500 to-pink-600 text-white\n      hover:from-red-600 hover:to-pink-700 focus:ring-red-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        warning: `\n      bg-gradient-to-r from-yellow-500 to-orange-600 text-white\n      hover:from-yellow-600 hover:to-orange-700 focus:ring-yellow-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        info: `\n      bg-gradient-to-r from-cyan-500 to-blue-600 text-white\n      hover:from-cyan-600 hover:to-blue-700 focus:ring-cyan-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `\n    };\n    const combinedClasses = `\n    ${baseClasses}\n    ${sizeClasses[size]}\n    ${variantClasses[variant]}\n    ${className}\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled || loading,\n        className: combinedClasses,\n        title: title,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-current\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            icon && iconPosition === 'right' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            icon && iconPosition === 'left' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9CdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUUwQjtBQWlCMUIsTUFBTUMsU0FBZ0MsQ0FBQyxFQUNyQ0MsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLE9BQU8sUUFBUSxFQUNmQyxVQUFVLFNBQVMsRUFDbkJDLE9BQU8sSUFBSSxFQUNYQyxXQUFXLEtBQUssRUFDaEJDLFVBQVUsS0FBSyxFQUNmQyxZQUFZLEtBQUssRUFDakJDLFlBQVksRUFBRSxFQUNkQyxJQUFJLEVBQ0pDLGVBQWUsT0FBTyxFQUN0QkMsS0FBSyxFQUNOO0lBQ0MsTUFBTUMsY0FBYyxDQUFDOzs7O0lBSW5CLEVBQUVMLFlBQVksV0FBVyxHQUFHO0VBQzlCLENBQUM7SUFFRCxNQUFNTSxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCQyxTQUFTLENBQUM7Ozs7SUFJVixDQUFDO1FBQ0RDLFdBQVcsQ0FBQzs7OztJQUlaLENBQUM7UUFDREMsU0FBUyxDQUFDOzs7O0lBSVYsQ0FBQztRQUNEQyxRQUFRLENBQUM7Ozs7SUFJVCxDQUFDO1FBQ0RDLFNBQVMsQ0FBQzs7OztJQUlWLENBQUM7UUFDREMsTUFBTSxDQUFDOzs7O0lBSVAsQ0FBQztJQUNIO0lBRUEsTUFBTUMsa0JBQWtCLENBQUM7SUFDdkIsRUFBRVosWUFBWTtJQUNkLEVBQUVDLFdBQVcsQ0FBQ1QsS0FBSyxDQUFDO0lBQ3BCLEVBQUVhLGNBQWMsQ0FBQ2QsUUFBUSxDQUFDO0lBQzFCLEVBQUVLLFVBQVU7RUFDZCxDQUFDO0lBRUQscUJBQ0UsOERBQUNpQjtRQUNDdkIsTUFBTUE7UUFDTkQsU0FBU0E7UUFDVEksVUFBVUEsWUFBWUM7UUFDdEJFLFdBQVdnQjtRQUNYYixPQUFPQTs7WUFFTkwseUJBQ0MsOERBQUNvQjtnQkFDQ2xCLFdBQVU7Z0JBQ1ZtQixPQUFNO2dCQUNOQyxNQUFLO2dCQUNMQyxTQUFROztrQ0FFUiw4REFBQ0M7d0JBQ0N0QixXQUFVO3dCQUNWdUIsSUFBRzt3QkFDSEMsSUFBRzt3QkFDSEMsR0FBRTt3QkFDRkMsUUFBTzt3QkFDUEMsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDQzt3QkFDQzVCLFdBQVU7d0JBQ1ZvQixNQUFLO3dCQUNMUyxHQUFFOzs7Ozs7Ozs7Ozs7WUFLUDVCLFFBQVFDLGlCQUFpQixXQUFXLENBQUNKLHlCQUNwQyw4REFBQ2dDO2dCQUFLOUIsV0FBVTswQkFBUUM7Ozs7OzswQkFHMUIsOERBQUM2QjswQkFBTXRDOzs7Ozs7WUFFTlMsUUFBUUMsaUJBQWlCLFVBQVUsQ0FBQ0oseUJBQ25DLDhEQUFDZ0M7Z0JBQUs5QixXQUFVOzBCQUFRQzs7Ozs7Ozs7Ozs7O0FBSWhDO0FBRUEsaUVBQWVWLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxjb21wb25lbnRzXFxCdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIEJ1dHRvblByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgb25DbGljaz86ICgpID0+IHZvaWQ7XG4gIHR5cGU/OiAnYnV0dG9uJyB8ICdzdWJtaXQnIHwgJ3Jlc2V0JztcbiAgdmFyaWFudD86ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ3N1Y2Nlc3MnIHwgJ2RhbmdlcicgfCAnd2FybmluZycgfCAnaW5mbyc7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZyc7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgbG9hZGluZz86IGJvb2xlYW47XG4gIGZ1bGxXaWR0aD86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgaWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgaWNvblBvc2l0aW9uPzogJ2xlZnQnIHwgJ3JpZ2h0JztcbiAgdGl0bGU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IEJ1dHRvbjogUmVhY3QuRkM8QnV0dG9uUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIG9uQ2xpY2ssXG4gIHR5cGUgPSAnYnV0dG9uJyxcbiAgdmFyaWFudCA9ICdwcmltYXJ5JyxcbiAgc2l6ZSA9ICdtZCcsXG4gIGRpc2FibGVkID0gZmFsc2UsXG4gIGxvYWRpbmcgPSBmYWxzZSxcbiAgZnVsbFdpZHRoID0gZmFsc2UsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBpY29uLFxuICBpY29uUG9zaXRpb24gPSAncmlnaHQnLFxuICB0aXRsZVxufSkgPT4ge1xuICBjb25zdCBiYXNlQ2xhc3NlcyA9IGBcbiAgICBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZm9udC1tZWRpdW0gcm91bmRlZC1sZ1xuICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTJcbiAgICBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFxuICAgICR7ZnVsbFdpZHRoID8gJ3ctZnVsbCcgOiAnJ31cbiAgYDtcblxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbTogJ3B4LTMgcHktMiB0ZXh0LXNtJyxcbiAgICBtZDogJ3B4LTQgcHktMi41IHRleHQtc20nLFxuICAgIGxnOiAncHgtNiBweS0zIHRleHQtYmFzZSdcbiAgfTtcblxuICBjb25zdCB2YXJpYW50Q2xhc3NlcyA9IHtcbiAgICBwcmltYXJ5OiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlXG4gICAgICBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLXB1cnBsZS03MDAgZm9jdXM6cmluZy1ibHVlLTUwMFxuICAgICAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2Zvcm0gaG92ZXI6LXRyYW5zbGF0ZS15LTAuNVxuICAgIGAsXG4gICAgc2Vjb25kYXJ5OiBgXG4gICAgICBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS0zMDBcbiAgICAgIGhvdmVyOmJnLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ncmF5LTQwMCBmb2N1czpyaW5nLWdyYXktNTAwXG4gICAgICBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnXG4gICAgYCxcbiAgICBzdWNjZXNzOiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAwIHRvLWVtZXJhbGQtNjAwIHRleHQtd2hpdGVcbiAgICAgIGhvdmVyOmZyb20tZ3JlZW4tNjAwIGhvdmVyOnRvLWVtZXJhbGQtNzAwIGZvY3VzOnJpbmctZ3JlZW4tNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYCxcbiAgICBkYW5nZXI6IGBcbiAgICAgIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNTAwIHRvLXBpbmstNjAwIHRleHQtd2hpdGVcbiAgICAgIGhvdmVyOmZyb20tcmVkLTYwMCBob3Zlcjp0by1waW5rLTcwMCBmb2N1czpyaW5nLXJlZC01MDBcbiAgICAgIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0wLjVcbiAgICBgLFxuICAgIHdhcm5pbmc6IGBcbiAgICAgIGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNTAwIHRvLW9yYW5nZS02MDAgdGV4dC13aGl0ZVxuICAgICAgaG92ZXI6ZnJvbS15ZWxsb3ctNjAwIGhvdmVyOnRvLW9yYW5nZS03MDAgZm9jdXM6cmluZy15ZWxsb3ctNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYCxcbiAgICBpbmZvOiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tY3lhbi01MDAgdG8tYmx1ZS02MDAgdGV4dC13aGl0ZVxuICAgICAgaG92ZXI6ZnJvbS1jeWFuLTYwMCBob3Zlcjp0by1ibHVlLTcwMCBmb2N1czpyaW5nLWN5YW4tNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYFxuICB9O1xuXG4gIGNvbnN0IGNvbWJpbmVkQ2xhc3NlcyA9IGBcbiAgICAke2Jhc2VDbGFzc2VzfVxuICAgICR7c2l6ZUNsYXNzZXNbc2l6ZV19XG4gICAgJHt2YXJpYW50Q2xhc3Nlc1t2YXJpYW50XX1cbiAgICAke2NsYXNzTmFtZX1cbiAgYDtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkIHx8IGxvYWRpbmd9XG4gICAgICBjbGFzc05hbWU9e2NvbWJpbmVkQ2xhc3Nlc31cbiAgICAgIHRpdGxlPXt0aXRsZX1cbiAgICA+XG4gICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgIDxzdmdcbiAgICAgICAgICBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gLW1sLTEgbXItMiBoLTQgdy00IHRleHQtY3VycmVudFwiXG4gICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICA+XG4gICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiXG4gICAgICAgICAgICBjeD1cIjEyXCJcbiAgICAgICAgICAgIGN5PVwiMTJcIlxuICAgICAgICAgICAgcj1cIjEwXCJcbiAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgIC8+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIlxuICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIHtpY29uICYmIGljb25Qb3NpdGlvbiA9PT0gJ3JpZ2h0JyAmJiAhbG9hZGluZyAmJiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTJcIj57aWNvbn08L3NwYW4+XG4gICAgICApfVxuICAgICAgXG4gICAgICA8c3Bhbj57Y2hpbGRyZW59PC9zcGFuPlxuICAgICAgXG4gICAgICB7aWNvbiAmJiBpY29uUG9zaXRpb24gPT09ICdsZWZ0JyAmJiAhbG9hZGluZyAmJiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj57aWNvbn08L3NwYW4+XG4gICAgICApfVxuICAgIDwvYnV0dG9uPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQnV0dG9uO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiY2hpbGRyZW4iLCJvbkNsaWNrIiwidHlwZSIsInZhcmlhbnQiLCJzaXplIiwiZGlzYWJsZWQiLCJsb2FkaW5nIiwiZnVsbFdpZHRoIiwiY2xhc3NOYW1lIiwiaWNvbiIsImljb25Qb3NpdGlvbiIsInRpdGxlIiwiYmFzZUNsYXNzZXMiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInZhcmlhbnRDbGFzc2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsInN1Y2Nlc3MiLCJkYW5nZXIiLCJ3YXJuaW5nIiwiaW5mbyIsImNvbWJpbmVkQ2xhc3NlcyIsImJ1dHRvbiIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwicGF0aCIsImQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Card.tsx":
/*!*********************************!*\
  !*** ./src/components/Card.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Card = ({ children, title, subtitle, className = '', onClick, hoverable = false, padding = 'md', shadow = 'md' })=>{\n    const paddingClasses = {\n        sm: 'p-4',\n        md: 'p-6',\n        lg: 'p-8'\n    };\n    const shadowClasses = {\n        sm: 'shadow-sm',\n        md: 'shadow-md',\n        lg: 'shadow-lg',\n        xl: 'shadow-xl'\n    };\n    const baseClasses = `\n    bg-white rounded-xl border border-gray-200 transition-all duration-300\n    ${paddingClasses[padding]}\n    ${shadowClasses[shadow]}\n    ${hoverable ? 'hover:shadow-xl hover:scale-105 cursor-pointer' : ''}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: baseClasses,\n        onClick: onClick,\n        children: [\n            (title || subtitle) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-1\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, undefined),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Header = ({ onToggleSidebar, currentUser, onLogout })=>{\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            // تحديث الوقت كل ثانية\n            const timer = setInterval({\n                \"Header.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Header.useEffect.timer\"], 1000);\n            return ({\n                \"Header.useEffect\": ()=>clearInterval(timer)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            second: '2-digit',\n            hour12: true\n        });\n    };\n    const formatDate = (date)=>{\n        return date.toLocaleDateString('ar-SA', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const formatWeekday = (date)=>{\n        return date.toLocaleDateString('ar-SA', {\n            weekday: 'long'\n        });\n    };\n    const formatGregorianDate = (date)=>{\n        const day = date.getDate().toString().padStart(2, '0');\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const year = date.getFullYear();\n        return `${day}/${month}/${year}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggleSidebar,\n                                    className: \"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"نظام إدارة المدرسة\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"اليوم\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700 font-medium\",\n                                            children: formatWeekday(currentTime)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-px h-10 bg-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"هجري\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700 font-medium\",\n                                            children: formatDate(currentTime)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-px h-10 bg-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"ميلادي\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700 font-medium\",\n                                            children: formatGregorianDate(currentTime)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-px h-10 bg-gray-300 ml-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center ml-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"الوقت\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-blue-600\",\n                                            children: formatTime(currentTime)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 space-x-reverse\",\n                            children: currentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUserMenu(!showUserMenu),\n                                        className: \"flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-sm\",\n                                                    children: currentUser.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden md:block text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: currentUser.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: [\n                                                            currentUser.role === 'developer' && 'المطور',\n                                                            currentUser.role === 'admin' && 'مدير النظام',\n                                                            currentUser.role === 'teacher' && 'معلم',\n                                                            currentUser.role === 'student' && 'طالب',\n                                                            currentUser.role === 'parent' && 'ولي أمر'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-gray-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 9l-7 7-7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>window.location.href = '/profile',\n                                                    className: \"w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                                    children: \"الملف الشخصي\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>window.location.href = '/settings',\n                                                    className: \"w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                                    children: \"الإعدادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {\n                                                            if (onLogout) {\n                                                                onLogout();\n                                                            } else {\n                                                                logout();\n                                                            }\n                                                        }\n                                                    },\n                                                    className: \"w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"تسجيل الخروج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30\",\n                onClick: ()=>{\n                    setShowUserMenu(false);\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFbUQ7QUFFVDtBQVExQyxNQUFNSSxTQUFnQyxDQUFDLEVBQUVDLGVBQWUsRUFBRUMsV0FBVyxFQUFFQyxRQUFRLEVBQUU7SUFDL0UsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdSLCtDQUFRQSxDQUFDLElBQUlTO0lBQ25ELE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdYLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sRUFBRVksTUFBTSxFQUFFLEdBQUdWLHVEQUFPQTtJQUUxQkQsZ0RBQVNBOzRCQUFDO1lBQ1IsdUJBQXVCO1lBQ3ZCLE1BQU1ZLFFBQVFDOzBDQUFZO29CQUN4Qk4sZUFBZSxJQUFJQztnQkFDckI7eUNBQUc7WUFFSDtvQ0FBTyxJQUFNTSxjQUFjRjs7UUFDN0I7MkJBQUcsRUFBRTtJQUVMLE1BQU1HLGFBQWEsQ0FBQ0M7UUFDbEIsT0FBT0EsS0FBS0Msa0JBQWtCLENBQUMsU0FBUztZQUN0Q0MsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNQyxhQUFhLENBQUNOO1FBQ2xCLE9BQU9BLEtBQUtPLGtCQUFrQixDQUFDLFNBQVM7WUFDdENDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxLQUFLO1FBQ1A7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDWDtRQUNyQixPQUFPQSxLQUFLTyxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3RDSyxTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU1DLHNCQUFzQixDQUFDYjtRQUMzQixNQUFNVSxNQUFNVixLQUFLYyxPQUFPLEdBQUdDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUc7UUFDbEQsTUFBTVAsUUFBUSxDQUFDVCxLQUFLaUIsUUFBUSxLQUFLLEdBQUdGLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUc7UUFDM0QsTUFBTVIsT0FBT1IsS0FBS2tCLFdBQVc7UUFDN0IsT0FBTyxHQUFHUixJQUFJLENBQUMsRUFBRUQsTUFBTSxDQUFDLEVBQUVELE1BQU07SUFDbEM7SUFJQSxxQkFDRSw4REFBQ1c7UUFBT0MsV0FBVTs7MEJBQ2hCLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNFO29DQUNDQyxTQUFTcEM7b0NBQ1RpQyxXQUFVOzhDQUVWLDRFQUFDSTt3Q0FBSUosV0FBVTt3Q0FBVUssTUFBSzt3Q0FBT0MsUUFBTzt3Q0FBZUMsU0FBUTtrREFDakUsNEVBQUNDOzRDQUFLQyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBYTs0Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJekUsOERBQUNYO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDYTt3Q0FBR2IsV0FBVTtrREFBa0M7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtwRCw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUViLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUF3Qjs7Ozs7O3NEQUN2Qyw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ1pULGNBQWNyQjs7Ozs7Ozs7Ozs7OzhDQUtuQiw4REFBQytCO29DQUFJRCxXQUFVOzs7Ozs7OENBR2YsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3ZDLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDWmQsV0FBV2hCOzs7Ozs7Ozs7Ozs7OENBS2hCLDhEQUFDK0I7b0NBQUlELFdBQVU7Ozs7Ozs4Q0FHZiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFBd0I7Ozs7OztzREFDdkMsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNaUCxvQkFBb0J2Qjs7Ozs7Ozs7Ozs7OzhDQUt6Qiw4REFBQytCO29DQUFJRCxXQUFVOzs7Ozs7OENBR2YsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3ZDLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDWnJCLFdBQVdUOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWxCLDhEQUFDK0I7NEJBQUlELFdBQVU7c0NBR1poQyw2QkFDQyw4REFBQ2lDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0U7d0NBQ0NDLFNBQVMsSUFBTTdCLGdCQUFnQixDQUFDRDt3Q0FDaEMyQixXQUFVOzswREFFViw4REFBQ0M7Z0RBQUlELFdBQVU7MERBQ2IsNEVBQUNjO29EQUFLZCxXQUFVOzhEQUNiaEMsWUFBWStDLElBQUksQ0FBQ0MsTUFBTSxDQUFDOzs7Ozs7Ozs7OzswREFHN0IsOERBQUNmO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVU7a0VBQ1poQyxZQUFZK0MsSUFBSTs7Ozs7O2tFQUVuQiw4REFBQ2Q7d0RBQUlELFdBQVU7OzREQUNaaEMsWUFBWWlELElBQUksS0FBSyxlQUFlOzREQUNwQ2pELFlBQVlpRCxJQUFJLEtBQUssV0FBVzs0REFDaENqRCxZQUFZaUQsSUFBSSxLQUFLLGFBQWE7NERBQ2xDakQsWUFBWWlELElBQUksS0FBSyxhQUFhOzREQUNsQ2pELFlBQVlpRCxJQUFJLEtBQUssWUFBWTs7Ozs7Ozs7Ozs7OzswREFHdEMsOERBQUNiO2dEQUFJSixXQUFVO2dEQUF3QkssTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDL0UsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBS3hFdkMsOEJBQ0MsOERBQUM0Qjt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDRTtvREFDQ0MsU0FBUyxJQUFNZSxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztvREFDdENwQixXQUFVOzhEQUNYOzs7Ozs7OERBR0QsOERBQUNFO29EQUNDQyxTQUFTLElBQU1lLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO29EQUN0Q3BCLFdBQVU7OERBQ1g7Ozs7Ozs4REFHRCw4REFBQ3FCO29EQUFHckIsV0FBVTs7Ozs7OzhEQUNkLDhEQUFDRTtvREFDQ0MsU0FBUzt3REFDUCxJQUFJbUIsUUFBUSxrQ0FBa0M7NERBQzVDLElBQUlyRCxVQUFVO2dFQUNaQTs0REFDRixPQUFPO2dFQUNMTTs0REFDRjt3REFDRjtvREFDRjtvREFDQXlCLFdBQVU7O3NFQUVWLDhEQUFDSTs0REFBSUosV0FBVTs0REFBVUssTUFBSzs0REFBT0MsUUFBTzs0REFBZUMsU0FBUTtzRUFDakUsNEVBQUNDO2dFQUFLQyxlQUFjO2dFQUFRQyxnQkFBZTtnRUFBUUMsYUFBYTtnRUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7c0VBRXZFLDhEQUFDRTtzRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBWXZCekMsOEJBQ0MsOERBQUM0QjtnQkFDQ0QsV0FBVTtnQkFDVkcsU0FBUztvQkFDUDdCLGdCQUFnQjtnQkFDbEI7Ozs7Ozs7Ozs7OztBQUtWO0FBRUEsaUVBQWVSLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxjb21wb25lbnRzXFxIZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBVc2VyIH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJztcblxuaW50ZXJmYWNlIEhlYWRlclByb3BzIHtcbiAgb25Ub2dnbGVTaWRlYmFyOiAoKSA9PiB2b2lkO1xuICBjdXJyZW50VXNlcjogVXNlciB8IG51bGw7XG4gIG9uTG9nb3V0PzogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgSGVhZGVyOiBSZWFjdC5GQzxIZWFkZXJQcm9wcz4gPSAoeyBvblRvZ2dsZVNpZGViYXIsIGN1cnJlbnRVc2VyLCBvbkxvZ291dCB9KSA9PiB7XG4gIGNvbnN0IFtjdXJyZW50VGltZSwgc2V0Q3VycmVudFRpbWVdID0gdXNlU3RhdGUobmV3IERhdGUoKSk7XG4gIGNvbnN0IFtzaG93VXNlck1lbnUsIHNldFNob3dVc2VyTWVudV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IHsgbG9nb3V0IH0gPSB1c2VBdXRoKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDYqtit2K/ZitirINin2YTZiNmC2Kog2YPZhCDYq9in2YbZitipXG4gICAgY29uc3QgdGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXRDdXJyZW50VGltZShuZXcgRGF0ZSgpKTtcbiAgICB9LCAxMDAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGZvcm1hdFRpbWUgPSAoZGF0ZTogRGF0ZSkgPT4ge1xuICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygnYXItU0EnLCB7XG4gICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgIHNlY29uZDogJzItZGlnaXQnLFxuICAgICAgaG91cjEyOiB0cnVlXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlOiBEYXRlKSA9PiB7XG4gICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1TQScsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICBkYXk6ICdudW1lcmljJ1xuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFdlZWtkYXkgPSAoZGF0ZTogRGF0ZSkgPT4ge1xuICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnYXItU0EnLCB7XG4gICAgICB3ZWVrZGF5OiAnbG9uZydcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRHcmVnb3JpYW5EYXRlID0gKGRhdGU6IERhdGUpID0+IHtcbiAgICBjb25zdCBkYXkgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7XG4gICAgY29uc3QgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgMSkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpO1xuICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7XG4gICAgcmV0dXJuIGAke2RheX0vJHttb250aH0vJHt5ZWFyfWA7XG4gIH07XG5cblxuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctbGcgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHN0aWNreSB0b3AtMCB6LTQwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGgtMTZcIj5cbiAgICAgICAgICB7Lyog2KfZhNis2KfZhtioINin2YTYo9mK2YXZhiAtINiy2LEg2KfZhNmC2KfYptmF2Kkg2YjYp9mE2LnZhtmI2KfZhiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17b25Ub2dnbGVTaWRlYmFyfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZyB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCA2aDE2TTQgMTJoMTZNNCAxOGgxNlwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrXCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+2YbYuNin2YUg2KXYr9in2LHYqSDYp9mE2YXYr9ix2LPYqTwvaDE+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDYp9mE2YjYs9i3IC0g2KfZhNiq2KfYsdmK2K4g2YjYp9mE2YjZgtiqICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtOCBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgIHsvKiDYp9mE2YrZiNmFICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPtin2YTZitmI2YU8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICB7Zm9ybWF0V2Vla2RheShjdXJyZW50VGltZSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDZgdin2LXZhCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1weCBoLTEwIGJnLWdyYXktMzAwXCI+PC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDYp9mE2KrYp9ix2YrYriDYp9mE2YfYrNix2YogKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+2YfYrNix2Yo8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShjdXJyZW50VGltZSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDZgdin2LXZhCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1weCBoLTEwIGJnLWdyYXktMzAwXCI+PC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDYp9mE2KrYp9ix2YrYriDYp9mE2YXZitmE2KfYr9mKICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPtmF2YrZhNin2K/ZijwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgIHtmb3JtYXRHcmVnb3JpYW5EYXRlKGN1cnJlbnRUaW1lKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINmB2KfYtdmEICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LXB4IGgtMTAgYmctZ3JheS0zMDAgbWwtNFwiPjwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog2KfZhNmI2YLYqiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWwtOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPtin2YTZiNmC2Ko8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgICAge2Zvcm1hdFRpbWUoY3VycmVudFRpbWUpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qINin2YTYrNin2YbYqCDYp9mE2KPZitiz2LEgLSDZgtin2KbZhdipINin2YTZhdiz2KrYrtiv2YUgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgc3BhY2UteC1yZXZlcnNlXCI+XG5cbiAgICAgICAgICAgIHsvKiDZgtin2KbZhdipINin2YTZhdiz2KrYrtiv2YUgKi99XG4gICAgICAgICAgICB7Y3VycmVudFVzZXIgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1VzZXJNZW51KCFzaG93VXNlck1lbnUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZSBwLTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50VXNlci5uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9jayB0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRVc2VyLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50VXNlci5yb2xlID09PSAnZGV2ZWxvcGVyJyAmJiAn2KfZhNmF2LfZiNixJ31cbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVudFVzZXIucm9sZSA9PT0gJ2FkbWluJyAmJiAn2YXYr9mK2LEg2KfZhNmG2LjYp9mFJ31cbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVudFVzZXIucm9sZSA9PT0gJ3RlYWNoZXInICYmICfZhdi52YTZhSd9XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRVc2VyLnJvbGUgPT09ICdzdHVkZW50JyAmJiAn2LfYp9mE2KgnfVxuICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50VXNlci5yb2xlID09PSAncGFyZW50JyAmJiAn2YjZhNmKINij2YXYsSd9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTkgOWwtNyA3LTctN1wiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgIHsvKiDZgtin2KbZhdipINin2YTZhdiz2KrYrtiv2YUg2KfZhNmF2YbYs9iv2YTYqSAqL31cbiAgICAgICAgICAgICAgICB7c2hvd1VzZXJNZW51ICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIG10LTIgdy00OCBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHotNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL3Byb2ZpbGUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtcmlnaHQgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAg2KfZhNmF2YTZgSDYp9mE2LTYrti12YpcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvc2V0dGluZ3MnfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtcmlnaHQgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAg2KfZhNil2LnYr9in2K/Yp9iqXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm15LTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNvbmZpcm0oJ9mH2YQg2KPZhtiqINmF2KrYo9mD2K8g2YXZhiDYqtiz2KzZitmEINin2YTYrtix2YjYrNifJykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAob25Mb2dvdXQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uTG9nb3V0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvZ291dCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXJpZ2h0IHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtcmVkLTYwMCBob3ZlcjpiZy1yZWQtNTAgdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNyAxNmw0LTRtMCAwbC00LTRtNCA0SDdtNiA0djFhMyAzIDAgMDEtMyAzSDZhMyAzIDAgMDEtMy0zVjdhMyAzIDAgMDEzLTNoNGEzIDMgMCAwMTMgM3YxXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+2KrYs9is2YrZhCDYp9mE2K7YsdmI2Kw8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog2KXYutmE2KfZgiDYp9mE2YLZiNin2KbZhSDYudmG2K8g2KfZhNmG2YLYsSDYrtin2LHYrNmH2KcgKi99XG4gICAgICB7c2hvd1VzZXJNZW51ICYmIChcbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei0zMFwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgc2V0U2hvd1VzZXJNZW51KGZhbHNlKTtcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgKX1cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEhlYWRlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsIkhlYWRlciIsIm9uVG9nZ2xlU2lkZWJhciIsImN1cnJlbnRVc2VyIiwib25Mb2dvdXQiLCJjdXJyZW50VGltZSIsInNldEN1cnJlbnRUaW1lIiwiRGF0ZSIsInNob3dVc2VyTWVudSIsInNldFNob3dVc2VyTWVudSIsImxvZ291dCIsInRpbWVyIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwiZm9ybWF0VGltZSIsImRhdGUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJob3VyIiwibWludXRlIiwic2Vjb25kIiwiaG91cjEyIiwiZm9ybWF0RGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInllYXIiLCJtb250aCIsImRheSIsImZvcm1hdFdlZWtkYXkiLCJ3ZWVrZGF5IiwiZm9ybWF0R3JlZ29yaWFuRGF0ZSIsImdldERhdGUiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwiZ2V0TW9udGgiLCJnZXRGdWxsWWVhciIsImhlYWRlciIsImNsYXNzTmFtZSIsImRpdiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJoMSIsInNwYW4iLCJuYW1lIiwiY2hhckF0Iiwicm9sZSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImhyIiwiY29uZmlybSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Layout = ({ children, allowedRoles, requireAuth = true })=>{\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Layout.useEffect\": ()=>{\n            // تحديد حجم الشاشة\n            const checkScreenSize = {\n                \"Layout.useEffect.checkScreenSize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    if (window.innerWidth < 768) {\n                        setSidebarOpen(false);\n                    }\n                }\n            }[\"Layout.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            window.addEventListener('resize', checkScreenSize);\n            // استرجاع المستخدم الحالي\n            const user = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getCurrentUser();\n            setCurrentUser(user);\n            return ({\n                \"Layout.useEffect\": ()=>{\n                    window.removeEventListener('resize', checkScreenSize);\n                }\n            })[\"Layout.useEffect\"];\n        }\n    }[\"Layout.useEffect\"], []);\n    const toggleSidebar = ()=>{\n        setSidebarOpen(!sidebarOpen);\n    };\n    const handleLogout = ()=>{\n        _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.logout();\n        setCurrentUser(null);\n        window.location.href = '/login';\n    };\n    // إذا كانت الحماية مطلوبة، استخدم ProtectedRoute\n    if (requireAuth) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            allowedRoles: allowedRoles,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContent, {\n                currentUser: currentUser,\n                sidebarOpen: sidebarOpen,\n                setSidebarOpen: setSidebarOpen,\n                isMobile: isMobile,\n                toggleSidebar: toggleSidebar,\n                handleLogout: handleLogout,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined);\n    }\n    // إذا لم تكن الحماية مطلوبة، اعرض المحتوى مباشرة\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContent, {\n        currentUser: currentUser,\n        sidebarOpen: sidebarOpen,\n        setSidebarOpen: setSidebarOpen,\n        isMobile: isMobile,\n        toggleSidebar: toggleSidebar,\n        handleLogout: handleLogout,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\nconst LayoutContent = ({ children, currentUser, sidebarOpen, setSidebarOpen, isMobile, toggleSidebar, handleLogout })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false),\n                isMobile: isMobile,\n                currentUser: currentUser\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `transition-all duration-300 ${sidebarOpen && !isMobile ? 'mr-64' : 'mr-0'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onToggleSidebar: toggleSidebar,\n                        currentUser: currentUser,\n                        onLogout: handleLogout\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-4 md:p-6 lg:p-8 pb-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"fixed bottom-0 right-0 bg-white/90 backdrop-blur-sm border-t border-l border-gray-200 rounded-tl-lg shadow-lg p-3 z-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-semibold text-blue-600 mb-1\",\n                                    children: \"نظام إدارة المدرسة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"تطوير: عبيدة العيثاوي\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"\\uD83D\\uDCDE 07813332882\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            isMobile && sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ProtectedRoute = ({ children, allowedRoles = [], redirectTo = '/login' })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [authorized, setAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"ProtectedRoute.useEffect\"], []);\n    const checkAuth = ()=>{\n        try {\n            const currentUser = _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageManager.getCurrentUser();\n            if (!currentUser) {\n                // المستخدم غير مسجل دخول\n                router.push(redirectTo);\n                return;\n            }\n            // التحقق من الصلاحيات إذا كانت محددة\n            if (allowedRoles.length > 0 && !allowedRoles.includes(currentUser.role)) {\n                // المستخدم لا يملك الصلاحية المطلوبة\n                router.push('/unauthorized');\n                return;\n            }\n            setUser(currentUser);\n            setAuthorized(true);\n        } catch (error) {\n            console.error('خطأ في التحقق من المصادقة:', error);\n            router.push(redirectTo);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // شاشة التحميل\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white text-2xl\",\n                            children: \"\\uD83C\\uDFEB\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري التحقق من المصادقة...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, undefined);\n    }\n    // إذا لم يكن مخولاً، لا نعرض شيئاً (سيتم إعادة التوجيه)\n    if (!authorized) {\n        return null;\n    }\n    // عرض المحتوى المحمي\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* harmony import */ var _utils_appInfo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/appInfo */ \"(ssr)/./src/utils/appInfo.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst menuItems = [\n    {\n        id: 'dashboard',\n        title: 'لوحة التحكم',\n        icon: '🏠',\n        href: '/',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'students',\n        title: 'إدارة الطلاب',\n        icon: '👨‍🎓',\n        href: '/students',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'teachers',\n        title: 'إدارة المعلمين',\n        icon: '👨‍🏫',\n        href: '/teachers',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'employees',\n        title: 'إدارة الموظفين',\n        icon: '👨‍💼',\n        href: '/employees',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'classes',\n        title: 'إدارة الصفوف',\n        icon: '🏫',\n        href: '/classes',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'subjects',\n        title: 'إدارة المواد',\n        icon: '📚',\n        href: '/subjects',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'schedule',\n        title: 'الجدول الدراسي',\n        icon: '📅',\n        href: '/schedule',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    },\n    {\n        id: 'settings',\n        title: 'الإعدادات',\n        icon: '⚙️',\n        href: '/settings',\n        roles: [\n            'developer',\n            'admin',\n            'teacher',\n            'student',\n            'parent'\n        ]\n    }\n];\nconst Sidebar = ({ isOpen, onClose, isMobile, currentUser })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [schoolName, setSchoolName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('نظام إدارة المدرسة');\n    // معلومات المطور\n    const developerInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_5__.getDeveloperInfo)();\n    // تحميل اسم المدرسة من الإعدادات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const settings = _utils_localStorage__WEBPACK_IMPORTED_MODULE_4__.localStorageManager.getSettings();\n            if (settings && settings.schoolName) {\n                setSchoolName(settings.schoolName);\n            }\n        }\n    }[\"Sidebar.useEffect\"], []);\n    // الاستماع لتحديثات الإعدادات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleSettingsUpdate = {\n                \"Sidebar.useEffect.handleSettingsUpdate\": (event)=>{\n                    const newSettings = event.detail;\n                    if (newSettings && newSettings.schoolName) {\n                        setSchoolName(newSettings.schoolName);\n                    }\n                }\n            }[\"Sidebar.useEffect.handleSettingsUpdate\"];\n            window.addEventListener('settingsUpdated', handleSettingsUpdate);\n            return ({\n                \"Sidebar.useEffect\": ()=>{\n                    window.removeEventListener('settingsUpdated', handleSettingsUpdate);\n                }\n            })[\"Sidebar.useEffect\"];\n        }\n    }[\"Sidebar.useEffect\"], []);\n    // تصفية العناصر حسب دور المستخدم\n    const filteredMenuItems = menuItems.filter((item)=>!item.roles || !currentUser || item.roles.includes(currentUser.role));\n    const isActive = (href)=>{\n        if (href === '/') {\n            return pathname === '/';\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n        fixed top-0 right-0 h-full w-64 bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : 'translate-x-full'}\n        ${isMobile ? 'w-64' : 'w-64'}\n      `,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"\\uD83C\\uDFEB\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-bold text-gray-800\",\n                                                \"data-sidebar-title\": true,\n                                                children: schoolName\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"نظام الإدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined),\n                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 overflow-y-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2\",\n                        children: filteredMenuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: isMobile ? onClose : undefined,\n                                    className: `\n                    flex items-center space-x-3 space-x-reverse p-3 rounded-xl transition-all duration-200\n                    ${isActive(item.href) ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}\n                  `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, item.id, false, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-1\",\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" \",\n                                    schoolName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: [\n                                    \"تطوير: \",\n                                    developerInfo.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUN0QjtBQUNpQjtBQUVhO0FBQ1I7QUFpQm5ELE1BQU1PLFlBQXdCO0lBQzVCO1FBQ0VDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztZQUFDO1lBQWE7WUFBUztZQUFXO1lBQVc7U0FBUztJQUMvRDtDQUNEO0FBRUQsTUFBTUMsVUFBa0MsQ0FBQyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUU7SUFDakYsTUFBTUMsV0FBV2QsNERBQVdBO0lBQzVCLE1BQU0sQ0FBQ2UsWUFBWUMsY0FBYyxHQUFHbkIsK0NBQVFBLENBQUM7SUFFN0MsaUJBQWlCO0lBQ2pCLE1BQU1vQixnQkFBZ0JmLGdFQUFnQkE7SUFFdEMsaUNBQWlDO0lBQ2pDSixnREFBU0E7NkJBQUM7WUFDUixNQUFNb0IsV0FBV2pCLG9FQUFtQkEsQ0FBQ2tCLFdBQVc7WUFDaEQsSUFBSUQsWUFBWUEsU0FBU0gsVUFBVSxFQUFFO2dCQUNuQ0MsY0FBY0UsU0FBU0gsVUFBVTtZQUNuQztRQUNGOzRCQUFHLEVBQUU7SUFFTCw4QkFBOEI7SUFDOUJqQixnREFBU0E7NkJBQUM7WUFDUixNQUFNc0I7MERBQXVCLENBQUNDO29CQUM1QixNQUFNQyxjQUFjRCxNQUFNRSxNQUFNO29CQUNoQyxJQUFJRCxlQUFlQSxZQUFZUCxVQUFVLEVBQUU7d0JBQ3pDQyxjQUFjTSxZQUFZUCxVQUFVO29CQUN0QztnQkFDRjs7WUFFQVMsT0FBT0MsZ0JBQWdCLENBQUMsbUJBQW1CTDtZQUUzQztxQ0FBTztvQkFDTEksT0FBT0UsbUJBQW1CLENBQUMsbUJBQW1CTjtnQkFDaEQ7O1FBQ0Y7NEJBQUcsRUFBRTtJQUVMLGlDQUFpQztJQUNqQyxNQUFNTyxvQkFBb0J4QixVQUFVeUIsTUFBTSxDQUFDQyxDQUFBQSxPQUN6QyxDQUFDQSxLQUFLckIsS0FBSyxJQUFJLENBQUNLLGVBQWVnQixLQUFLckIsS0FBSyxDQUFDc0IsUUFBUSxDQUFDakIsWUFBWWtCLElBQUk7SUFHckUsTUFBTUMsV0FBVyxDQUFDekI7UUFDaEIsSUFBSUEsU0FBUyxLQUFLO1lBQ2hCLE9BQU9PLGFBQWE7UUFDdEI7UUFDQSxPQUFPQSxTQUFTbUIsVUFBVSxDQUFDMUI7SUFDN0I7SUFFQSxxQkFDRTtrQkFFRSw0RUFBQzJCO1lBQUlDLFdBQVcsQ0FBQzs7UUFFZixFQUFFekIsU0FBUyxrQkFBa0IsbUJBQW1CO1FBQ2hELEVBQUVFLFdBQVcsU0FBUyxPQUFPO01BQy9CLENBQUM7OzhCQUVDLDhEQUFDc0I7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUtELFdBQVU7c0RBQStCOzs7Ozs7Ozs7OztrREFFakQsOERBQUNEOzswREFDQyw4REFBQ0c7Z0RBQUdGLFdBQVU7Z0RBQWtDRyxvQkFBa0I7MERBQUV2Qjs7Ozs7OzBEQUNwRSw4REFBQ3dCO2dEQUFFSixXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQUd4Q3ZCLDBCQUNDLDhEQUFDNEI7Z0NBQ0NDLFNBQVM5QjtnQ0FDVHdCLFdBQVU7MENBRVYsNEVBQUNDO29DQUFLRCxXQUFVOzhDQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVNsQyw4REFBQ087b0JBQUlQLFdBQVU7OEJBQ2IsNEVBQUNRO3dCQUFHUixXQUFVO2tDQUNYUixrQkFBa0JpQixHQUFHLENBQUMsQ0FBQ2YscUJBQ3RCLDhEQUFDZ0I7MENBQ0MsNEVBQUM5QyxrREFBSUE7b0NBQ0hRLE1BQU1zQixLQUFLdEIsSUFBSTtvQ0FDZmtDLFNBQVM3QixXQUFXRCxVQUFVbUM7b0NBQzlCWCxXQUFXLENBQUM7O29CQUVWLEVBQUVILFNBQVNILEtBQUt0QixJQUFJLElBQ2hCLDBGQUNBLHNEQUNIO2tCQUNILENBQUM7O3NEQUVELDhEQUFDNkI7NENBQUtELFdBQVU7c0RBQVdOLEtBQUt2QixJQUFJOzs7Ozs7c0RBQ3BDLDhEQUFDOEI7NENBQUtELFdBQVU7c0RBQWVOLEtBQUt4QixLQUFLOzs7Ozs7Ozs7Ozs7K0JBYnBDd0IsS0FBS3pCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs4QkFxQnRCLDhEQUFDOEI7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0k7Z0NBQUVKLFdBQVU7O29DQUFPO29DQUFHLElBQUlZLE9BQU9DLFdBQVc7b0NBQUc7b0NBQUVqQzs7Ozs7OzswQ0FDbEQsOERBQUN3QjtnQ0FBRUosV0FBVTs7b0NBQVU7b0NBQVFsQixjQUFjZ0MsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU03RDtBQUVBLGlFQUFleEMsT0FBT0EsRUFBQyIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxzcmNcXGNvbXBvbmVudHNcXFNpZGViYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgVXNlciB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgbG9jYWxTdG9yYWdlTWFuYWdlciB9IGZyb20gJ0AvdXRpbHMvbG9jYWxTdG9yYWdlJztcbmltcG9ydCB7IGdldERldmVsb3BlckluZm8gfSBmcm9tICdAL3V0aWxzL2FwcEluZm8nO1xuXG5pbnRlcmZhY2UgU2lkZWJhclByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBpc01vYmlsZTogYm9vbGVhbjtcbiAgY3VycmVudFVzZXI6IFVzZXIgfCBudWxsO1xufVxuXG5pbnRlcmZhY2UgTWVudUl0ZW0ge1xuICBpZDogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBpY29uOiBzdHJpbmc7XG4gIGhyZWY6IHN0cmluZztcbiAgcm9sZXM/OiBzdHJpbmdbXTtcbn1cblxuY29uc3QgbWVudUl0ZW1zOiBNZW51SXRlbVtdID0gW1xuICB7XG4gICAgaWQ6ICdkYXNoYm9hcmQnLFxuICAgIHRpdGxlOiAn2YTZiNit2Kkg2KfZhNiq2K3Zg9mFJyxcbiAgICBpY29uOiAn8J+PoCcsXG4gICAgaHJlZjogJy8nLFxuICAgIHJvbGVzOiBbJ2RldmVsb3BlcicsICdhZG1pbicsICd0ZWFjaGVyJywgJ3N0dWRlbnQnLCAncGFyZW50J11cbiAgfSxcbiAge1xuICAgIGlkOiAnc3R1ZGVudHMnLFxuICAgIHRpdGxlOiAn2KXYr9in2LHYqSDYp9mE2LfZhNin2KgnLFxuICAgIGljb246ICfwn5Go4oCN8J+OkycsXG4gICAgaHJlZjogJy9zdHVkZW50cycsXG4gICAgcm9sZXM6IFsnZGV2ZWxvcGVyJywgJ2FkbWluJywgJ3RlYWNoZXInLCAnc3R1ZGVudCcsICdwYXJlbnQnXVxuICB9LFxuICB7XG4gICAgaWQ6ICd0ZWFjaGVycycsXG4gICAgdGl0bGU6ICfYpdiv2KfYsdipINin2YTZhdi52YTZhdmK2YYnLFxuICAgIGljb246ICfwn5Go4oCN8J+PqycsXG4gICAgaHJlZjogJy90ZWFjaGVycycsXG4gICAgcm9sZXM6IFsnZGV2ZWxvcGVyJywgJ2FkbWluJywgJ3RlYWNoZXInLCAnc3R1ZGVudCcsICdwYXJlbnQnXVxuICB9LFxuICB7XG4gICAgaWQ6ICdlbXBsb3llZXMnLFxuICAgIHRpdGxlOiAn2KXYr9in2LHYqSDYp9mE2YXZiNi42YHZitmGJyxcbiAgICBpY29uOiAn8J+RqOKAjfCfkrwnLFxuICAgIGhyZWY6ICcvZW1wbG95ZWVzJyxcbiAgICByb2xlczogWydkZXZlbG9wZXInLCAnYWRtaW4nLCAndGVhY2hlcicsICdzdHVkZW50JywgJ3BhcmVudCddXG4gIH0sXG4gIHtcbiAgICBpZDogJ2NsYXNzZXMnLFxuICAgIHRpdGxlOiAn2KXYr9in2LHYqSDYp9mE2LXZgdmI2YEnLFxuICAgIGljb246ICfwn4+rJyxcbiAgICBocmVmOiAnL2NsYXNzZXMnLFxuICAgIHJvbGVzOiBbJ2RldmVsb3BlcicsICdhZG1pbicsICd0ZWFjaGVyJywgJ3N0dWRlbnQnLCAncGFyZW50J11cbiAgfSxcbiAge1xuICAgIGlkOiAnc3ViamVjdHMnLFxuICAgIHRpdGxlOiAn2KXYr9in2LHYqSDYp9mE2YXZiNin2K8nLFxuICAgIGljb246ICfwn5OaJyxcbiAgICBocmVmOiAnL3N1YmplY3RzJyxcbiAgICByb2xlczogWydkZXZlbG9wZXInLCAnYWRtaW4nLCAndGVhY2hlcicsICdzdHVkZW50JywgJ3BhcmVudCddXG4gIH0sXG4gIHtcbiAgICBpZDogJ3NjaGVkdWxlJyxcbiAgICB0aXRsZTogJ9in2YTYrNiv2YjZhCDYp9mE2K/Ysdin2LPZiicsXG4gICAgaWNvbjogJ/Cfk4UnLFxuICAgIGhyZWY6ICcvc2NoZWR1bGUnLFxuICAgIHJvbGVzOiBbJ2RldmVsb3BlcicsICdhZG1pbicsICd0ZWFjaGVyJywgJ3N0dWRlbnQnLCAncGFyZW50J11cbiAgfSxcbiAge1xuICAgIGlkOiAnc2V0dGluZ3MnLFxuICAgIHRpdGxlOiAn2KfZhNil2LnYr9in2K/Yp9iqJyxcbiAgICBpY29uOiAn4pqZ77iPJyxcbiAgICBocmVmOiAnL3NldHRpbmdzJyxcbiAgICByb2xlczogWydkZXZlbG9wZXInLCAnYWRtaW4nLCAndGVhY2hlcicsICdzdHVkZW50JywgJ3BhcmVudCddXG4gIH1cbl07XG5cbmNvbnN0IFNpZGViYXI6IFJlYWN0LkZDPFNpZGViYXJQcm9wcz4gPSAoeyBpc09wZW4sIG9uQ2xvc2UsIGlzTW9iaWxlLCBjdXJyZW50VXNlciB9KSA9PiB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcbiAgY29uc3QgW3NjaG9vbE5hbWUsIHNldFNjaG9vbE5hbWVdID0gdXNlU3RhdGUoJ9mG2LjYp9mFINil2K/Yp9ix2Kkg2KfZhNmF2K/Ysdiz2KknKTtcblxuICAvLyDZhdi52YTZiNmF2KfYqiDYp9mE2YXYt9mI2LFcbiAgY29uc3QgZGV2ZWxvcGVySW5mbyA9IGdldERldmVsb3BlckluZm8oKTtcblxuICAvLyDYqtit2YXZitmEINin2LPZhSDYp9mE2YXYr9ix2LPYqSDZhdmGINin2YTYpdi52K/Yp9iv2KfYqlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNldHRpbmdzID0gbG9jYWxTdG9yYWdlTWFuYWdlci5nZXRTZXR0aW5ncygpO1xuICAgIGlmIChzZXR0aW5ncyAmJiBzZXR0aW5ncy5zY2hvb2xOYW1lKSB7XG4gICAgICBzZXRTY2hvb2xOYW1lKHNldHRpbmdzLnNjaG9vbE5hbWUpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vINin2YTYp9iz2KrZhdin2Lkg2YTYqtit2K/Zitir2KfYqiDYp9mE2KXYudiv2KfYr9in2KpcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVTZXR0aW5nc1VwZGF0ZSA9IChldmVudDogQ3VzdG9tRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IG5ld1NldHRpbmdzID0gZXZlbnQuZGV0YWlsO1xuICAgICAgaWYgKG5ld1NldHRpbmdzICYmIG5ld1NldHRpbmdzLnNjaG9vbE5hbWUpIHtcbiAgICAgICAgc2V0U2Nob29sTmFtZShuZXdTZXR0aW5ncy5zY2hvb2xOYW1lKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3NldHRpbmdzVXBkYXRlZCcsIGhhbmRsZVNldHRpbmdzVXBkYXRlIGFzIEV2ZW50TGlzdGVuZXIpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzZXR0aW5nc1VwZGF0ZWQnLCBoYW5kbGVTZXR0aW5nc1VwZGF0ZSBhcyBFdmVudExpc3RlbmVyKTtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8g2KrYtdmB2YrYqSDYp9mE2LnZhtin2LXYsSDYrdiz2Kgg2K/ZiNixINin2YTZhdiz2KrYrtiv2YVcbiAgY29uc3QgZmlsdGVyZWRNZW51SXRlbXMgPSBtZW51SXRlbXMuZmlsdGVyKGl0ZW0gPT5cbiAgICAhaXRlbS5yb2xlcyB8fCAhY3VycmVudFVzZXIgfHwgaXRlbS5yb2xlcy5pbmNsdWRlcyhjdXJyZW50VXNlci5yb2xlKVxuICApO1xuXG4gIGNvbnN0IGlzQWN0aXZlID0gKGhyZWY6IHN0cmluZykgPT4ge1xuICAgIGlmIChocmVmID09PSAnLycpIHtcbiAgICAgIHJldHVybiBwYXRobmFtZSA9PT0gJy8nO1xuICAgIH1cbiAgICByZXR1cm4gcGF0aG5hbWUuc3RhcnRzV2l0aChocmVmKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7Lyog2KfZhNi02LHZiti3INin2YTYrNin2YbYqNmKICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BcbiAgICAgICAgZml4ZWQgdG9wLTAgcmlnaHQtMCBoLWZ1bGwgdy02NCBiZy13aGl0ZSBzaGFkb3ctMnhsIHotNTAgdHJhbnNmb3JtIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dFxuICAgICAgICAke2lzT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICd0cmFuc2xhdGUteC1mdWxsJ31cbiAgICAgICAgJHtpc01vYmlsZSA/ICd3LTY0JyA6ICd3LTY0J31cbiAgICAgIGB9PlxuICAgICAgICB7Lyog2LHYo9izINin2YTYtNix2YrYtyDYp9mE2KzYp9mG2KjZiiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC1sZ1wiPvCfj6s8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCIgZGF0YS1zaWRlYmFyLXRpdGxlPntzY2hvb2xOYW1lfTwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+2YbYuNin2YUg2KfZhNil2K/Yp9ix2Kk8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7aXNNb2JpbGUgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsXCI+4pyVPC9zcGFuPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG5cblxuICAgICAgICB7Lyog2YLYp9im2YXYqSDYp9mE2KrZhtmC2YQgKi99XG4gICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTRcIj5cbiAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICB7ZmlsdGVyZWRNZW51SXRlbXMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgIDxsaSBrZXk9e2l0ZW0uaWR9PlxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtpc01vYmlsZSA/IG9uQ2xvc2UgOiB1bmRlZmluZWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHNwYWNlLXgtcmV2ZXJzZSBwLTMgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcbiAgICAgICAgICAgICAgICAgICAgJHtpc0FjdGl2ZShpdGVtLmhyZWYpXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbGcgdHJhbnNmb3JtIHNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwIGhvdmVyOnRleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bFwiPntpdGVtLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57aXRlbS50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC91bD5cbiAgICAgICAgPC9uYXY+XG5cbiAgICAgICAgey8qINiq2LDZitmK2YQg2KfZhNi02LHZiti3INin2YTYrNin2YbYqNmKICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItMVwiPsKpIHtuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCl9IHtzY2hvb2xOYW1lfTwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHNcIj7Yqti32YjZitixOiB7ZGV2ZWxvcGVySW5mby5uYW1lfTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8Lz5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNpZGViYXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsImxvY2FsU3RvcmFnZU1hbmFnZXIiLCJnZXREZXZlbG9wZXJJbmZvIiwibWVudUl0ZW1zIiwiaWQiLCJ0aXRsZSIsImljb24iLCJocmVmIiwicm9sZXMiLCJTaWRlYmFyIiwiaXNPcGVuIiwib25DbG9zZSIsImlzTW9iaWxlIiwiY3VycmVudFVzZXIiLCJwYXRobmFtZSIsInNjaG9vbE5hbWUiLCJzZXRTY2hvb2xOYW1lIiwiZGV2ZWxvcGVySW5mbyIsInNldHRpbmdzIiwiZ2V0U2V0dGluZ3MiLCJoYW5kbGVTZXR0aW5nc1VwZGF0ZSIsImV2ZW50IiwibmV3U2V0dGluZ3MiLCJkZXRhaWwiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImZpbHRlcmVkTWVudUl0ZW1zIiwiZmlsdGVyIiwiaXRlbSIsImluY2x1ZGVzIiwicm9sZSIsImlzQWN0aXZlIiwic3RhcnRzV2l0aCIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJoMiIsImRhdGEtc2lkZWJhci10aXRsZSIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwibmF2IiwidWwiLCJtYXAiLCJsaSIsInVuZGVmaW5lZCIsIkRhdGUiLCJnZXRGdWxsWWVhciIsIm5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"useAuth.useEffect\"], []);\n    const checkAuth = ()=>{\n        try {\n            const currentUser = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getCurrentUser();\n            setUser(currentUser);\n        } catch (error) {\n            console.error('خطأ في التحقق من المصادقة:', error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (emailOrUsername, password)=>{\n        try {\n            setLoading(true);\n            // محاكاة تأخير الشبكة\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const foundUser = users.find((u)=>(u.email === emailOrUsername || u.username === emailOrUsername) && u.password === password);\n            if (!foundUser) {\n                return {\n                    success: false,\n                    error: 'البريد الإلكتروني/اسم المستخدم أو كلمة المرور غير صحيحة'\n                };\n            }\n            // تسجيل الدخول بنجاح\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(foundUser);\n            setUser(foundUser);\n            // إضافة إشعار نجاح تسجيل الدخول\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'تم تسجيل الدخول بنجاح',\n                message: `مرحباً ${foundUser.name}، تم تسجيل دخولك بنجاح`,\n                type: 'success',\n                recipientId: foundUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: 'حدث خطأ أثناء تسجيل الدخول'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        try {\n            // إضافة إشعار تسجيل الخروج\n            if (user) {\n                const notification = {\n                    id: `notification-${Date.now()}`,\n                    title: 'تم تسجيل الخروج',\n                    message: `تم تسجيل خروجك من النظام بنجاح`,\n                    type: 'info',\n                    recipientId: user.id,\n                    recipientType: 'user',\n                    isRead: false,\n                    createdAt: new Date()\n                };\n                _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            }\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.logout();\n            setUser(null);\n            router.push('/login');\n        } catch (error) {\n            console.error('خطأ في تسجيل الخروج:', error);\n        }\n    };\n    const hasRole = (roles)=>{\n        if (!user) return false;\n        return roles.includes(user.role);\n    };\n    const updatePassword = async (currentPassword, newPassword)=>{\n        try {\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'المستخدم غير مسجل الدخول'\n                };\n            }\n            // التحقق من كلمة المرور الحالية\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const currentUser = users.find((u)=>u.id === user.id);\n            if (!currentUser || currentUser.password !== currentPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الحالية غير صحيحة'\n                };\n            }\n            // التحقق من قوة كلمة المرور الجديدة\n            if (newPassword.length < 3) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة قصيرة جداً (الحد الأدنى 3 أحرف)'\n                };\n            }\n            if (currentPassword === newPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية'\n                };\n            }\n            // تحديث كلمة المرور\n            const updatedUsers = users.map((u)=>u.id === user.id ? {\n                    ...u,\n                    password: newPassword,\n                    updatedAt: new Date()\n                } : u);\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.saveUsers(updatedUsers);\n            // تحديث المستخدم الحالي\n            const updatedUser = {\n                ...user,\n                password: newPassword,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(updatedUser);\n            setUser(updatedUser);\n            // إضافة إشعار نجاح تغيير كلمة المرور\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'تم تغيير كلمة المرور',\n                message: 'تم تغيير كلمة المرور بنجاح',\n                type: 'success',\n                recipientId: user.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تحديث كلمة المرور:', error);\n            return {\n                success: false,\n                error: 'فشل في تحديث كلمة المرور'\n            };\n        }\n    };\n    return {\n        user,\n        loading,\n        isAuthenticated: !!user,\n        login,\n        logout,\n        checkAuth,\n        hasRole,\n        updatePassword\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/appInfo.ts":
/*!******************************!*\
  !*** ./src/utils/appInfo.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_INFO: () => (/* binding */ APP_INFO),\n/* harmony export */   getAppInfo: () => (/* binding */ getAppInfo),\n/* harmony export */   getDeveloperInfo: () => (/* binding */ getDeveloperInfo),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo)\n/* harmony export */ });\n// معلومات التطبيق والمطور\nconst APP_INFO = {\n    name: 'نظام إدارة المدرسة',\n    version: '1.0.0',\n    description: 'نظام شامل لإدارة المدارس باللغة العربية',\n    developer: 'عبيدة العيثاوي',\n    developedBy: 'تم التطوير بواسطة عبيدة العيثاوي',\n    copyright: `© ${new Date().getFullYear()} عبيدة العيثاوي - جميع الحقوق محفوظة`,\n    features: [\n        'إدارة الطلاب والمعلمين',\n        'إدارة الصفوف والمواد الدراسية',\n        'نظام التقييم والدرجات',\n        'تقارير شاملة ومفصلة',\n        'واجهة عربية متجاوبة',\n        'تصميم نيومورفيك عصري'\n    ],\n    technologies: [\n        'Next.js 15',\n        'React 19',\n        'TypeScript',\n        'Tailwind CSS',\n        'localStorage'\n    ],\n    contact: {\n        developer: 'عبيدة العيثاوي',\n        email: '<EMAIL>',\n        phone: '07813332882'\n    }\n};\n// دالة للحصول على معلومات التطبيق\nconst getAppInfo = ()=>APP_INFO;\n// دالة للحصول على معلومات المطور\nconst getDeveloperInfo = ()=>({\n        name: APP_INFO.developer,\n        developedBy: APP_INFO.developedBy,\n        copyright: APP_INFO.copyright,\n        contact: APP_INFO.contact\n    });\n// دالة للحصول على معلومات الإصدار\nconst getVersionInfo = ()=>({\n        version: APP_INFO.version,\n        name: APP_INFO.name,\n        description: APP_INFO.description\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvYXBwSW5mby50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsMEJBQTBCO0FBQ25CLE1BQU1BLFdBQVc7SUFDdEJDLE1BQU07SUFDTkMsU0FBUztJQUNUQyxhQUFhO0lBQ2JDLFdBQVc7SUFDWEMsYUFBYTtJQUNiQyxXQUFXLENBQUMsRUFBRSxFQUFFLElBQUlDLE9BQU9DLFdBQVcsR0FBRyxvQ0FBb0MsQ0FBQztJQUM5RUMsVUFBVTtRQUNSO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBQ0RDLGNBQWM7UUFDWjtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDREMsU0FBUztRQUNQUCxXQUFXO1FBQ1hRLE9BQU87UUFDUEMsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGLGtDQUFrQztBQUMzQixNQUFNQyxhQUFhLElBQU1kLFNBQVM7QUFFekMsaUNBQWlDO0FBQzFCLE1BQU1lLG1CQUFtQixJQUFPO1FBQ3JDZCxNQUFNRCxTQUFTSSxTQUFTO1FBQ3hCQyxhQUFhTCxTQUFTSyxXQUFXO1FBQ2pDQyxXQUFXTixTQUFTTSxTQUFTO1FBQzdCSyxTQUFTWCxTQUFTVyxPQUFPO0lBQzNCLEdBQUc7QUFFSCxrQ0FBa0M7QUFDM0IsTUFBTUssaUJBQWlCLElBQU87UUFDbkNkLFNBQVNGLFNBQVNFLE9BQU87UUFDekJELE1BQU1ELFNBQVNDLElBQUk7UUFDbkJFLGFBQWFILFNBQVNHLFdBQVc7SUFDbkMsR0FBRyIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxzcmNcXHV0aWxzXFxhcHBJbmZvLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vINmF2LnZhNmI2YXYp9iqINin2YTYqti32KjZitmCINmI2KfZhNmF2LfZiNixXG5leHBvcnQgY29uc3QgQVBQX0lORk8gPSB7XG4gIG5hbWU6ICfZhti42KfZhSDYpdiv2KfYsdipINin2YTZhdiv2LHYs9ipJyxcbiAgdmVyc2lvbjogJzEuMC4wJyxcbiAgZGVzY3JpcHRpb246ICfZhti42KfZhSDYtNin2YXZhCDZhNil2K/Yp9ix2Kkg2KfZhNmF2K/Yp9ix2LMg2KjYp9mE2YTYutipINin2YTYudix2KjZitipJyxcbiAgZGV2ZWxvcGVyOiAn2LnYqNmK2K/YqSDYp9mE2LnZitir2KfZiNmKJyxcbiAgZGV2ZWxvcGVkQnk6ICfYqtmFINin2YTYqti32YjZitixINio2YjYp9iz2LfYqSDYudio2YrYr9ipINin2YTYudmK2KvYp9mI2YonLFxuICBjb3B5cmlnaHQ6IGDCqSAke25ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKX0g2LnYqNmK2K/YqSDYp9mE2LnZitir2KfZiNmKIC0g2KzZhdmK2Lkg2KfZhNit2YLZiNmCINmF2K3ZgdmI2LjYqWAsXG4gIGZlYXR1cmVzOiBbXG4gICAgJ9il2K/Yp9ix2Kkg2KfZhNi32YTYp9ioINmI2KfZhNmF2LnZhNmF2YrZhicsXG4gICAgJ9il2K/Yp9ix2Kkg2KfZhNi12YHZiNmBINmI2KfZhNmF2YjYp9ivINin2YTYr9ix2KfYs9mK2KknLFxuICAgICfZhti42KfZhSDYp9mE2KrZgtmK2YrZhSDZiNin2YTYr9ix2KzYp9iqJyxcbiAgICAn2KrZgtin2LHZitixINi02KfZhdmE2Kkg2YjZhdmB2LXZhNipJyxcbiAgICAn2YjYp9is2YfYqSDYudix2KjZitipINmF2KrYrNin2YjYqNipJyxcbiAgICAn2KrYtdmF2YrZhSDZhtmK2YjZhdmI2LHZgdmK2YMg2LnYtdix2YonXG4gIF0sXG4gIHRlY2hub2xvZ2llczogW1xuICAgICdOZXh0LmpzIDE1JyxcbiAgICAnUmVhY3QgMTknLFxuICAgICdUeXBlU2NyaXB0JyxcbiAgICAnVGFpbHdpbmQgQ1NTJyxcbiAgICAnbG9jYWxTdG9yYWdlJ1xuICBdLFxuICBjb250YWN0OiB7XG4gICAgZGV2ZWxvcGVyOiAn2LnYqNmK2K/YqSDYp9mE2LnZitir2KfZiNmKJyxcbiAgICBlbWFpbDogJ29iZWlkYUBzY2hvb2wuY29tJyxcbiAgICBwaG9uZTogJzA3ODEzMzMyODgyJ1xuICB9XG59O1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINmF2LnZhNmI2YXYp9iqINin2YTYqti32KjZitmCXG5leHBvcnQgY29uc3QgZ2V0QXBwSW5mbyA9ICgpID0+IEFQUF9JTkZPO1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINmF2LnZhNmI2YXYp9iqINin2YTZhdi32YjYsVxuZXhwb3J0IGNvbnN0IGdldERldmVsb3BlckluZm8gPSAoKSA9PiAoe1xuICBuYW1lOiBBUFBfSU5GTy5kZXZlbG9wZXIsXG4gIGRldmVsb3BlZEJ5OiBBUFBfSU5GTy5kZXZlbG9wZWRCeSxcbiAgY29weXJpZ2h0OiBBUFBfSU5GTy5jb3B5cmlnaHQsXG4gIGNvbnRhY3Q6IEFQUF9JTkZPLmNvbnRhY3Rcbn0pO1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINmF2LnZhNmI2YXYp9iqINin2YTYpdi12K/Yp9ixXG5leHBvcnQgY29uc3QgZ2V0VmVyc2lvbkluZm8gPSAoKSA9PiAoe1xuICB2ZXJzaW9uOiBBUFBfSU5GTy52ZXJzaW9uLFxuICBuYW1lOiBBUFBfSU5GTy5uYW1lLFxuICBkZXNjcmlwdGlvbjogQVBQX0lORk8uZGVzY3JpcHRpb25cbn0pO1xuIl0sIm5hbWVzIjpbIkFQUF9JTkZPIiwibmFtZSIsInZlcnNpb24iLCJkZXNjcmlwdGlvbiIsImRldmVsb3BlciIsImRldmVsb3BlZEJ5IiwiY29weXJpZ2h0IiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwiZmVhdHVyZXMiLCJ0ZWNobm9sb2dpZXMiLCJjb250YWN0IiwiZW1haWwiLCJwaG9uZSIsImdldEFwcEluZm8iLCJnZXREZXZlbG9wZXJJbmZvIiwiZ2V0VmVyc2lvbkluZm8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/appInfo.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/localStorage.ts":
/*!***********************************!*\
  !*** ./src/utils/localStorage.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageManager: () => (/* binding */ localStorageManager),\n/* harmony export */   searchUtils: () => (/* binding */ searchUtils)\n/* harmony export */ });\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    STUDENTS: 'school_students',\n    TEACHERS: 'school_teachers',\n    EMPLOYEES: 'school_employees',\n    CLASSES: 'school_classes',\n    SUBJECTS: 'school_subjects',\n    GRADES: 'school_grades',\n    ATTENDANCE: 'school_attendance',\n    USERS: 'school_users',\n    SETTINGS: 'school_settings',\n    EVENTS: 'school_events',\n    NOTIFICATIONS: 'school_notifications',\n    CURRENT_USER: 'school_current_user',\n    ACADEMIC_YEAR: 'school_academic_year',\n    CURRENT_SEMESTER: 'school_current_semester'\n};\n// فئة إدارة التخزين المحلي\nclass LocalStorageManager {\n    // حفظ البيانات\n    setItem(key, data) {\n        try {\n            const serializedData = JSON.stringify(data);\n            localStorage.setItem(key, serializedData);\n        } catch (error) {\n            console.error(`خطأ في حفظ البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // استرجاع البيانات\n    getItem(key) {\n        try {\n            const serializedData = localStorage.getItem(key);\n            if (serializedData === null) {\n                return null;\n            }\n            return JSON.parse(serializedData);\n        } catch (error) {\n            console.error(`خطأ في استرجاع البيانات للمفتاح ${key}:`, error);\n            return null;\n        }\n    }\n    // حذف البيانات\n    removeItem(key) {\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(`خطأ في حذف البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // مسح جميع البيانات\n    clearAll() {\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            this.removeItem(key);\n        });\n    }\n    // === إدارة الطلاب ===\n    getStudents() {\n        return this.getItem(STORAGE_KEYS.STUDENTS) || [];\n    }\n    saveStudents(students) {\n        this.setItem(STORAGE_KEYS.STUDENTS, students);\n    }\n    addStudent(student) {\n        const students = this.getStudents();\n        students.push(student);\n        this.saveStudents(students);\n    }\n    updateStudent(studentId, updatedStudent) {\n        const students = this.getStudents();\n        const index = students.findIndex((s)=>s.id === studentId);\n        if (index !== -1) {\n            students[index] = {\n                ...students[index],\n                ...updatedStudent,\n                updatedAt: new Date()\n            };\n            this.saveStudents(students);\n        }\n    }\n    deleteStudent(studentId) {\n        const students = this.getStudents();\n        const filteredStudents = students.filter((s)=>s.id !== studentId);\n        this.saveStudents(filteredStudents);\n    }\n    getStudentById(studentId) {\n        const students = this.getStudents();\n        return students.find((s)=>s.id === studentId) || null;\n    }\n    // === إدارة المعلمين ===\n    getTeachers() {\n        return this.getItem(STORAGE_KEYS.TEACHERS) || [];\n    }\n    saveTeachers(teachers) {\n        this.setItem(STORAGE_KEYS.TEACHERS, teachers);\n    }\n    addTeacher(teacher) {\n        const teachers = this.getTeachers();\n        teachers.push(teacher);\n        this.saveTeachers(teachers);\n    }\n    updateTeacher(teacherId, updatedTeacher) {\n        const teachers = this.getTeachers();\n        const index = teachers.findIndex((t)=>t.id === teacherId);\n        if (index !== -1) {\n            teachers[index] = {\n                ...teachers[index],\n                ...updatedTeacher,\n                updatedAt: new Date()\n            };\n            this.saveTeachers(teachers);\n        }\n    }\n    deleteTeacher(teacherId) {\n        const teachers = this.getTeachers();\n        const filteredTeachers = teachers.filter((t)=>t.id !== teacherId);\n        this.saveTeachers(filteredTeachers);\n    }\n    getTeacherById(teacherId) {\n        const teachers = this.getTeachers();\n        return teachers.find((t)=>t.id === teacherId) || null;\n    }\n    // === إدارة الموظفين ===\n    getEmployees() {\n        return this.getItem(STORAGE_KEYS.EMPLOYEES) || [];\n    }\n    saveEmployees(employees) {\n        this.setItem(STORAGE_KEYS.EMPLOYEES, employees);\n    }\n    addEmployee(employee) {\n        const employees = this.getEmployees();\n        employees.push(employee);\n        this.saveEmployees(employees);\n    }\n    updateEmployee(employeeId, updatedEmployee) {\n        const employees = this.getEmployees();\n        const index = employees.findIndex((e)=>e.id === employeeId);\n        if (index !== -1) {\n            employees[index] = {\n                ...employees[index],\n                ...updatedEmployee,\n                updatedAt: new Date()\n            };\n            this.saveEmployees(employees);\n        }\n    }\n    deleteEmployee(employeeId) {\n        const employees = this.getEmployees();\n        const filteredEmployees = employees.filter((e)=>e.id !== employeeId);\n        this.saveEmployees(filteredEmployees);\n    }\n    getEmployeeById(employeeId) {\n        const employees = this.getEmployees();\n        return employees.find((e)=>e.id === employeeId) || null;\n    }\n    // === إدارة الصفوف ===\n    getClasses() {\n        return this.getItem(STORAGE_KEYS.CLASSES) || [];\n    }\n    saveClasses(classes) {\n        this.setItem(STORAGE_KEYS.CLASSES, classes);\n    }\n    addClass(classData) {\n        const classes = this.getClasses();\n        classes.push(classData);\n        this.saveClasses(classes);\n    }\n    updateClass(classId, updatedClass) {\n        const classes = this.getClasses();\n        const index = classes.findIndex((c)=>c.id === classId);\n        if (index !== -1) {\n            classes[index] = {\n                ...classes[index],\n                ...updatedClass,\n                updatedAt: new Date()\n            };\n            this.saveClasses(classes);\n        }\n    }\n    deleteClass(classId) {\n        const classes = this.getClasses();\n        const filteredClasses = classes.filter((c)=>c.id !== classId);\n        this.saveClasses(filteredClasses);\n    }\n    getClassById(classId) {\n        const classes = this.getClasses();\n        return classes.find((c)=>c.id === classId) || null;\n    }\n    // === إدارة المواد ===\n    getSubjects() {\n        return this.getItem(STORAGE_KEYS.SUBJECTS) || [];\n    }\n    saveSubjects(subjects) {\n        this.setItem(STORAGE_KEYS.SUBJECTS, subjects);\n    }\n    addSubject(subject) {\n        const subjects = this.getSubjects();\n        subjects.push(subject);\n        this.saveSubjects(subjects);\n    }\n    updateSubject(subjectId, updatedSubject) {\n        const subjects = this.getSubjects();\n        const index = subjects.findIndex((s)=>s.id === subjectId);\n        if (index !== -1) {\n            subjects[index] = {\n                ...subjects[index],\n                ...updatedSubject,\n                updatedAt: new Date()\n            };\n            this.saveSubjects(subjects);\n        }\n    }\n    deleteSubject(subjectId) {\n        const subjects = this.getSubjects();\n        const filteredSubjects = subjects.filter((s)=>s.id !== subjectId);\n        this.saveSubjects(filteredSubjects);\n    }\n    getSubjectById(subjectId) {\n        const subjects = this.getSubjects();\n        return subjects.find((s)=>s.id === subjectId) || null;\n    }\n    // === إدارة الدرجات ===\n    getGrades() {\n        return this.getItem(STORAGE_KEYS.GRADES) || [];\n    }\n    saveGrades(grades) {\n        this.setItem(STORAGE_KEYS.GRADES, grades);\n    }\n    addGrade(grade) {\n        const grades = this.getGrades();\n        grades.push(grade);\n        this.saveGrades(grades);\n    }\n    updateGrade(gradeId, updatedGrade) {\n        const grades = this.getGrades();\n        const index = grades.findIndex((g)=>g.id === gradeId);\n        if (index !== -1) {\n            grades[index] = {\n                ...grades[index],\n                ...updatedGrade,\n                updatedAt: new Date()\n            };\n            this.saveGrades(grades);\n        }\n    }\n    deleteGrade(gradeId) {\n        const grades = this.getGrades();\n        const filteredGrades = grades.filter((g)=>g.id !== gradeId);\n        this.saveGrades(filteredGrades);\n    }\n    getGradesByStudent(studentId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.studentId === studentId);\n    }\n    getGradesByClass(classId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.classId === classId);\n    }\n    // === إدارة الحضور ===\n    getAttendance() {\n        return this.getItem(STORAGE_KEYS.ATTENDANCE) || [];\n    }\n    saveAttendance(attendance) {\n        this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);\n    }\n    addAttendance(attendance) {\n        const attendanceRecords = this.getAttendance();\n        attendanceRecords.push(attendance);\n        this.saveAttendance(attendanceRecords);\n    }\n    updateAttendance(attendanceId, updatedAttendance) {\n        const attendanceRecords = this.getAttendance();\n        const index = attendanceRecords.findIndex((a)=>a.id === attendanceId);\n        if (index !== -1) {\n            attendanceRecords[index] = {\n                ...attendanceRecords[index],\n                ...updatedAttendance,\n                updatedAt: new Date()\n            };\n            this.saveAttendance(attendanceRecords);\n        }\n    }\n    getAttendanceByStudent(studentId) {\n        const attendance = this.getAttendance();\n        return attendance.filter((a)=>a.studentId === studentId);\n    }\n    getAttendanceByDate(date) {\n        const attendance = this.getAttendance();\n        const dateString = date.toDateString();\n        return attendance.filter((a)=>new Date(a.date).toDateString() === dateString);\n    }\n    // === إدارة المستخدمين ===\n    getUsers() {\n        return this.getItem(STORAGE_KEYS.USERS) || [];\n    }\n    saveUsers(users) {\n        this.setItem(STORAGE_KEYS.USERS, users);\n    }\n    getCurrentUser() {\n        return this.getItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    setCurrentUser(user) {\n        this.setItem(STORAGE_KEYS.CURRENT_USER, user);\n    }\n    logout() {\n        this.removeItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    // === إدارة الإعدادات ===\n    getSettings() {\n        return this.getItem(STORAGE_KEYS.SETTINGS);\n    }\n    saveSettings(settings) {\n        this.setItem(STORAGE_KEYS.SETTINGS, settings);\n    }\n    // === إدارة الأحداث ===\n    getEvents() {\n        return this.getItem(STORAGE_KEYS.EVENTS) || [];\n    }\n    saveEvents(events) {\n        this.setItem(STORAGE_KEYS.EVENTS, events);\n    }\n    addEvent(event) {\n        const events = this.getEvents();\n        events.push(event);\n        this.saveEvents(events);\n    }\n    // === إدارة الإشعارات ===\n    getNotifications() {\n        return this.getItem(STORAGE_KEYS.NOTIFICATIONS) || [];\n    }\n    saveNotifications(notifications) {\n        this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);\n    }\n    addNotification(notification) {\n        const notifications = this.getNotifications();\n        notifications.push(notification);\n        this.saveNotifications(notifications);\n    }\n    markNotificationAsRead(notificationId) {\n        const notifications = this.getNotifications();\n        const index = notifications.findIndex((n)=>n.id === notificationId);\n        if (index !== -1) {\n            notifications[index].isRead = true;\n            notifications[index].readAt = new Date();\n            this.saveNotifications(notifications);\n        }\n    }\n}\n// إنشاء مثيل واحد للاستخدام في التطبيق\nconst localStorageManager = new LocalStorageManager();\n// دوال مساعدة للبحث والتصفية\nconst searchUtils = {\n    searchStudents: (query, students)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return students.filter((student)=>student.name.toLowerCase().includes(lowercaseQuery) || student.studentId.toLowerCase().includes(lowercaseQuery) || student.email?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchTeachers: (query, teachers)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return teachers.filter((teacher)=>teacher.name?.toLowerCase().includes(lowercaseQuery) || teacher.fullName?.toLowerCase().includes(lowercaseQuery) || teacher.teacherId?.toLowerCase().includes(lowercaseQuery) || teacher.serialNumber?.toLowerCase().includes(lowercaseQuery) || teacher.email?.toLowerCase().includes(lowercaseQuery) || teacher.specialization?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchEmployees: (query, employees)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return employees.filter((employee)=>employee.name?.toLowerCase().includes(lowercaseQuery) || employee.fullName?.toLowerCase().includes(lowercaseQuery) || employee.employeeId?.toLowerCase().includes(lowercaseQuery) || employee.serialNumber?.toLowerCase().includes(lowercaseQuery) || employee.email?.toLowerCase().includes(lowercaseQuery) || employee.department?.toLowerCase().includes(lowercaseQuery) || employee.position?.toLowerCase().includes(lowercaseQuery));\n    },\n    filterStudentsByClass: (classId, students)=>{\n        return students.filter((student)=>student.classId === classId);\n    },\n    filterGradesByDateRange: (startDate, endDate, grades)=>{\n        return grades.filter((grade)=>{\n            const gradeDate = new Date(grade.examDate);\n            return gradeDate >= startDate && gradeDate <= endDate;\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvbG9jYWxTdG9yYWdlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBY0Esd0JBQXdCO0FBQ3hCLE1BQU1BLGVBQWU7SUFDbkJDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLFNBQVM7SUFDVEMsVUFBVTtJQUNWQyxRQUFRO0lBQ1JDLFlBQVk7SUFDWkMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFFBQVE7SUFDUkMsZUFBZTtJQUNmQyxjQUFjO0lBQ2RDLGVBQWU7SUFDZkMsa0JBQWtCO0FBQ3BCO0FBRUEsMkJBQTJCO0FBQzNCLE1BQU1DO0lBQ0osZUFBZTtJQUNQQyxRQUFXQyxHQUFXLEVBQUVDLElBQU8sRUFBUTtRQUM3QyxJQUFJO1lBQ0YsTUFBTUMsaUJBQWlCQyxLQUFLQyxTQUFTLENBQUNIO1lBQ3RDSSxhQUFhTixPQUFPLENBQUNDLEtBQUtFO1FBQzVCLEVBQUUsT0FBT0ksT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyw0QkFBNEIsRUFBRU4sSUFBSSxDQUFDLENBQUMsRUFBRU07UUFDdkQ7SUFDRjtJQUVBLG1CQUFtQjtJQUNYRSxRQUFXUixHQUFXLEVBQVk7UUFDeEMsSUFBSTtZQUNGLE1BQU1FLGlCQUFpQkcsYUFBYUcsT0FBTyxDQUFDUjtZQUM1QyxJQUFJRSxtQkFBbUIsTUFBTTtnQkFDM0IsT0FBTztZQUNUO1lBQ0EsT0FBT0MsS0FBS00sS0FBSyxDQUFDUDtRQUNwQixFQUFFLE9BQU9JLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsZ0NBQWdDLEVBQUVOLElBQUksQ0FBQyxDQUFDLEVBQUVNO1lBQ3pELE9BQU87UUFDVDtJQUNGO0lBRUEsZUFBZTtJQUNQSSxXQUFXVixHQUFXLEVBQVE7UUFDcEMsSUFBSTtZQUNGSyxhQUFhSyxVQUFVLENBQUNWO1FBQzFCLEVBQUUsT0FBT00sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyw0QkFBNEIsRUFBRU4sSUFBSSxDQUFDLENBQUMsRUFBRU07UUFDdkQ7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQkssV0FBaUI7UUFDZkMsT0FBT0MsTUFBTSxDQUFDOUIsY0FBYytCLE9BQU8sQ0FBQ2QsQ0FBQUE7WUFDbEMsSUFBSSxDQUFDVSxVQUFVLENBQUNWO1FBQ2xCO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkJlLGNBQXlCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDUCxPQUFPLENBQVl6QixhQUFhQyxRQUFRLEtBQUssRUFBRTtJQUM3RDtJQUVBZ0MsYUFBYUMsUUFBbUIsRUFBUTtRQUN0QyxJQUFJLENBQUNsQixPQUFPLENBQUNoQixhQUFhQyxRQUFRLEVBQUVpQztJQUN0QztJQUVBQyxXQUFXQyxPQUFnQixFQUFRO1FBQ2pDLE1BQU1GLFdBQVcsSUFBSSxDQUFDRixXQUFXO1FBQ2pDRSxTQUFTRyxJQUFJLENBQUNEO1FBQ2QsSUFBSSxDQUFDSCxZQUFZLENBQUNDO0lBQ3BCO0lBRUFJLGNBQWNDLFNBQWlCLEVBQUVDLGNBQWdDLEVBQVE7UUFDdkUsTUFBTU4sV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTVMsUUFBUVAsU0FBU1EsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtMO1FBQy9DLElBQUlFLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCUCxRQUFRLENBQUNPLE1BQU0sR0FBRztnQkFBRSxHQUFHUCxRQUFRLENBQUNPLE1BQU07Z0JBQUUsR0FBR0QsY0FBYztnQkFBRUssV0FBVyxJQUFJQztZQUFPO1lBQ2pGLElBQUksQ0FBQ2IsWUFBWSxDQUFDQztRQUNwQjtJQUNGO0lBRUFhLGNBQWNSLFNBQWlCLEVBQVE7UUFDckMsTUFBTUwsV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTWdCLG1CQUFtQmQsU0FBU2UsTUFBTSxDQUFDTixDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtMO1FBQ3ZELElBQUksQ0FBQ04sWUFBWSxDQUFDZTtJQUNwQjtJQUVBRSxlQUFlWCxTQUFpQixFQUFrQjtRQUNoRCxNQUFNTCxXQUFXLElBQUksQ0FBQ0YsV0FBVztRQUNqQyxPQUFPRSxTQUFTaUIsSUFBSSxDQUFDUixDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtMLGNBQWM7SUFDbkQ7SUFFQSx5QkFBeUI7SUFDekJhLGNBQXlCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDM0IsT0FBTyxDQUFZekIsYUFBYUUsUUFBUSxLQUFLLEVBQUU7SUFDN0Q7SUFFQW1ELGFBQWFDLFFBQW1CLEVBQVE7UUFDdEMsSUFBSSxDQUFDdEMsT0FBTyxDQUFDaEIsYUFBYUUsUUFBUSxFQUFFb0Q7SUFDdEM7SUFFQUMsV0FBV0MsT0FBZ0IsRUFBUTtRQUNqQyxNQUFNRixXQUFXLElBQUksQ0FBQ0YsV0FBVztRQUNqQ0UsU0FBU2pCLElBQUksQ0FBQ21CO1FBQ2QsSUFBSSxDQUFDSCxZQUFZLENBQUNDO0lBQ3BCO0lBRUFHLGNBQWNDLFNBQWlCLEVBQUVDLGNBQWdDLEVBQVE7UUFDdkUsTUFBTUwsV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTVgsUUFBUWEsU0FBU1osU0FBUyxDQUFDa0IsQ0FBQUEsSUFBS0EsRUFBRWhCLEVBQUUsS0FBS2M7UUFDL0MsSUFBSWpCLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCYSxRQUFRLENBQUNiLE1BQU0sR0FBRztnQkFBRSxHQUFHYSxRQUFRLENBQUNiLE1BQU07Z0JBQUUsR0FBR2tCLGNBQWM7Z0JBQUVkLFdBQVcsSUFBSUM7WUFBTztZQUNqRixJQUFJLENBQUNPLFlBQVksQ0FBQ0M7UUFDcEI7SUFDRjtJQUVBTyxjQUFjSCxTQUFpQixFQUFRO1FBQ3JDLE1BQU1KLFdBQVcsSUFBSSxDQUFDRixXQUFXO1FBQ2pDLE1BQU1VLG1CQUFtQlIsU0FBU0wsTUFBTSxDQUFDVyxDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLYztRQUN2RCxJQUFJLENBQUNMLFlBQVksQ0FBQ1M7SUFDcEI7SUFFQUMsZUFBZUwsU0FBaUIsRUFBa0I7UUFDaEQsTUFBTUosV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsT0FBT0UsU0FBU0gsSUFBSSxDQUFDUyxDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLYyxjQUFjO0lBQ25EO0lBRUEseUJBQXlCO0lBQ3pCTSxlQUEyQjtRQUN6QixPQUFPLElBQUksQ0FBQ3ZDLE9BQU8sQ0FBYXpCLGFBQWFHLFNBQVMsS0FBSyxFQUFFO0lBQy9EO0lBRUE4RCxjQUFjQyxTQUFxQixFQUFRO1FBQ3pDLElBQUksQ0FBQ2xELE9BQU8sQ0FBQ2hCLGFBQWFHLFNBQVMsRUFBRStEO0lBQ3ZDO0lBRUFDLFlBQVlDLFFBQWtCLEVBQVE7UUFDcEMsTUFBTUYsWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkNFLFVBQVU3QixJQUFJLENBQUMrQjtRQUNmLElBQUksQ0FBQ0gsYUFBYSxDQUFDQztJQUNyQjtJQUVBRyxlQUFlQyxVQUFrQixFQUFFQyxlQUFrQyxFQUFRO1FBQzNFLE1BQU1MLFlBQVksSUFBSSxDQUFDRixZQUFZO1FBQ25DLE1BQU12QixRQUFReUIsVUFBVXhCLFNBQVMsQ0FBQzhCLENBQUFBLElBQUtBLEVBQUU1QixFQUFFLEtBQUswQjtRQUNoRCxJQUFJN0IsVUFBVSxDQUFDLEdBQUc7WUFDaEJ5QixTQUFTLENBQUN6QixNQUFNLEdBQUc7Z0JBQUUsR0FBR3lCLFNBQVMsQ0FBQ3pCLE1BQU07Z0JBQUUsR0FBRzhCLGVBQWU7Z0JBQUUxQixXQUFXLElBQUlDO1lBQU87WUFDcEYsSUFBSSxDQUFDbUIsYUFBYSxDQUFDQztRQUNyQjtJQUNGO0lBRUFPLGVBQWVILFVBQWtCLEVBQVE7UUFDdkMsTUFBTUosWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkMsTUFBTVUsb0JBQW9CUixVQUFVakIsTUFBTSxDQUFDdUIsQ0FBQUEsSUFBS0EsRUFBRTVCLEVBQUUsS0FBSzBCO1FBQ3pELElBQUksQ0FBQ0wsYUFBYSxDQUFDUztJQUNyQjtJQUVBQyxnQkFBZ0JMLFVBQWtCLEVBQW1CO1FBQ25ELE1BQU1KLFlBQVksSUFBSSxDQUFDRixZQUFZO1FBQ25DLE9BQU9FLFVBQVVmLElBQUksQ0FBQ3FCLENBQUFBLElBQUtBLEVBQUU1QixFQUFFLEtBQUswQixlQUFlO0lBQ3JEO0lBRUEsdUJBQXVCO0lBQ3ZCTSxhQUFzQjtRQUNwQixPQUFPLElBQUksQ0FBQ25ELE9BQU8sQ0FBVXpCLGFBQWFJLE9BQU8sS0FBSyxFQUFFO0lBQzFEO0lBRUF5RSxZQUFZQyxPQUFnQixFQUFRO1FBQ2xDLElBQUksQ0FBQzlELE9BQU8sQ0FBQ2hCLGFBQWFJLE9BQU8sRUFBRTBFO0lBQ3JDO0lBRUFDLFNBQVNDLFNBQWdCLEVBQVE7UUFDL0IsTUFBTUYsVUFBVSxJQUFJLENBQUNGLFVBQVU7UUFDL0JFLFFBQVF6QyxJQUFJLENBQUMyQztRQUNiLElBQUksQ0FBQ0gsV0FBVyxDQUFDQztJQUNuQjtJQUVBRyxZQUFZQyxPQUFlLEVBQUVDLFlBQTRCLEVBQVE7UUFDL0QsTUFBTUwsVUFBVSxJQUFJLENBQUNGLFVBQVU7UUFDL0IsTUFBTW5DLFFBQVFxQyxRQUFRcEMsU0FBUyxDQUFDMEMsQ0FBQUEsSUFBS0EsRUFBRXhDLEVBQUUsS0FBS3NDO1FBQzlDLElBQUl6QyxVQUFVLENBQUMsR0FBRztZQUNoQnFDLE9BQU8sQ0FBQ3JDLE1BQU0sR0FBRztnQkFBRSxHQUFHcUMsT0FBTyxDQUFDckMsTUFBTTtnQkFBRSxHQUFHMEMsWUFBWTtnQkFBRXRDLFdBQVcsSUFBSUM7WUFBTztZQUM3RSxJQUFJLENBQUMrQixXQUFXLENBQUNDO1FBQ25CO0lBQ0Y7SUFFQU8sWUFBWUgsT0FBZSxFQUFRO1FBQ2pDLE1BQU1KLFVBQVUsSUFBSSxDQUFDRixVQUFVO1FBQy9CLE1BQU1VLGtCQUFrQlIsUUFBUTdCLE1BQU0sQ0FBQ21DLENBQUFBLElBQUtBLEVBQUV4QyxFQUFFLEtBQUtzQztRQUNyRCxJQUFJLENBQUNMLFdBQVcsQ0FBQ1M7SUFDbkI7SUFFQUMsYUFBYUwsT0FBZSxFQUFnQjtRQUMxQyxNQUFNSixVQUFVLElBQUksQ0FBQ0YsVUFBVTtRQUMvQixPQUFPRSxRQUFRM0IsSUFBSSxDQUFDaUMsQ0FBQUEsSUFBS0EsRUFBRXhDLEVBQUUsS0FBS3NDLFlBQVk7SUFDaEQ7SUFFQSx1QkFBdUI7SUFDdkJNLGNBQXlCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDL0QsT0FBTyxDQUFZekIsYUFBYUssUUFBUSxLQUFLLEVBQUU7SUFDN0Q7SUFFQW9GLGFBQWFDLFFBQW1CLEVBQVE7UUFDdEMsSUFBSSxDQUFDMUUsT0FBTyxDQUFDaEIsYUFBYUssUUFBUSxFQUFFcUY7SUFDdEM7SUFFQUMsV0FBV0MsT0FBZ0IsRUFBUTtRQUNqQyxNQUFNRixXQUFXLElBQUksQ0FBQ0YsV0FBVztRQUNqQ0UsU0FBU3JELElBQUksQ0FBQ3VEO1FBQ2QsSUFBSSxDQUFDSCxZQUFZLENBQUNDO0lBQ3BCO0lBRUFHLGNBQWNDLFNBQWlCLEVBQUVDLGNBQWdDLEVBQVE7UUFDdkUsTUFBTUwsV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTS9DLFFBQVFpRCxTQUFTaEQsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtrRDtRQUMvQyxJQUFJckQsVUFBVSxDQUFDLEdBQUc7WUFDaEJpRCxRQUFRLENBQUNqRCxNQUFNLEdBQUc7Z0JBQUUsR0FBR2lELFFBQVEsQ0FBQ2pELE1BQU07Z0JBQUUsR0FBR3NELGNBQWM7Z0JBQUVsRCxXQUFXLElBQUlDO1lBQU87WUFDakYsSUFBSSxDQUFDMkMsWUFBWSxDQUFDQztRQUNwQjtJQUNGO0lBRUFNLGNBQWNGLFNBQWlCLEVBQVE7UUFDckMsTUFBTUosV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTVMsbUJBQW1CUCxTQUFTekMsTUFBTSxDQUFDTixDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtrRDtRQUN2RCxJQUFJLENBQUNMLFlBQVksQ0FBQ1E7SUFDcEI7SUFFQUMsZUFBZUosU0FBaUIsRUFBa0I7UUFDaEQsTUFBTUosV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsT0FBT0UsU0FBU3ZDLElBQUksQ0FBQ1IsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLa0QsY0FBYztJQUNuRDtJQUVBLHdCQUF3QjtJQUN4QkssWUFBcUI7UUFDbkIsT0FBTyxJQUFJLENBQUMxRSxPQUFPLENBQVV6QixhQUFhTSxNQUFNLEtBQUssRUFBRTtJQUN6RDtJQUVBOEYsV0FBV0MsTUFBZSxFQUFRO1FBQ2hDLElBQUksQ0FBQ3JGLE9BQU8sQ0FBQ2hCLGFBQWFNLE1BQU0sRUFBRStGO0lBQ3BDO0lBRUFDLFNBQVNDLEtBQVksRUFBUTtRQUMzQixNQUFNRixTQUFTLElBQUksQ0FBQ0YsU0FBUztRQUM3QkUsT0FBT2hFLElBQUksQ0FBQ2tFO1FBQ1osSUFBSSxDQUFDSCxVQUFVLENBQUNDO0lBQ2xCO0lBRUFHLFlBQVlDLE9BQWUsRUFBRUMsWUFBNEIsRUFBUTtRQUMvRCxNQUFNTCxTQUFTLElBQUksQ0FBQ0YsU0FBUztRQUM3QixNQUFNMUQsUUFBUTRELE9BQU8zRCxTQUFTLENBQUNpRSxDQUFBQSxJQUFLQSxFQUFFL0QsRUFBRSxLQUFLNkQ7UUFDN0MsSUFBSWhFLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCNEQsTUFBTSxDQUFDNUQsTUFBTSxHQUFHO2dCQUFFLEdBQUc0RCxNQUFNLENBQUM1RCxNQUFNO2dCQUFFLEdBQUdpRSxZQUFZO2dCQUFFN0QsV0FBVyxJQUFJQztZQUFPO1lBQzNFLElBQUksQ0FBQ3NELFVBQVUsQ0FBQ0M7UUFDbEI7SUFDRjtJQUVBTyxZQUFZSCxPQUFlLEVBQVE7UUFDakMsTUFBTUosU0FBUyxJQUFJLENBQUNGLFNBQVM7UUFDN0IsTUFBTVUsaUJBQWlCUixPQUFPcEQsTUFBTSxDQUFDMEQsQ0FBQUEsSUFBS0EsRUFBRS9ELEVBQUUsS0FBSzZEO1FBQ25ELElBQUksQ0FBQ0wsVUFBVSxDQUFDUztJQUNsQjtJQUVBQyxtQkFBbUJ2RSxTQUFpQixFQUFXO1FBQzdDLE1BQU04RCxTQUFTLElBQUksQ0FBQ0YsU0FBUztRQUM3QixPQUFPRSxPQUFPcEQsTUFBTSxDQUFDMEQsQ0FBQUEsSUFBS0EsRUFBRXBFLFNBQVMsS0FBS0E7SUFDNUM7SUFFQXdFLGlCQUFpQjdCLE9BQWUsRUFBVztRQUN6QyxNQUFNbUIsU0FBUyxJQUFJLENBQUNGLFNBQVM7UUFDN0IsT0FBT0UsT0FBT3BELE1BQU0sQ0FBQzBELENBQUFBLElBQUtBLEVBQUV6QixPQUFPLEtBQUtBO0lBQzFDO0lBRUEsdUJBQXVCO0lBQ3ZCOEIsZ0JBQThCO1FBQzVCLE9BQU8sSUFBSSxDQUFDdkYsT0FBTyxDQUFlekIsYUFBYU8sVUFBVSxLQUFLLEVBQUU7SUFDbEU7SUFFQTBHLGVBQWVDLFVBQXdCLEVBQVE7UUFDN0MsSUFBSSxDQUFDbEcsT0FBTyxDQUFDaEIsYUFBYU8sVUFBVSxFQUFFMkc7SUFDeEM7SUFFQUMsY0FBY0QsVUFBc0IsRUFBUTtRQUMxQyxNQUFNRSxvQkFBb0IsSUFBSSxDQUFDSixhQUFhO1FBQzVDSSxrQkFBa0IvRSxJQUFJLENBQUM2RTtRQUN2QixJQUFJLENBQUNELGNBQWMsQ0FBQ0c7SUFDdEI7SUFFQUMsaUJBQWlCQyxZQUFvQixFQUFFQyxpQkFBc0MsRUFBUTtRQUNuRixNQUFNSCxvQkFBb0IsSUFBSSxDQUFDSixhQUFhO1FBQzVDLE1BQU12RSxRQUFRMkUsa0JBQWtCMUUsU0FBUyxDQUFDOEUsQ0FBQUEsSUFBS0EsRUFBRTVFLEVBQUUsS0FBSzBFO1FBQ3hELElBQUk3RSxVQUFVLENBQUMsR0FBRztZQUNoQjJFLGlCQUFpQixDQUFDM0UsTUFBTSxHQUFHO2dCQUFFLEdBQUcyRSxpQkFBaUIsQ0FBQzNFLE1BQU07Z0JBQUUsR0FBRzhFLGlCQUFpQjtnQkFBRTFFLFdBQVcsSUFBSUM7WUFBTztZQUN0RyxJQUFJLENBQUNtRSxjQUFjLENBQUNHO1FBQ3RCO0lBQ0Y7SUFFQUssdUJBQXVCbEYsU0FBaUIsRUFBZ0I7UUFDdEQsTUFBTTJFLGFBQWEsSUFBSSxDQUFDRixhQUFhO1FBQ3JDLE9BQU9FLFdBQVdqRSxNQUFNLENBQUN1RSxDQUFBQSxJQUFLQSxFQUFFakYsU0FBUyxLQUFLQTtJQUNoRDtJQUVBbUYsb0JBQW9CQyxJQUFVLEVBQWdCO1FBQzVDLE1BQU1ULGFBQWEsSUFBSSxDQUFDRixhQUFhO1FBQ3JDLE1BQU1ZLGFBQWFELEtBQUtFLFlBQVk7UUFDcEMsT0FBT1gsV0FBV2pFLE1BQU0sQ0FBQ3VFLENBQUFBLElBQUssSUFBSTFFLEtBQUswRSxFQUFFRyxJQUFJLEVBQUVFLFlBQVksT0FBT0Q7SUFDcEU7SUFFQSwyQkFBMkI7SUFDM0JFLFdBQW1CO1FBQ2pCLE9BQU8sSUFBSSxDQUFDckcsT0FBTyxDQUFTekIsYUFBYVEsS0FBSyxLQUFLLEVBQUU7SUFDdkQ7SUFFQXVILFVBQVVDLEtBQWEsRUFBUTtRQUM3QixJQUFJLENBQUNoSCxPQUFPLENBQUNoQixhQUFhUSxLQUFLLEVBQUV3SDtJQUNuQztJQUVBQyxpQkFBOEI7UUFDNUIsT0FBTyxJQUFJLENBQUN4RyxPQUFPLENBQU96QixhQUFhWSxZQUFZO0lBQ3JEO0lBRUFzSCxlQUFlQyxJQUFVLEVBQVE7UUFDL0IsSUFBSSxDQUFDbkgsT0FBTyxDQUFDaEIsYUFBYVksWUFBWSxFQUFFdUg7SUFDMUM7SUFFQUMsU0FBZTtRQUNiLElBQUksQ0FBQ3pHLFVBQVUsQ0FBQzNCLGFBQWFZLFlBQVk7SUFDM0M7SUFFQSwwQkFBMEI7SUFDMUJ5SCxjQUErQjtRQUM3QixPQUFPLElBQUksQ0FBQzVHLE9BQU8sQ0FBV3pCLGFBQWFTLFFBQVE7SUFDckQ7SUFFQTZILGFBQWFDLFFBQWtCLEVBQVE7UUFDckMsSUFBSSxDQUFDdkgsT0FBTyxDQUFDaEIsYUFBYVMsUUFBUSxFQUFFOEg7SUFDdEM7SUFFQSx3QkFBd0I7SUFDeEJDLFlBQXFCO1FBQ25CLE9BQU8sSUFBSSxDQUFDL0csT0FBTyxDQUFVekIsYUFBYVUsTUFBTSxLQUFLLEVBQUU7SUFDekQ7SUFFQStILFdBQVdDLE1BQWUsRUFBUTtRQUNoQyxJQUFJLENBQUMxSCxPQUFPLENBQUNoQixhQUFhVSxNQUFNLEVBQUVnSTtJQUNwQztJQUVBQyxTQUFTQyxLQUFZLEVBQVE7UUFDM0IsTUFBTUYsU0FBUyxJQUFJLENBQUNGLFNBQVM7UUFDN0JFLE9BQU9yRyxJQUFJLENBQUN1RztRQUNaLElBQUksQ0FBQ0gsVUFBVSxDQUFDQztJQUNsQjtJQUVBLDBCQUEwQjtJQUMxQkcsbUJBQW1DO1FBQ2pDLE9BQU8sSUFBSSxDQUFDcEgsT0FBTyxDQUFpQnpCLGFBQWFXLGFBQWEsS0FBSyxFQUFFO0lBQ3ZFO0lBRUFtSSxrQkFBa0JDLGFBQTZCLEVBQVE7UUFDckQsSUFBSSxDQUFDL0gsT0FBTyxDQUFDaEIsYUFBYVcsYUFBYSxFQUFFb0k7SUFDM0M7SUFFQUMsZ0JBQWdCQyxZQUEwQixFQUFRO1FBQ2hELE1BQU1GLGdCQUFnQixJQUFJLENBQUNGLGdCQUFnQjtRQUMzQ0UsY0FBYzFHLElBQUksQ0FBQzRHO1FBQ25CLElBQUksQ0FBQ0gsaUJBQWlCLENBQUNDO0lBQ3pCO0lBRUFHLHVCQUF1QkMsY0FBc0IsRUFBUTtRQUNuRCxNQUFNSixnQkFBZ0IsSUFBSSxDQUFDRixnQkFBZ0I7UUFDM0MsTUFBTXBHLFFBQVFzRyxjQUFjckcsU0FBUyxDQUFDMEcsQ0FBQUEsSUFBS0EsRUFBRXhHLEVBQUUsS0FBS3VHO1FBQ3BELElBQUkxRyxVQUFVLENBQUMsR0FBRztZQUNoQnNHLGFBQWEsQ0FBQ3RHLE1BQU0sQ0FBQzRHLE1BQU0sR0FBRztZQUM5Qk4sYUFBYSxDQUFDdEcsTUFBTSxDQUFDNkcsTUFBTSxHQUFHLElBQUl4RztZQUNsQyxJQUFJLENBQUNnRyxpQkFBaUIsQ0FBQ0M7UUFDekI7SUFDRjtBQUNGO0FBRUEsdUNBQXVDO0FBQ2hDLE1BQU1RLHNCQUFzQixJQUFJeEksc0JBQXNCO0FBRTdELDZCQUE2QjtBQUN0QixNQUFNeUksY0FBYztJQUN6QkMsZ0JBQWdCLENBQUNDLE9BQWV4SDtRQUM5QixNQUFNeUgsaUJBQWlCRCxNQUFNRSxXQUFXO1FBQ3hDLE9BQU8xSCxTQUFTZSxNQUFNLENBQUNiLENBQUFBLFVBQ3JCQSxRQUFReUgsSUFBSSxDQUFDRCxXQUFXLEdBQUdFLFFBQVEsQ0FBQ0gsbUJBQ3BDdkgsUUFBUUcsU0FBUyxDQUFDcUgsV0FBVyxHQUFHRSxRQUFRLENBQUNILG1CQUN6Q3ZILFFBQVEySCxLQUFLLEVBQUVILGNBQWNFLFNBQVNIO0lBRTFDO0lBRUFLLGdCQUFnQixDQUFDTixPQUFlcEc7UUFDOUIsTUFBTXFHLGlCQUFpQkQsTUFBTUUsV0FBVztRQUN4QyxPQUFPdEcsU0FBU0wsTUFBTSxDQUFDTyxDQUFBQSxVQUNyQkEsUUFBUXFHLElBQUksRUFBRUQsY0FBY0UsU0FBU0gsbUJBQ3JDbkcsUUFBUXlHLFFBQVEsRUFBRUwsY0FBY0UsU0FBU0gsbUJBQ3pDbkcsUUFBUUUsU0FBUyxFQUFFa0csY0FBY0UsU0FBU0gsbUJBQzFDbkcsUUFBUTBHLFlBQVksRUFBRU4sY0FBY0UsU0FBU0gsbUJBQzdDbkcsUUFBUXVHLEtBQUssRUFBRUgsY0FBY0UsU0FBU0gsbUJBQ3RDbkcsUUFBUTJHLGNBQWMsRUFBRVAsY0FBY0UsU0FBU0g7SUFFbkQ7SUFFQVMsaUJBQWlCLENBQUNWLE9BQWV4RjtRQUMvQixNQUFNeUYsaUJBQWlCRCxNQUFNRSxXQUFXO1FBQ3hDLE9BQU8xRixVQUFVakIsTUFBTSxDQUFDbUIsQ0FBQUEsV0FDdEJBLFNBQVN5RixJQUFJLEVBQUVELGNBQWNFLFNBQVNILG1CQUN0Q3ZGLFNBQVM2RixRQUFRLEVBQUVMLGNBQWNFLFNBQVNILG1CQUMxQ3ZGLFNBQVNFLFVBQVUsRUFBRXNGLGNBQWNFLFNBQVNILG1CQUM1Q3ZGLFNBQVM4RixZQUFZLEVBQUVOLGNBQWNFLFNBQVNILG1CQUM5Q3ZGLFNBQVMyRixLQUFLLEVBQUVILGNBQWNFLFNBQVNILG1CQUN2Q3ZGLFNBQVNpRyxVQUFVLEVBQUVULGNBQWNFLFNBQVNILG1CQUM1Q3ZGLFNBQVNrRyxRQUFRLEVBQUVWLGNBQWNFLFNBQVNIO0lBRTlDO0lBRUFZLHVCQUF1QixDQUFDckYsU0FBaUJoRDtRQUN2QyxPQUFPQSxTQUFTZSxNQUFNLENBQUNiLENBQUFBLFVBQVdBLFFBQVE4QyxPQUFPLEtBQUtBO0lBQ3hEO0lBRUFzRix5QkFBeUIsQ0FBQ0MsV0FBaUJDLFNBQWVyRTtRQUN4RCxPQUFPQSxPQUFPcEQsTUFBTSxDQUFDc0QsQ0FBQUE7WUFDbkIsTUFBTW9FLFlBQVksSUFBSTdILEtBQUt5RCxNQUFNcUUsUUFBUTtZQUN6QyxPQUFPRCxhQUFhRixhQUFhRSxhQUFhRDtRQUNoRDtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxzcmNcXHV0aWxzXFxsb2NhbFN0b3JhZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgU3R1ZGVudCxcbiAgVGVhY2hlcixcbiAgRW1wbG95ZWUsXG4gIENsYXNzLFxuICBTdWJqZWN0LFxuICBHcmFkZSxcbiAgQXR0ZW5kYW5jZSxcbiAgVXNlcixcbiAgU2V0dGluZ3MsXG4gIEV2ZW50LFxuICBOb3RpZmljYXRpb25cbn0gZnJvbSAnQC90eXBlcyc7XG5cbi8vINmF2YHYp9iq2YrYrSDYp9mE2KrYrtiy2YrZhiDYp9mE2YXYrdmE2YpcbmNvbnN0IFNUT1JBR0VfS0VZUyA9IHtcbiAgU1RVREVOVFM6ICdzY2hvb2xfc3R1ZGVudHMnLFxuICBURUFDSEVSUzogJ3NjaG9vbF90ZWFjaGVycycsXG4gIEVNUExPWUVFUzogJ3NjaG9vbF9lbXBsb3llZXMnLFxuICBDTEFTU0VTOiAnc2Nob29sX2NsYXNzZXMnLFxuICBTVUJKRUNUUzogJ3NjaG9vbF9zdWJqZWN0cycsXG4gIEdSQURFUzogJ3NjaG9vbF9ncmFkZXMnLFxuICBBVFRFTkRBTkNFOiAnc2Nob29sX2F0dGVuZGFuY2UnLFxuICBVU0VSUzogJ3NjaG9vbF91c2VycycsXG4gIFNFVFRJTkdTOiAnc2Nob29sX3NldHRpbmdzJyxcbiAgRVZFTlRTOiAnc2Nob29sX2V2ZW50cycsXG4gIE5PVElGSUNBVElPTlM6ICdzY2hvb2xfbm90aWZpY2F0aW9ucycsXG4gIENVUlJFTlRfVVNFUjogJ3NjaG9vbF9jdXJyZW50X3VzZXInLFxuICBBQ0FERU1JQ19ZRUFSOiAnc2Nob29sX2FjYWRlbWljX3llYXInLFxuICBDVVJSRU5UX1NFTUVTVEVSOiAnc2Nob29sX2N1cnJlbnRfc2VtZXN0ZXInXG59O1xuXG4vLyDZgdim2Kkg2KXYr9in2LHYqSDYp9mE2KrYrtiy2YrZhiDYp9mE2YXYrdmE2YpcbmNsYXNzIExvY2FsU3RvcmFnZU1hbmFnZXIge1xuICAvLyDYrdmB2Lgg2KfZhNio2YrYp9mG2KfYqlxuICBwcml2YXRlIHNldEl0ZW08VD4oa2V5OiBzdHJpbmcsIGRhdGE6IFQpOiB2b2lkIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc2VyaWFsaXplZERhdGEgPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGtleSwgc2VyaWFsaXplZERhdGEpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGDYrti32KMg2YHZiiDYrdmB2Lgg2KfZhNio2YrYp9mG2KfYqiDZhNmE2YXZgdiq2KfYrSAke2tleX06YCwgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIC8vINin2LPYqtix2KzYp9i5INin2YTYqNmK2KfZhtin2KpcbiAgcHJpdmF0ZSBnZXRJdGVtPFQ+KGtleTogc3RyaW5nKTogVCB8IG51bGwge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXJpYWxpemVkRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgICBpZiAoc2VyaWFsaXplZERhdGEgPT09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShzZXJpYWxpemVkRGF0YSkgYXMgVDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihg2K7Yt9ijINmB2Yog2KfYs9iq2LHYrNin2Lkg2KfZhNio2YrYp9mG2KfYqiDZhNmE2YXZgdiq2KfYrSAke2tleX06YCwgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgLy8g2K3YsNmBINin2YTYqNmK2KfZhtin2KpcbiAgcHJpdmF0ZSByZW1vdmVJdGVtKGtleTogc3RyaW5nKTogdm9pZCB7XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYNiu2LfYoyDZgdmKINit2LDZgSDYp9mE2KjZitin2YbYp9iqINmE2YTZhdmB2KrYp9itICR7a2V5fTpgLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLy8g2YXYs9itINis2YXZiti5INin2YTYqNmK2KfZhtin2KpcbiAgY2xlYXJBbGwoKTogdm9pZCB7XG4gICAgT2JqZWN0LnZhbHVlcyhTVE9SQUdFX0tFWVMpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIHRoaXMucmVtb3ZlSXRlbShrZXkpO1xuICAgIH0pO1xuICB9XG5cbiAgLy8gPT09INil2K/Yp9ix2Kkg2KfZhNi32YTYp9ioID09PVxuICBnZXRTdHVkZW50cygpOiBTdHVkZW50W10ge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08U3R1ZGVudFtdPihTVE9SQUdFX0tFWVMuU1RVREVOVFMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZVN0dWRlbnRzKHN0dWRlbnRzOiBTdHVkZW50W10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLlNUVURFTlRTLCBzdHVkZW50cyk7XG4gIH1cblxuICBhZGRTdHVkZW50KHN0dWRlbnQ6IFN0dWRlbnQpOiB2b2lkIHtcbiAgICBjb25zdCBzdHVkZW50cyA9IHRoaXMuZ2V0U3R1ZGVudHMoKTtcbiAgICBzdHVkZW50cy5wdXNoKHN0dWRlbnQpO1xuICAgIHRoaXMuc2F2ZVN0dWRlbnRzKHN0dWRlbnRzKTtcbiAgfVxuXG4gIHVwZGF0ZVN0dWRlbnQoc3R1ZGVudElkOiBzdHJpbmcsIHVwZGF0ZWRTdHVkZW50OiBQYXJ0aWFsPFN0dWRlbnQ+KTogdm9pZCB7XG4gICAgY29uc3Qgc3R1ZGVudHMgPSB0aGlzLmdldFN0dWRlbnRzKCk7XG4gICAgY29uc3QgaW5kZXggPSBzdHVkZW50cy5maW5kSW5kZXgocyA9PiBzLmlkID09PSBzdHVkZW50SWQpO1xuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHN0dWRlbnRzW2luZGV4XSA9IHsgLi4uc3R1ZGVudHNbaW5kZXhdLCAuLi51cGRhdGVkU3R1ZGVudCwgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpIH07XG4gICAgICB0aGlzLnNhdmVTdHVkZW50cyhzdHVkZW50cyk7XG4gICAgfVxuICB9XG5cbiAgZGVsZXRlU3R1ZGVudChzdHVkZW50SWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHN0dWRlbnRzID0gdGhpcy5nZXRTdHVkZW50cygpO1xuICAgIGNvbnN0IGZpbHRlcmVkU3R1ZGVudHMgPSBzdHVkZW50cy5maWx0ZXIocyA9PiBzLmlkICE9PSBzdHVkZW50SWQpO1xuICAgIHRoaXMuc2F2ZVN0dWRlbnRzKGZpbHRlcmVkU3R1ZGVudHMpO1xuICB9XG5cbiAgZ2V0U3R1ZGVudEJ5SWQoc3R1ZGVudElkOiBzdHJpbmcpOiBTdHVkZW50IHwgbnVsbCB7XG4gICAgY29uc3Qgc3R1ZGVudHMgPSB0aGlzLmdldFN0dWRlbnRzKCk7XG4gICAgcmV0dXJuIHN0dWRlbnRzLmZpbmQocyA9PiBzLmlkID09PSBzdHVkZW50SWQpIHx8IG51bGw7XG4gIH1cblxuICAvLyA9PT0g2KXYr9in2LHYqSDYp9mE2YXYudmE2YXZitmGID09PVxuICBnZXRUZWFjaGVycygpOiBUZWFjaGVyW10ge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08VGVhY2hlcltdPihTVE9SQUdFX0tFWVMuVEVBQ0hFUlMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZVRlYWNoZXJzKHRlYWNoZXJzOiBUZWFjaGVyW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLlRFQUNIRVJTLCB0ZWFjaGVycyk7XG4gIH1cblxuICBhZGRUZWFjaGVyKHRlYWNoZXI6IFRlYWNoZXIpOiB2b2lkIHtcbiAgICBjb25zdCB0ZWFjaGVycyA9IHRoaXMuZ2V0VGVhY2hlcnMoKTtcbiAgICB0ZWFjaGVycy5wdXNoKHRlYWNoZXIpO1xuICAgIHRoaXMuc2F2ZVRlYWNoZXJzKHRlYWNoZXJzKTtcbiAgfVxuXG4gIHVwZGF0ZVRlYWNoZXIodGVhY2hlcklkOiBzdHJpbmcsIHVwZGF0ZWRUZWFjaGVyOiBQYXJ0aWFsPFRlYWNoZXI+KTogdm9pZCB7XG4gICAgY29uc3QgdGVhY2hlcnMgPSB0aGlzLmdldFRlYWNoZXJzKCk7XG4gICAgY29uc3QgaW5kZXggPSB0ZWFjaGVycy5maW5kSW5kZXgodCA9PiB0LmlkID09PSB0ZWFjaGVySWQpO1xuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHRlYWNoZXJzW2luZGV4XSA9IHsgLi4udGVhY2hlcnNbaW5kZXhdLCAuLi51cGRhdGVkVGVhY2hlciwgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpIH07XG4gICAgICB0aGlzLnNhdmVUZWFjaGVycyh0ZWFjaGVycyk7XG4gICAgfVxuICB9XG5cbiAgZGVsZXRlVGVhY2hlcih0ZWFjaGVySWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHRlYWNoZXJzID0gdGhpcy5nZXRUZWFjaGVycygpO1xuICAgIGNvbnN0IGZpbHRlcmVkVGVhY2hlcnMgPSB0ZWFjaGVycy5maWx0ZXIodCA9PiB0LmlkICE9PSB0ZWFjaGVySWQpO1xuICAgIHRoaXMuc2F2ZVRlYWNoZXJzKGZpbHRlcmVkVGVhY2hlcnMpO1xuICB9XG5cbiAgZ2V0VGVhY2hlckJ5SWQodGVhY2hlcklkOiBzdHJpbmcpOiBUZWFjaGVyIHwgbnVsbCB7XG4gICAgY29uc3QgdGVhY2hlcnMgPSB0aGlzLmdldFRlYWNoZXJzKCk7XG4gICAgcmV0dXJuIHRlYWNoZXJzLmZpbmQodCA9PiB0LmlkID09PSB0ZWFjaGVySWQpIHx8IG51bGw7XG4gIH1cblxuICAvLyA9PT0g2KXYr9in2LHYqSDYp9mE2YXZiNi42YHZitmGID09PVxuICBnZXRFbXBsb3llZXMoKTogRW1wbG95ZWVbXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0SXRlbTxFbXBsb3llZVtdPihTVE9SQUdFX0tFWVMuRU1QTE9ZRUVTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVFbXBsb3llZXMoZW1wbG95ZWVzOiBFbXBsb3llZVtdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5FTVBMT1lFRVMsIGVtcGxveWVlcyk7XG4gIH1cblxuICBhZGRFbXBsb3llZShlbXBsb3llZTogRW1wbG95ZWUpOiB2b2lkIHtcbiAgICBjb25zdCBlbXBsb3llZXMgPSB0aGlzLmdldEVtcGxveWVlcygpO1xuICAgIGVtcGxveWVlcy5wdXNoKGVtcGxveWVlKTtcbiAgICB0aGlzLnNhdmVFbXBsb3llZXMoZW1wbG95ZWVzKTtcbiAgfVxuXG4gIHVwZGF0ZUVtcGxveWVlKGVtcGxveWVlSWQ6IHN0cmluZywgdXBkYXRlZEVtcGxveWVlOiBQYXJ0aWFsPEVtcGxveWVlPik6IHZvaWQge1xuICAgIGNvbnN0IGVtcGxveWVlcyA9IHRoaXMuZ2V0RW1wbG95ZWVzKCk7XG4gICAgY29uc3QgaW5kZXggPSBlbXBsb3llZXMuZmluZEluZGV4KGUgPT4gZS5pZCA9PT0gZW1wbG95ZWVJZCk7XG4gICAgaWYgKGluZGV4ICE9PSAtMSkge1xuICAgICAgZW1wbG95ZWVzW2luZGV4XSA9IHsgLi4uZW1wbG95ZWVzW2luZGV4XSwgLi4udXBkYXRlZEVtcGxveWVlLCB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkgfTtcbiAgICAgIHRoaXMuc2F2ZUVtcGxveWVlcyhlbXBsb3llZXMpO1xuICAgIH1cbiAgfVxuXG4gIGRlbGV0ZUVtcGxveWVlKGVtcGxveWVlSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IGVtcGxveWVlcyA9IHRoaXMuZ2V0RW1wbG95ZWVzKCk7XG4gICAgY29uc3QgZmlsdGVyZWRFbXBsb3llZXMgPSBlbXBsb3llZXMuZmlsdGVyKGUgPT4gZS5pZCAhPT0gZW1wbG95ZWVJZCk7XG4gICAgdGhpcy5zYXZlRW1wbG95ZWVzKGZpbHRlcmVkRW1wbG95ZWVzKTtcbiAgfVxuXG4gIGdldEVtcGxveWVlQnlJZChlbXBsb3llZUlkOiBzdHJpbmcpOiBFbXBsb3llZSB8IG51bGwge1xuICAgIGNvbnN0IGVtcGxveWVlcyA9IHRoaXMuZ2V0RW1wbG95ZWVzKCk7XG4gICAgcmV0dXJuIGVtcGxveWVlcy5maW5kKGUgPT4gZS5pZCA9PT0gZW1wbG95ZWVJZCkgfHwgbnVsbDtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYtdmB2YjZgSA9PT1cbiAgZ2V0Q2xhc3NlcygpOiBDbGFzc1tdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPENsYXNzW10+KFNUT1JBR0VfS0VZUy5DTEFTU0VTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVDbGFzc2VzKGNsYXNzZXM6IENsYXNzW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkNMQVNTRVMsIGNsYXNzZXMpO1xuICB9XG5cbiAgYWRkQ2xhc3MoY2xhc3NEYXRhOiBDbGFzcyk6IHZvaWQge1xuICAgIGNvbnN0IGNsYXNzZXMgPSB0aGlzLmdldENsYXNzZXMoKTtcbiAgICBjbGFzc2VzLnB1c2goY2xhc3NEYXRhKTtcbiAgICB0aGlzLnNhdmVDbGFzc2VzKGNsYXNzZXMpO1xuICB9XG5cbiAgdXBkYXRlQ2xhc3MoY2xhc3NJZDogc3RyaW5nLCB1cGRhdGVkQ2xhc3M6IFBhcnRpYWw8Q2xhc3M+KTogdm9pZCB7XG4gICAgY29uc3QgY2xhc3NlcyA9IHRoaXMuZ2V0Q2xhc3NlcygpO1xuICAgIGNvbnN0IGluZGV4ID0gY2xhc3Nlcy5maW5kSW5kZXgoYyA9PiBjLmlkID09PSBjbGFzc0lkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBjbGFzc2VzW2luZGV4XSA9IHsgLi4uY2xhc3Nlc1tpbmRleF0sIC4uLnVwZGF0ZWRDbGFzcywgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpIH07XG4gICAgICB0aGlzLnNhdmVDbGFzc2VzKGNsYXNzZXMpO1xuICAgIH1cbiAgfVxuXG4gIGRlbGV0ZUNsYXNzKGNsYXNzSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IGNsYXNzZXMgPSB0aGlzLmdldENsYXNzZXMoKTtcbiAgICBjb25zdCBmaWx0ZXJlZENsYXNzZXMgPSBjbGFzc2VzLmZpbHRlcihjID0+IGMuaWQgIT09IGNsYXNzSWQpO1xuICAgIHRoaXMuc2F2ZUNsYXNzZXMoZmlsdGVyZWRDbGFzc2VzKTtcbiAgfVxuXG4gIGdldENsYXNzQnlJZChjbGFzc0lkOiBzdHJpbmcpOiBDbGFzcyB8IG51bGwge1xuICAgIGNvbnN0IGNsYXNzZXMgPSB0aGlzLmdldENsYXNzZXMoKTtcbiAgICByZXR1cm4gY2xhc3Nlcy5maW5kKGMgPT4gYy5pZCA9PT0gY2xhc3NJZCkgfHwgbnVsbDtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTZhdmI2KfYryA9PT1cbiAgZ2V0U3ViamVjdHMoKTogU3ViamVjdFtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPFN1YmplY3RbXT4oU1RPUkFHRV9LRVlTLlNVQkpFQ1RTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVTdWJqZWN0cyhzdWJqZWN0czogU3ViamVjdFtdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5TVUJKRUNUUywgc3ViamVjdHMpO1xuICB9XG5cbiAgYWRkU3ViamVjdChzdWJqZWN0OiBTdWJqZWN0KTogdm9pZCB7XG4gICAgY29uc3Qgc3ViamVjdHMgPSB0aGlzLmdldFN1YmplY3RzKCk7XG4gICAgc3ViamVjdHMucHVzaChzdWJqZWN0KTtcbiAgICB0aGlzLnNhdmVTdWJqZWN0cyhzdWJqZWN0cyk7XG4gIH1cblxuICB1cGRhdGVTdWJqZWN0KHN1YmplY3RJZDogc3RyaW5nLCB1cGRhdGVkU3ViamVjdDogUGFydGlhbDxTdWJqZWN0Pik6IHZvaWQge1xuICAgIGNvbnN0IHN1YmplY3RzID0gdGhpcy5nZXRTdWJqZWN0cygpO1xuICAgIGNvbnN0IGluZGV4ID0gc3ViamVjdHMuZmluZEluZGV4KHMgPT4gcy5pZCA9PT0gc3ViamVjdElkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBzdWJqZWN0c1tpbmRleF0gPSB7IC4uLnN1YmplY3RzW2luZGV4XSwgLi4udXBkYXRlZFN1YmplY3QsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9O1xuICAgICAgdGhpcy5zYXZlU3ViamVjdHMoc3ViamVjdHMpO1xuICAgIH1cbiAgfVxuXG4gIGRlbGV0ZVN1YmplY3Qoc3ViamVjdElkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBzdWJqZWN0cyA9IHRoaXMuZ2V0U3ViamVjdHMoKTtcbiAgICBjb25zdCBmaWx0ZXJlZFN1YmplY3RzID0gc3ViamVjdHMuZmlsdGVyKHMgPT4gcy5pZCAhPT0gc3ViamVjdElkKTtcbiAgICB0aGlzLnNhdmVTdWJqZWN0cyhmaWx0ZXJlZFN1YmplY3RzKTtcbiAgfVxuXG4gIGdldFN1YmplY3RCeUlkKHN1YmplY3RJZDogc3RyaW5nKTogU3ViamVjdCB8IG51bGwge1xuICAgIGNvbnN0IHN1YmplY3RzID0gdGhpcy5nZXRTdWJqZWN0cygpO1xuICAgIHJldHVybiBzdWJqZWN0cy5maW5kKHMgPT4gcy5pZCA9PT0gc3ViamVjdElkKSB8fCBudWxsO1xuICB9XG5cbiAgLy8gPT09INil2K/Yp9ix2Kkg2KfZhNiv2LHYrNin2KogPT09XG4gIGdldEdyYWRlcygpOiBHcmFkZVtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPEdyYWRlW10+KFNUT1JBR0VfS0VZUy5HUkFERVMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZUdyYWRlcyhncmFkZXM6IEdyYWRlW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkdSQURFUywgZ3JhZGVzKTtcbiAgfVxuXG4gIGFkZEdyYWRlKGdyYWRlOiBHcmFkZSk6IHZvaWQge1xuICAgIGNvbnN0IGdyYWRlcyA9IHRoaXMuZ2V0R3JhZGVzKCk7XG4gICAgZ3JhZGVzLnB1c2goZ3JhZGUpO1xuICAgIHRoaXMuc2F2ZUdyYWRlcyhncmFkZXMpO1xuICB9XG5cbiAgdXBkYXRlR3JhZGUoZ3JhZGVJZDogc3RyaW5nLCB1cGRhdGVkR3JhZGU6IFBhcnRpYWw8R3JhZGU+KTogdm9pZCB7XG4gICAgY29uc3QgZ3JhZGVzID0gdGhpcy5nZXRHcmFkZXMoKTtcbiAgICBjb25zdCBpbmRleCA9IGdyYWRlcy5maW5kSW5kZXgoZyA9PiBnLmlkID09PSBncmFkZUlkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBncmFkZXNbaW5kZXhdID0geyAuLi5ncmFkZXNbaW5kZXhdLCAuLi51cGRhdGVkR3JhZGUsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9O1xuICAgICAgdGhpcy5zYXZlR3JhZGVzKGdyYWRlcyk7XG4gICAgfVxuICB9XG5cbiAgZGVsZXRlR3JhZGUoZ3JhZGVJZDogc3RyaW5nKTogdm9pZCB7XG4gICAgY29uc3QgZ3JhZGVzID0gdGhpcy5nZXRHcmFkZXMoKTtcbiAgICBjb25zdCBmaWx0ZXJlZEdyYWRlcyA9IGdyYWRlcy5maWx0ZXIoZyA9PiBnLmlkICE9PSBncmFkZUlkKTtcbiAgICB0aGlzLnNhdmVHcmFkZXMoZmlsdGVyZWRHcmFkZXMpO1xuICB9XG5cbiAgZ2V0R3JhZGVzQnlTdHVkZW50KHN0dWRlbnRJZDogc3RyaW5nKTogR3JhZGVbXSB7XG4gICAgY29uc3QgZ3JhZGVzID0gdGhpcy5nZXRHcmFkZXMoKTtcbiAgICByZXR1cm4gZ3JhZGVzLmZpbHRlcihnID0+IGcuc3R1ZGVudElkID09PSBzdHVkZW50SWQpO1xuICB9XG5cbiAgZ2V0R3JhZGVzQnlDbGFzcyhjbGFzc0lkOiBzdHJpbmcpOiBHcmFkZVtdIHtcbiAgICBjb25zdCBncmFkZXMgPSB0aGlzLmdldEdyYWRlcygpO1xuICAgIHJldHVybiBncmFkZXMuZmlsdGVyKGcgPT4gZy5jbGFzc0lkID09PSBjbGFzc0lkKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYrdi22YjYsSA9PT1cbiAgZ2V0QXR0ZW5kYW5jZSgpOiBBdHRlbmRhbmNlW10ge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08QXR0ZW5kYW5jZVtdPihTVE9SQUdFX0tFWVMuQVRURU5EQU5DRSkgfHwgW107XG4gIH1cblxuICBzYXZlQXR0ZW5kYW5jZShhdHRlbmRhbmNlOiBBdHRlbmRhbmNlW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkFUVEVOREFOQ0UsIGF0dGVuZGFuY2UpO1xuICB9XG5cbiAgYWRkQXR0ZW5kYW5jZShhdHRlbmRhbmNlOiBBdHRlbmRhbmNlKTogdm9pZCB7XG4gICAgY29uc3QgYXR0ZW5kYW5jZVJlY29yZHMgPSB0aGlzLmdldEF0dGVuZGFuY2UoKTtcbiAgICBhdHRlbmRhbmNlUmVjb3Jkcy5wdXNoKGF0dGVuZGFuY2UpO1xuICAgIHRoaXMuc2F2ZUF0dGVuZGFuY2UoYXR0ZW5kYW5jZVJlY29yZHMpO1xuICB9XG5cbiAgdXBkYXRlQXR0ZW5kYW5jZShhdHRlbmRhbmNlSWQ6IHN0cmluZywgdXBkYXRlZEF0dGVuZGFuY2U6IFBhcnRpYWw8QXR0ZW5kYW5jZT4pOiB2b2lkIHtcbiAgICBjb25zdCBhdHRlbmRhbmNlUmVjb3JkcyA9IHRoaXMuZ2V0QXR0ZW5kYW5jZSgpO1xuICAgIGNvbnN0IGluZGV4ID0gYXR0ZW5kYW5jZVJlY29yZHMuZmluZEluZGV4KGEgPT4gYS5pZCA9PT0gYXR0ZW5kYW5jZUlkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBhdHRlbmRhbmNlUmVjb3Jkc1tpbmRleF0gPSB7IC4uLmF0dGVuZGFuY2VSZWNvcmRzW2luZGV4XSwgLi4udXBkYXRlZEF0dGVuZGFuY2UsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9O1xuICAgICAgdGhpcy5zYXZlQXR0ZW5kYW5jZShhdHRlbmRhbmNlUmVjb3Jkcyk7XG4gICAgfVxuICB9XG5cbiAgZ2V0QXR0ZW5kYW5jZUJ5U3R1ZGVudChzdHVkZW50SWQ6IHN0cmluZyk6IEF0dGVuZGFuY2VbXSB7XG4gICAgY29uc3QgYXR0ZW5kYW5jZSA9IHRoaXMuZ2V0QXR0ZW5kYW5jZSgpO1xuICAgIHJldHVybiBhdHRlbmRhbmNlLmZpbHRlcihhID0+IGEuc3R1ZGVudElkID09PSBzdHVkZW50SWQpO1xuICB9XG5cbiAgZ2V0QXR0ZW5kYW5jZUJ5RGF0ZShkYXRlOiBEYXRlKTogQXR0ZW5kYW5jZVtdIHtcbiAgICBjb25zdCBhdHRlbmRhbmNlID0gdGhpcy5nZXRBdHRlbmRhbmNlKCk7XG4gICAgY29uc3QgZGF0ZVN0cmluZyA9IGRhdGUudG9EYXRlU3RyaW5nKCk7XG4gICAgcmV0dXJuIGF0dGVuZGFuY2UuZmlsdGVyKGEgPT4gbmV3IERhdGUoYS5kYXRlKS50b0RhdGVTdHJpbmcoKSA9PT0gZGF0ZVN0cmluZyk7XG4gIH1cblxuICAvLyA9PT0g2KXYr9in2LHYqSDYp9mE2YXYs9iq2K7Yr9mF2YrZhiA9PT1cbiAgZ2V0VXNlcnMoKTogVXNlcltdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPFVzZXJbXT4oU1RPUkFHRV9LRVlTLlVTRVJTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVVc2Vycyh1c2VyczogVXNlcltdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5VU0VSUywgdXNlcnMpO1xuICB9XG5cbiAgZ2V0Q3VycmVudFVzZXIoKTogVXNlciB8IG51bGwge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08VXNlcj4oU1RPUkFHRV9LRVlTLkNVUlJFTlRfVVNFUik7XG4gIH1cblxuICBzZXRDdXJyZW50VXNlcih1c2VyOiBVc2VyKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5DVVJSRU5UX1VTRVIsIHVzZXIpO1xuICB9XG5cbiAgbG9nb3V0KCk6IHZvaWQge1xuICAgIHRoaXMucmVtb3ZlSXRlbShTVE9SQUdFX0tFWVMuQ1VSUkVOVF9VU0VSKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYpdi52K/Yp9iv2KfYqiA9PT1cbiAgZ2V0U2V0dGluZ3MoKTogU2V0dGluZ3MgfCBudWxsIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPFNldHRpbmdzPihTVE9SQUdFX0tFWVMuU0VUVElOR1MpO1xuICB9XG5cbiAgc2F2ZVNldHRpbmdzKHNldHRpbmdzOiBTZXR0aW5ncyk6IHZvaWQge1xuICAgIHRoaXMuc2V0SXRlbShTVE9SQUdFX0tFWVMuU0VUVElOR1MsIHNldHRpbmdzKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYo9it2K/Yp9irID09PVxuICBnZXRFdmVudHMoKTogRXZlbnRbXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0SXRlbTxFdmVudFtdPihTVE9SQUdFX0tFWVMuRVZFTlRTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVFdmVudHMoZXZlbnRzOiBFdmVudFtdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5FVkVOVFMsIGV2ZW50cyk7XG4gIH1cblxuICBhZGRFdmVudChldmVudDogRXZlbnQpOiB2b2lkIHtcbiAgICBjb25zdCBldmVudHMgPSB0aGlzLmdldEV2ZW50cygpO1xuICAgIGV2ZW50cy5wdXNoKGV2ZW50KTtcbiAgICB0aGlzLnNhdmVFdmVudHMoZXZlbnRzKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYpdi02LnYp9ix2KfYqiA9PT1cbiAgZ2V0Tm90aWZpY2F0aW9ucygpOiBOb3RpZmljYXRpb25bXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0SXRlbTxOb3RpZmljYXRpb25bXT4oU1RPUkFHRV9LRVlTLk5PVElGSUNBVElPTlMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9uczogTm90aWZpY2F0aW9uW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLk5PVElGSUNBVElPTlMsIG5vdGlmaWNhdGlvbnMpO1xuICB9XG5cbiAgYWRkTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKTogdm9pZCB7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9ucyA9IHRoaXMuZ2V0Tm90aWZpY2F0aW9ucygpO1xuICAgIG5vdGlmaWNhdGlvbnMucHVzaChub3RpZmljYXRpb24pO1xuICAgIHRoaXMuc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucyk7XG4gIH1cblxuICBtYXJrTm90aWZpY2F0aW9uQXNSZWFkKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBub3RpZmljYXRpb25zID0gdGhpcy5nZXROb3RpZmljYXRpb25zKCk7XG4gICAgY29uc3QgaW5kZXggPSBub3RpZmljYXRpb25zLmZpbmRJbmRleChuID0+IG4uaWQgPT09IG5vdGlmaWNhdGlvbklkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBub3RpZmljYXRpb25zW2luZGV4XS5pc1JlYWQgPSB0cnVlO1xuICAgICAgbm90aWZpY2F0aW9uc1tpbmRleF0ucmVhZEF0ID0gbmV3IERhdGUoKTtcbiAgICAgIHRoaXMuc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucyk7XG4gICAgfVxuICB9XG59XG5cbi8vINil2YbYtNin2KEg2YXYq9mK2YQg2YjYp9it2K8g2YTZhNin2LPYqtiu2K/Yp9mFINmB2Yog2KfZhNiq2LfYqNmK2YJcbmV4cG9ydCBjb25zdCBsb2NhbFN0b3JhZ2VNYW5hZ2VyID0gbmV3IExvY2FsU3RvcmFnZU1hbmFnZXIoKTtcblxuLy8g2K/ZiNin2YQg2YXYs9in2LnYr9ipINmE2YTYqNit2Ksg2YjYp9mE2KrYtdmB2YrYqVxuZXhwb3J0IGNvbnN0IHNlYXJjaFV0aWxzID0ge1xuICBzZWFyY2hTdHVkZW50czogKHF1ZXJ5OiBzdHJpbmcsIHN0dWRlbnRzOiBTdHVkZW50W10pOiBTdHVkZW50W10gPT4ge1xuICAgIGNvbnN0IGxvd2VyY2FzZVF1ZXJ5ID0gcXVlcnkudG9Mb3dlckNhc2UoKTtcbiAgICByZXR1cm4gc3R1ZGVudHMuZmlsdGVyKHN0dWRlbnQgPT4gXG4gICAgICBzdHVkZW50Lm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlcmNhc2VRdWVyeSkgfHxcbiAgICAgIHN0dWRlbnQuc3R1ZGVudElkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICBzdHVkZW50LmVtYWlsPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KVxuICAgICk7XG4gIH0sXG5cbiAgc2VhcmNoVGVhY2hlcnM6IChxdWVyeTogc3RyaW5nLCB0ZWFjaGVyczogVGVhY2hlcltdKTogVGVhY2hlcltdID0+IHtcbiAgICBjb25zdCBsb3dlcmNhc2VRdWVyeSA9IHF1ZXJ5LnRvTG93ZXJDYXNlKCk7XG4gICAgcmV0dXJuIHRlYWNoZXJzLmZpbHRlcih0ZWFjaGVyID0+XG4gICAgICB0ZWFjaGVyLm5hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICB0ZWFjaGVyLmZ1bGxOYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgdGVhY2hlci50ZWFjaGVySWQ/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICB0ZWFjaGVyLnNlcmlhbE51bWJlcj8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlcmNhc2VRdWVyeSkgfHxcbiAgICAgIHRlYWNoZXIuZW1haWw/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICB0ZWFjaGVyLnNwZWNpYWxpemF0aW9uPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KVxuICAgICk7XG4gIH0sXG5cbiAgc2VhcmNoRW1wbG95ZWVzOiAocXVlcnk6IHN0cmluZywgZW1wbG95ZWVzOiBFbXBsb3llZVtdKTogRW1wbG95ZWVbXSA9PiB7XG4gICAgY29uc3QgbG93ZXJjYXNlUXVlcnkgPSBxdWVyeS50b0xvd2VyQ2FzZSgpO1xuICAgIHJldHVybiBlbXBsb3llZXMuZmlsdGVyKGVtcGxveWVlID0+XG4gICAgICBlbXBsb3llZS5uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUuZnVsbE5hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICBlbXBsb3llZS5lbXBsb3llZUlkPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUuc2VyaWFsTnVtYmVyPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUuZW1haWw/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICBlbXBsb3llZS5kZXBhcnRtZW50Py50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUucG9zaXRpb24/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpXG4gICAgKTtcbiAgfSxcblxuICBmaWx0ZXJTdHVkZW50c0J5Q2xhc3M6IChjbGFzc0lkOiBzdHJpbmcsIHN0dWRlbnRzOiBTdHVkZW50W10pOiBTdHVkZW50W10gPT4ge1xuICAgIHJldHVybiBzdHVkZW50cy5maWx0ZXIoc3R1ZGVudCA9PiBzdHVkZW50LmNsYXNzSWQgPT09IGNsYXNzSWQpO1xuICB9LFxuXG4gIGZpbHRlckdyYWRlc0J5RGF0ZVJhbmdlOiAoc3RhcnREYXRlOiBEYXRlLCBlbmREYXRlOiBEYXRlLCBncmFkZXM6IEdyYWRlW10pOiBHcmFkZVtdID0+IHtcbiAgICByZXR1cm4gZ3JhZGVzLmZpbHRlcihncmFkZSA9PiB7XG4gICAgICBjb25zdCBncmFkZURhdGUgPSBuZXcgRGF0ZShncmFkZS5leGFtRGF0ZSk7XG4gICAgICByZXR1cm4gZ3JhZGVEYXRlID49IHN0YXJ0RGF0ZSAmJiBncmFkZURhdGUgPD0gZW5kRGF0ZTtcbiAgICB9KTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6WyJTVE9SQUdFX0tFWVMiLCJTVFVERU5UUyIsIlRFQUNIRVJTIiwiRU1QTE9ZRUVTIiwiQ0xBU1NFUyIsIlNVQkpFQ1RTIiwiR1JBREVTIiwiQVRURU5EQU5DRSIsIlVTRVJTIiwiU0VUVElOR1MiLCJFVkVOVFMiLCJOT1RJRklDQVRJT05TIiwiQ1VSUkVOVF9VU0VSIiwiQUNBREVNSUNfWUVBUiIsIkNVUlJFTlRfU0VNRVNURVIiLCJMb2NhbFN0b3JhZ2VNYW5hZ2VyIiwic2V0SXRlbSIsImtleSIsImRhdGEiLCJzZXJpYWxpemVkRGF0YSIsIkpTT04iLCJzdHJpbmdpZnkiLCJsb2NhbFN0b3JhZ2UiLCJlcnJvciIsImNvbnNvbGUiLCJnZXRJdGVtIiwicGFyc2UiLCJyZW1vdmVJdGVtIiwiY2xlYXJBbGwiLCJPYmplY3QiLCJ2YWx1ZXMiLCJmb3JFYWNoIiwiZ2V0U3R1ZGVudHMiLCJzYXZlU3R1ZGVudHMiLCJzdHVkZW50cyIsImFkZFN0dWRlbnQiLCJzdHVkZW50IiwicHVzaCIsInVwZGF0ZVN0dWRlbnQiLCJzdHVkZW50SWQiLCJ1cGRhdGVkU3R1ZGVudCIsImluZGV4IiwiZmluZEluZGV4IiwicyIsImlkIiwidXBkYXRlZEF0IiwiRGF0ZSIsImRlbGV0ZVN0dWRlbnQiLCJmaWx0ZXJlZFN0dWRlbnRzIiwiZmlsdGVyIiwiZ2V0U3R1ZGVudEJ5SWQiLCJmaW5kIiwiZ2V0VGVhY2hlcnMiLCJzYXZlVGVhY2hlcnMiLCJ0ZWFjaGVycyIsImFkZFRlYWNoZXIiLCJ0ZWFjaGVyIiwidXBkYXRlVGVhY2hlciIsInRlYWNoZXJJZCIsInVwZGF0ZWRUZWFjaGVyIiwidCIsImRlbGV0ZVRlYWNoZXIiLCJmaWx0ZXJlZFRlYWNoZXJzIiwiZ2V0VGVhY2hlckJ5SWQiLCJnZXRFbXBsb3llZXMiLCJzYXZlRW1wbG95ZWVzIiwiZW1wbG95ZWVzIiwiYWRkRW1wbG95ZWUiLCJlbXBsb3llZSIsInVwZGF0ZUVtcGxveWVlIiwiZW1wbG95ZWVJZCIsInVwZGF0ZWRFbXBsb3llZSIsImUiLCJkZWxldGVFbXBsb3llZSIsImZpbHRlcmVkRW1wbG95ZWVzIiwiZ2V0RW1wbG95ZWVCeUlkIiwiZ2V0Q2xhc3NlcyIsInNhdmVDbGFzc2VzIiwiY2xhc3NlcyIsImFkZENsYXNzIiwiY2xhc3NEYXRhIiwidXBkYXRlQ2xhc3MiLCJjbGFzc0lkIiwidXBkYXRlZENsYXNzIiwiYyIsImRlbGV0ZUNsYXNzIiwiZmlsdGVyZWRDbGFzc2VzIiwiZ2V0Q2xhc3NCeUlkIiwiZ2V0U3ViamVjdHMiLCJzYXZlU3ViamVjdHMiLCJzdWJqZWN0cyIsImFkZFN1YmplY3QiLCJzdWJqZWN0IiwidXBkYXRlU3ViamVjdCIsInN1YmplY3RJZCIsInVwZGF0ZWRTdWJqZWN0IiwiZGVsZXRlU3ViamVjdCIsImZpbHRlcmVkU3ViamVjdHMiLCJnZXRTdWJqZWN0QnlJZCIsImdldEdyYWRlcyIsInNhdmVHcmFkZXMiLCJncmFkZXMiLCJhZGRHcmFkZSIsImdyYWRlIiwidXBkYXRlR3JhZGUiLCJncmFkZUlkIiwidXBkYXRlZEdyYWRlIiwiZyIsImRlbGV0ZUdyYWRlIiwiZmlsdGVyZWRHcmFkZXMiLCJnZXRHcmFkZXNCeVN0dWRlbnQiLCJnZXRHcmFkZXNCeUNsYXNzIiwiZ2V0QXR0ZW5kYW5jZSIsInNhdmVBdHRlbmRhbmNlIiwiYXR0ZW5kYW5jZSIsImFkZEF0dGVuZGFuY2UiLCJhdHRlbmRhbmNlUmVjb3JkcyIsInVwZGF0ZUF0dGVuZGFuY2UiLCJhdHRlbmRhbmNlSWQiLCJ1cGRhdGVkQXR0ZW5kYW5jZSIsImEiLCJnZXRBdHRlbmRhbmNlQnlTdHVkZW50IiwiZ2V0QXR0ZW5kYW5jZUJ5RGF0ZSIsImRhdGUiLCJkYXRlU3RyaW5nIiwidG9EYXRlU3RyaW5nIiwiZ2V0VXNlcnMiLCJzYXZlVXNlcnMiLCJ1c2VycyIsImdldEN1cnJlbnRVc2VyIiwic2V0Q3VycmVudFVzZXIiLCJ1c2VyIiwibG9nb3V0IiwiZ2V0U2V0dGluZ3MiLCJzYXZlU2V0dGluZ3MiLCJzZXR0aW5ncyIsImdldEV2ZW50cyIsInNhdmVFdmVudHMiLCJldmVudHMiLCJhZGRFdmVudCIsImV2ZW50IiwiZ2V0Tm90aWZpY2F0aW9ucyIsInNhdmVOb3RpZmljYXRpb25zIiwibm90aWZpY2F0aW9ucyIsImFkZE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbiIsIm1hcmtOb3RpZmljYXRpb25Bc1JlYWQiLCJub3RpZmljYXRpb25JZCIsIm4iLCJpc1JlYWQiLCJyZWFkQXQiLCJsb2NhbFN0b3JhZ2VNYW5hZ2VyIiwic2VhcmNoVXRpbHMiLCJzZWFyY2hTdHVkZW50cyIsInF1ZXJ5IiwibG93ZXJjYXNlUXVlcnkiLCJ0b0xvd2VyQ2FzZSIsIm5hbWUiLCJpbmNsdWRlcyIsImVtYWlsIiwic2VhcmNoVGVhY2hlcnMiLCJmdWxsTmFtZSIsInNlcmlhbE51bWJlciIsInNwZWNpYWxpemF0aW9uIiwic2VhcmNoRW1wbG95ZWVzIiwiZGVwYXJ0bWVudCIsInBvc2l0aW9uIiwiZmlsdGVyU3R1ZGVudHNCeUNsYXNzIiwiZmlsdGVyR3JhZGVzQnlEYXRlUmFuZ2UiLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZ3JhZGVEYXRlIiwiZXhhbURhdGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/localStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/sampleData.ts":
/*!*********************************!*\
  !*** ./src/utils/sampleData.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeSampleData: () => (/* binding */ initializeSampleData)\n/* harmony export */ });\n/* harmony import */ var _localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n\n// إنشاء بيانات تجريبية للنظام\nconst initializeSampleData = ()=>{\n    // التحقق من وجود بيانات مسبقة\n    const existingUsers = _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.getUsers();\n    if (existingUsers.length > 0) {\n        return; // البيانات موجودة بالفعل\n    }\n    // إنشاء المستخدمين\n    const users = [\n        {\n            id: 'user-1',\n            name: 'عبيدة العيثاوي',\n            username: 'obeida',\n            email: '<EMAIL>',\n            password: '12345',\n            role: 'developer',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'user-2',\n            name: 'admin',\n            username: 'admin',\n            email: '<EMAIL>',\n            password: 'admin',\n            role: 'admin',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المواد الدراسية\n    const subjects = [\n        {\n            id: 'subject-1',\n            name: 'الرياضيات',\n            code: 'MATH101',\n            description: 'مادة الرياضيات للصف الأول',\n            grade: 1,\n            credits: 4,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-2',\n            name: 'اللغة العربية',\n            code: 'ARAB101',\n            description: 'مادة اللغة العربية للصف الأول',\n            grade: 1,\n            credits: 5,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-3',\n            name: 'العلوم',\n            code: 'SCI101',\n            description: 'مادة العلوم للصف الأول',\n            grade: 1,\n            credits: 3,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-4',\n            name: 'التاريخ',\n            code: 'HIST101',\n            description: 'مادة التاريخ للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-5',\n            name: 'الجغرافيا',\n            code: 'GEO101',\n            description: 'مادة الجغرافيا للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المعلمين (بيانات تجريبية)\n    const teachers = [\n        {\n            id: 'teacher-1',\n            serialNumber: '001',\n            fullName: 'فاطمة أحمد محمد علي',\n            shortName: 'زينب حسن محمد',\n            title: 'أستاذة',\n            specialization: 'الرياضيات',\n            firstAppointmentDate: new Date('2018-09-01'),\n            schoolStartDate: new Date('2020-09-01'),\n            dateOfBirth: new Date('1985-05-15'),\n            address: 'بغداد - الكرادة - شارع الجامعة',\n            phone: '07901234567',\n            employmentType: 'permanent',\n            status: 'active',\n            subjects: [\n                'subject-1'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '001',\n            name: 'فاطمة أحمد محمد',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس رياضيات',\n            experience: 8,\n            salary: 800000,\n            hireDate: new Date('2020-09-01')\n        },\n        {\n            id: 'teacher-2',\n            serialNumber: '002',\n            fullName: 'محمد علي حسن الأستاذ',\n            shortName: 'فاطمة أحمد علي',\n            title: 'أستاذ',\n            specialization: 'اللغة العربية',\n            firstAppointmentDate: new Date('2016-09-01'),\n            schoolStartDate: new Date('2018-09-01'),\n            dateOfBirth: new Date('1980-03-20'),\n            address: 'بغداد - الجادرية - المنطقة الثانية',\n            phone: '07801234567',\n            employmentType: 'assignment',\n            status: 'active',\n            subjects: [\n                'subject-2'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '002',\n            name: 'محمد علي حسن الأستاذ',\n            email: '<EMAIL>',\n            gender: 'male',\n            qualification: 'ماجستير لغة عربية',\n            experience: 12,\n            salary: 900000,\n            hireDate: new Date('2018-09-01')\n        },\n        {\n            id: 'teacher-3',\n            serialNumber: '003',\n            fullName: 'سارة خالد أحمد المدرسة',\n            shortName: 'مريم حسين محمد',\n            title: 'مدرسة',\n            specialization: 'العلوم',\n            firstAppointmentDate: new Date('2021-09-01'),\n            schoolStartDate: new Date('2021-09-01'),\n            dateOfBirth: new Date('1990-08-12'),\n            address: 'بغداد - الكاظمية - حي الأطباء',\n            phone: '07701234567',\n            employmentType: 'contract',\n            status: 'active',\n            subjects: [\n                'subject-3'\n            ],\n            classes: [\n                'class-2'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '003',\n            name: 'سارة خالد أحمد المدرسة',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس علوم',\n            experience: 3,\n            salary: 700000,\n            hireDate: new Date('2021-09-01')\n        }\n    ];\n    // إنشاء الصفوف\n    const classes = [\n        {\n            id: 'class-1',\n            name: 'الصف الأول أ',\n            grade: 1,\n            section: 'أ',\n            capacity: 30,\n            currentStudents: 20,\n            classTeacherId: 'teacher-1',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'class-2',\n            name: 'الصف الأول ب',\n            grade: 1,\n            section: 'ب',\n            capacity: 30,\n            currentStudents: 18,\n            classTeacherId: 'teacher-2',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الطلاب (بيانات تجريبية)\n    const students = [\n        {\n            id: 'student-1',\n            studentId: 'S001',\n            name: 'علي أحمد محمد',\n            email: '<EMAIL>',\n            phone: '07701234567',\n            dateOfBirth: new Date('2012-01-15'),\n            gender: 'male',\n            address: 'بغداد - الكرادة - شارع الرشيد',\n            parentName: 'أحمد محمد علي',\n            parentPhone: '07901234567',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالب متفوق',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-2',\n            studentId: 'S002',\n            name: 'فاطمة حسن علي',\n            email: '<EMAIL>',\n            phone: '07701234568',\n            dateOfBirth: new Date('2012-03-20'),\n            gender: 'female',\n            address: 'بغداد - الجادرية - شارع الجامعة',\n            parentName: 'حسن علي محمد',\n            parentPhone: '07901234568',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'حساسية من الفول السوداني',\n            notes: 'طالبة نشيطة',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-3',\n            studentId: 'S003',\n            name: 'زينب محمد حسن',\n            email: '<EMAIL>',\n            phone: '07701234570',\n            dateOfBirth: new Date('2012-07-25'),\n            gender: 'female',\n            address: 'بغداد - الكاظمية - شارع الإمام',\n            parentName: 'محمد حسن علي',\n            parentPhone: '07901234570',\n            parentEmail: '<EMAIL>',\n            classId: 'class-2',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالبة متميزة في اللغة العربية',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الإعدادات\n    const settings = {\n        id: 'settings-1',\n        schoolName: 'مدرسة النور الابتدائية',\n        address: 'بغداد - الكرادة - شارع الرشيد',\n        phone: '07901234567',\n        email: '<EMAIL>',\n        website: 'www.alnoor-school.edu.iq',\n        academicYear: '2024-2025',\n        currentSemester: 'first',\n        gradeSystem: 'percentage',\n        attendanceRequired: true,\n        maxAbsences: 10,\n        workingDays: [\n            'sunday',\n            'monday',\n            'tuesday',\n            'wednesday',\n            'thursday'\n        ],\n        schoolStartTime: '08:00',\n        schoolEndTime: '14:00',\n        updatedAt: new Date()\n    };\n    // حفظ البيانات في localStorage\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSubjects(subjects);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveTeachers(teachers);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveClasses(classes);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveStudents(students);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSettings(settings);\n    console.log('تم إنشاء البيانات التجريبية بنجاح');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/sampleData.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();