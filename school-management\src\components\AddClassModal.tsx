'use client';

import React, { useState, useEffect } from 'react';
import { Class, Teacher } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface AddClassModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  editingClass?: Class | null;
}

const AddClassModal: React.FC<AddClassModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingClass
}) => {
  const [formData, setFormData] = useState<Partial<Class>>({
    name: '',
    grade: 1,
    section: '',
    capacity: 30,
    classTeacherId: '',
    academicYear: new Date().getFullYear().toString(),
    status: 'active',
    schedule: []
  });

  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      loadTeachers();
      if (editingClass) {
        setFormData({
          ...editingClass
        });
      } else {
        resetForm();
      }
    }
  }, [isOpen, editingClass]);

  const loadTeachers = () => {
    const teachersData = localStorageManager.getTeachers();
    setTeachers(teachersData);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      grade: 1,
      section: '',
      capacity: 30,
      classTeacherId: '',
      academicYear: new Date().getFullYear().toString(),
      status: 'active',
      schedule: []
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'اسم الصف مطلوب';
    }

    if (!formData.section?.trim()) {
      newErrors.section = 'الشعبة مطلوبة';
    }

    // التحقق من عدم تكرار اسم الصف والشعبة معاً
    if (formData.name?.trim() && formData.section?.trim()) {
      const existingClasses = localStorageManager.getClasses();
      const isDuplicate = existingClasses.some(c =>
        c.id !== editingClass?.id && // استثناء الصف الحالي عند التعديل
        c.name.toLowerCase().trim() === formData.name!.toLowerCase().trim() &&
        c.section.toLowerCase().trim() === formData.section!.toLowerCase().trim()
      );

      if (isDuplicate) {
        newErrors.name = 'اسم الصف مع هذه الشعبة موجود مسبقاً';
        newErrors.section = 'هذه الشعبة موجودة مسبقاً لنفس الصف';
      }
    }

    if (!formData.capacity || formData.capacity < 1) {
      newErrors.capacity = 'السعة يجب أن تكون أكبر من صفر';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const classData: Class = {
        id: editingClass?.id || `class-${Date.now()}`,
        name: formData.name!,
        grade: formData.grade!,
        section: formData.section!,
        capacity: formData.capacity!,
        classTeacherId: formData.classTeacherId || undefined,
        academicYear: formData.academicYear!,
        status: formData.status!,
        schedule: formData.schedule || [],
        createdAt: editingClass?.createdAt || new Date(),
        updatedAt: new Date()
      };

      if (editingClass) {
        localStorageManager.updateClass(editingClass.id, classData);
      } else {
        localStorageManager.addClass(classData);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ الصف:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Class, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // إزالة رسالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const getAvailableTeachers = () => {
    // إظهار المعلمين الذين ليس لديهم صف أو المعلم الحالي للصف
    const classes = localStorageManager.getClasses();
    const assignedTeacherIds = classes
      .filter(c => c.id !== editingClass?.id) // استثناء الصف الحالي
      .map(c => c.classTeacherId);
    
    return teachers.filter(t => 
      !assignedTeacherIds.includes(t.id) || t.id === editingClass?.classTeacherId
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900">
              {editingClass ? 'تعديل الصف' : 'إضافة صف جديد'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* معلومات الصف الأساسية */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات الصف الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* اسم الصف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الصف *
                  </label>
                  <input
                    type="text"
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="مثال: الصف الأول أ"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name}</p>
                  )}
                </div>

                {/* الصف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الصف *
                  </label>
                  <select
                    value={formData.grade || 1}
                    onChange={(e) => handleInputChange('grade', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={1}>الأول</option>
                    <option value={2}>الثاني</option>
                    <option value={3}>الثالث</option>
                    <option value={4}>الرابع</option>
                    <option value={5}>الخامس</option>
                    <option value={6}>السادس</option>
                  </select>
                </div>

                {/* الشعبة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الشعبة *
                  </label>
                  <input
                    type="text"
                    value={formData.section || ''}
                    onChange={(e) => handleInputChange('section', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.section ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="أ، ب، ج..."
                  />
                  {errors.section && (
                    <p className="text-red-500 text-xs mt-1">{errors.section}</p>
                  )}
                </div>


              </div>
            </div>

            {/* معلومات إضافية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* السعة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  السعة القصوى *
                </label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={formData.capacity || 30}
                  onChange={(e) => handleInputChange('capacity', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.capacity ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.capacity && (
                  <p className="text-red-500 text-xs mt-1">{errors.capacity}</p>
                )}
              </div>

              {/* السنة الدراسية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  السنة الدراسية
                </label>
                <input
                  type="text"
                  value={formData.academicYear || new Date().getFullYear().toString()}
                  onChange={(e) => handleInputChange('academicYear', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="2024"
                />
              </div>
            </div>

            {/* معلم الصف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                معلم الصف (اختياري)
              </label>
              <select
                value={formData.classTeacherId || ''}
                onChange={(e) => handleInputChange('classTeacherId', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.classTeacherId ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">بدون معلم صف (اختياري)</option>
                {getAvailableTeachers().map(teacher => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.fullName || teacher.name || teacher.shortName} - {teacher.specialization}
                  </option>
                ))}
              </select>
              {errors.classTeacherId && (
                <p className="text-red-500 text-xs mt-1">{errors.classTeacherId}</p>
              )}
              {getAvailableTeachers().length === 0 && (
                <p className="text-yellow-600 text-xs mt-1">
                  جميع المعلمين مُعينون لصفوف أخرى
                </p>
              )}
            </div>

            {/* حالة الصف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                حالة الصف
              </label>
              <select
                value={formData.status || 'active'}
                onChange={(e) => handleInputChange('status', e.target.value as 'active' | 'inactive')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
              </select>
            </div>

            <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                )}
                {loading ? 'جاري الحفظ...' : (editingClass ? 'تحديث' : 'إضافة')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddClassModal;
