'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/Layout';
import ShowDeveloperPassword from './ShowDeveloperPassword';
import ShowAllUserPasswords from './ShowAllUserPasswords';
import Card from '@/components/Card';
import Button from '@/components/Button';
import { User } from '@/types';
import { localStorageManager } from '@/utils/localStorage';
import { useAuth } from '@/hooks/useAuth';

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [newRole, setNewRole] = useState<'developer' | 'admin' | 'teacher' | 'student' | 'parent'>('teacher');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState({
    name: '',
    username: '',
    email: '',
    password: '',
    role: 'student' as 'developer' | 'admin' | 'teacher' | 'student' | 'parent'
  });
  const [editUser, setEditUser] = useState({
    name: '',
    username: '',
    email: '',
    role: 'student' as 'developer' | 'admin' | 'teacher' | 'student' | 'parent'
  });
  const { user: currentUser } = useAuth();
  const router = useRouter();

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = () => {
    try {
      const allUsers = localStorageManager.getUsers();
      setUsers(allUsers);
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = (user: User) => {
    setSelectedUser(user);
    setNewRole(user.role);
    setShowRoleModal(true);
  };

  const updateUserRole = () => {
    if (!selectedUser) return;

    try {
      const updatedUsers = users.map(user => 
        user.id === selectedUser.id 
          ? { ...user, role: newRole, updatedAt: new Date() }
          : user
      );

      localStorageManager.saveUsers(updatedUsers);
      setUsers(updatedUsers);
      setShowRoleModal(false);
      setSelectedUser(null);

      alert('تم تحديث دور المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث الدور:', error);
      alert('حدث خطأ أثناء تحديث الدور');
    }
  };

  const deleteUser = (userId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

    try {
      const updatedUsers = users.filter(user => user.id !== userId);
      localStorageManager.saveUsers(updatedUsers);
      setUsers(updatedUsers);
      alert('تم حذف المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error);
      alert('حدث خطأ أثناء حذف المستخدم');
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setEditUser({
      name: user.name,
      username: user.username || '',
      email: user.email,
      role: user.role
    });
    setShowEditModal(true);
  };

  const updateUser = () => {
    if (!editUser.name.trim() || !editUser.email.trim()) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (!editingUser) return;

    // التحقق من عدم تكرار البريد الإلكتروني
    const emailExists = users.some(u => u.id !== editingUser.id && u.email === editUser.email);
    if (emailExists) {
      alert('البريد الإلكتروني موجود بالفعل');
      return;
    }

    // التحقق من عدم تكرار اسم المستخدم
    if (editUser.username && users.some(u => u.id !== editingUser.id && u.username === editUser.username)) {
      alert('اسم المستخدم موجود بالفعل');
      return;
    }

    try {
      const updatedUsers = users.map(user =>
        user.id === editingUser.id
          ? {
              ...user,
              name: editUser.name,
              username: editUser.username || undefined,
              email: editUser.email,
              role: editUser.role,
              updatedAt: new Date()
            }
          : user
      );

      localStorageManager.saveUsers(updatedUsers);
      setUsers(updatedUsers);
      setShowEditModal(false);
      setEditingUser(null);
      setEditUser({ name: '', username: '', email: '', role: 'student' });
      alert('تم تحديث بيانات المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error);
      alert('حدث خطأ أثناء تحديث المستخدم');
    }
  };

  const createUser = () => {
    if (!newUser.name.trim() || !newUser.email.trim() || !newUser.password.trim()) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const userExists = users.some(u => u.email === newUser.email);
    if (userExists) {
      alert('المستخدم موجود بالفعل بهذا البريد الإلكتروني');
      return;
    }

    // التحقق من عدم تكرار اسم المستخدم
    if (newUser.username && users.some(u => u.username === newUser.username)) {
      alert('اسم المستخدم موجود بالفعل');
      return;
    }

    try {
      const user: User = {
        id: `user-${Date.now()}`,
        name: newUser.name,
        username: newUser.username || undefined,
        email: newUser.email,
        password: newUser.password,
        role: newUser.role,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedUsers = [...users, user];
      localStorageManager.saveUsers(updatedUsers);
      setUsers(updatedUsers);
      setShowCreateModal(false);
      setNewUser({ name: '', username: '', email: '', password: '', role: 'student' });
      alert('تم إنشاء المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في إنشاء المستخدم:', error);
      alert('حدث خطأ أثناء إنشاء المستخدم');
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'developer': return 'المطور';
      case 'admin': return 'مدير النظام';
      case 'teacher': return 'معلم';
      case 'student': return 'طالب';
      case 'parent': return 'ولي أمر';
      default: return 'غير محدد';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'developer': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-red-100 text-red-800';
      case 'teacher': return 'bg-blue-100 text-blue-800';
      case 'student': return 'bg-green-100 text-green-800';
      case 'parent': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* رأس الصفحة */}
        {/* إظهار باسورد المطور وجميع كلمات المرور (للمطور فقط) */}
        {currentUser?.role === 'developer' && (
          <>
            <ShowDeveloperPassword />
            <ShowAllUserPasswords />
          </>
        )}

        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">👥 إدارة المستخدمين</h1>
            <p className="text-gray-600">إدارة حسابات المستخدمين وصلاحياتهم</p>
            {/* رسالة للمستخدمين غير المطورين */}
            {currentUser?.role !== 'developer' && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <span className="font-medium">ملاحظة:</span> يمكنك عرض قائمة المستخدمين فقط. إدارة المستخدمين متاحة للمطور فقط.
                </p>
              </div>
            )}
          </div>
          {/* زر إضافة مستخدم - متاح للمطور فقط */}
          {currentUser?.role === 'developer' && (
            <Button
              variant="primary"
              onClick={() => setShowCreateModal(true)}
              className="flex items-center"
            >
              <span className="ml-2">➕</span>
              إضافة مستخدم جديد
            </Button>
          )}
        </div>

        {/* إحصائيات المستخدمين */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <Card className="text-center bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="text-2xl mb-2">👨‍💻</div>
            <div className="text-2xl font-bold text-purple-600">
              {users.filter(u => u.role === 'developer').length}
            </div>
            <div className="text-sm text-gray-600">المطورون</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <div className="text-2xl mb-2">👑</div>
            <div className="text-2xl font-bold text-red-600">
              {users.filter(u => u.role === 'admin').length}
            </div>
            <div className="text-sm text-gray-600">المديرون</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-2xl mb-2">👨‍🏫</div>
            <div className="text-2xl font-bold text-blue-600">
              {users.filter(u => u.role === 'teacher').length}
            </div>
            <div className="text-sm text-gray-600">المعلمون</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-2xl mb-2">👨‍🎓</div>
            <div className="text-2xl font-bold text-green-600">
              {users.filter(u => u.role === 'student').length}
            </div>
            <div className="text-sm text-gray-600">الطلاب</div>
          </Card>

          <Card className="text-center bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <div className="text-2xl mb-2">👨‍👩‍👧‍👦</div>
            <div className="text-2xl font-bold text-orange-600">
              {users.filter(u => u.role === 'parent').length}
            </div>
            <div className="text-sm text-gray-600">أولياء الأمور</div>
          </Card>
        </div>

        {/* قائمة المستخدمين */}
        <Card title="قائمة المستخدمين" subtitle={`إجمالي المستخدمين: ${users.length}`}>
          {users.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">👥</div>
              <p className="text-gray-600">لا يوجد مستخدمون في النظام</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الاسم</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">اسم المستخدم</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">البريد الإلكتروني</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الدور</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">تاريخ الإنشاء</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{user.name}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600 font-mono text-sm">
                          {user.username || '-'}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">{user.email}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                          {getRoleDisplayName(user.role)}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(user.createdAt).toLocaleDateString('en-GB')}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2 space-x-reverse">
                          {/* أزرار التحكم - متاحة للمطور فقط */}
                          {currentUser?.role === 'developer' && (
                            <>
                              <Button
                                variant="success"
                                size="sm"
                                onClick={() => handleEditUser(user)}
                              >
                                تعديل
                              </Button>
                              <Button
                                variant="primary"
                                size="sm"
                                onClick={() => handleRoleChange(user)}
                              >
                                تغيير الدور
                              </Button>
                              <Button
                                variant="danger"
                                size="sm"
                                onClick={() => deleteUser(user.id)}
                              >
                                حذف
                              </Button>
                            </>
                          )}
                          {/* رسالة للمستخدمين غير المطورين */}
                          {currentUser?.role !== 'developer' && (
                            <span className="text-sm text-gray-500 italic">
                              صلاحيات المطور فقط
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>

        {/* نافذة تغيير الدور */}
        {showRoleModal && selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">تغيير دور المستخدم</h3>
              <p className="text-gray-600 mb-4">
                تغيير دور: <strong>{selectedUser.name}</strong>
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الدور الجديد
                </label>
                <select
                  value={newRole}
                  onChange={(e) => setNewRole(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="developer">المطور</option>
                  <option value="admin">مدير النظام</option>
                  <option value="teacher">معلم</option>
                  <option value="student">طالب</option>
                  <option value="parent">ولي أمر</option>
                </select>
              </div>

              <div className="flex space-x-2 space-x-reverse">
                <Button variant="primary" onClick={updateUserRole}>
                  تحديث
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowRoleModal(false);
                    setSelectedUser(null);
                  }}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* نافذة تعديل المستخدم */}
        {showEditModal && editingUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">تعديل بيانات المستخدم</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الكامل
                  </label>
                  <input
                    type="text"
                    value={editUser.name}
                    onChange={(e) => setEditUser({...editUser, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل الاسم الكامل"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم المستخدم <span className="text-gray-500">(اختياري)</span>
                  </label>
                  <input
                    type="text"
                    value={editUser.username}
                    onChange={(e) => setEditUser({...editUser, username: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل اسم المستخدم"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={editUser.email}
                    onChange={(e) => setEditUser({...editUser, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الدور
                  </label>
                  <select
                    value={editUser.role}
                    onChange={(e) => setEditUser({...editUser, role: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="developer">المطور</option>
                    <option value="admin">مدير النظام</option>
                    <option value="teacher">معلم</option>
                    <option value="student">طالب</option>
                    <option value="parent">ولي أمر</option>
                  </select>
                </div>
              </div>

              <div className="flex space-x-2 space-x-reverse mt-6">
                <Button variant="primary" onClick={updateUser}>
                  حفظ التغييرات
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingUser(null);
                    setEditUser({ name: '', username: '', email: '', role: 'student' });
                  }}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* نافذة إنشاء مستخدم جديد */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">إنشاء مستخدم جديد</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الكامل
                  </label>
                  <input
                    type="text"
                    value={newUser.name}
                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل الاسم الكامل"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم المستخدم <span className="text-gray-500">(اختياري)</span>
                  </label>
                  <input
                    type="text"
                    value={newUser.username}
                    onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل اسم المستخدم"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور
                  </label>
                  <input
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل كلمة المرور"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الدور
                  </label>
                  <select
                    value={newUser.role}
                    onChange={(e) => setNewUser({...newUser, role: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="developer">المطور</option>
                    <option value="admin">مدير النظام</option>
                    <option value="teacher">معلم</option>
                    <option value="student">طالب</option>
                    <option value="parent">ولي أمر</option>
                  </select>
                </div>
              </div>

              <div className="flex space-x-2 space-x-reverse mt-6">
                <Button variant="primary" onClick={createUser}>
                  إنشاء المستخدم
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowCreateModal(false);
                    setNewUser({ name: '', username: '', email: '', password: '', role: 'student' });
                  }}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
