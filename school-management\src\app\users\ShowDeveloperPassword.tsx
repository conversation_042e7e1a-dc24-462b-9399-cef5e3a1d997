// مكون لإظهار باسورد المطور فقط
'use client';
import React, { useState, useEffect } from 'react';
import { localStorageManager } from '@/utils/localStorage';
import Card from '@/components/Card';
import Button from '@/components/Button';
import { User } from '@/types';

export default function ShowDeveloperPassword() {
  const [developerUser, setDeveloperUser] = useState<User | null>(null);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const users = localStorageManager.getUsers();
    const dev = users.find(u => u.role === 'developer');
    setDeveloperUser(dev || null);
  }, []);

  if (!developerUser) {
    return (
      <Card className="my-8 text-center">
        <div className="text-red-600 font-bold text-lg">لا يوجد مستخدم مطور في النظام</div>
      </Card>
    );
  }

  return (
    <Card className="my-8 max-w-xl mx-auto">
      <div className="flex flex-col items-center space-y-4">
        <div className="text-xl font-bold text-purple-700">معلومات المطور</div>
        <div>الاسم: <span className="font-mono text-lg">{developerUser.name}</span></div>
        <div>اسم المستخدم: <span className="font-mono text-lg">{developerUser.username || '-'}</span></div>
        <div>البريد الإلكتروني: <span className="font-mono text-lg">{developerUser.email}</span></div>
        <Button variant="primary" onClick={() => setShow(!show)}>
          {show ? 'إخفاء كلمة المرور' : 'إظهار كلمة المرور'}
        </Button>
        {show && (
          <div className="bg-gray-100 rounded p-3 mt-2 text-center">
            <span className="font-mono text-lg text-red-700">{developerUser.password}</span>
          </div>
        )}
      </div>
    </Card>
  );
}
