'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

interface NavigationButtonsProps {
  showBackButton?: boolean;
  showHomeButton?: boolean;
  customBackAction?: () => void;
  customHomeAction?: () => void;
  className?: string;
}

const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  showBackButton = true,
  showHomeButton = true,
  customBackAction,
  customHomeAction,
  className = ''
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (customBackAction) {
      customBackAction();
    } else {
      router.back();
    }
  };

  const handleHome = () => {
    if (customHomeAction) {
      customHomeAction();
    } else {
      router.push('/');
    }
  };

  if (!showBackButton && !showHomeButton) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-3 space-x-reverse ${className}`}>
      {showBackButton && (
        <button
          onClick={handleBack}
          className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 group"
          title="الرجوع للخلف"
        >
          <span className="text-lg ml-2 group-hover:transform group-hover:-translate-x-1 transition-transform duration-200">
            ←
          </span>
          <span className="text-sm font-medium">رجوع</span>
        </button>
      )}

      {showHomeButton && (
        <button
          onClick={handleHome}
          className="flex items-center px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200 group"
          title="الرجوع للواجهة الرئيسية"
        >
          <span className="text-lg ml-2 group-hover:transform group-hover:scale-110 transition-transform duration-200">
            🏠
          </span>
          <span className="text-sm font-medium">الرئيسية</span>
        </button>
      )}
    </div>
  );
};

export default NavigationButtons;
