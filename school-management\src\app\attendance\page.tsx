'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import { Attendance, Student, Class } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

export default function AttendancePage() {
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const attendanceData = localStorageManager.getAttendance();
      const studentsData = localStorageManager.getStudents();
      const classesData = localStorageManager.getClasses();
      
      setAttendance(attendanceData);
      setStudents(studentsData);
      setClasses(classesData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredAttendance = attendance.filter(record => {
    const matchesClass = selectedClass === '' || record.classId === selectedClass;
    const matchesDate = selectedDate === '' || 
      new Date(record.date).toISOString().split('T')[0] === selectedDate;
    const matchesStatus = selectedStatus === '' || record.status === selectedStatus;
    
    return matchesClass && matchesDate && matchesStatus;
  });

  const getStudentName = (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    return student ? student.name : 'غير محدد';
  };

  const getClassName = (classId: string) => {
    const classData = classes.find(c => c.id === classId);
    return classData ? classData.name : 'غير محدد';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'bg-green-100 text-green-800';
      case 'absent': return 'bg-red-100 text-red-800';
      case 'late': return 'bg-yellow-100 text-yellow-800';
      case 'excused': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'present': return 'حاضر';
      case 'absent': return 'غائب';
      case 'late': return 'متأخر';
      case 'excused': return 'غياب مبرر';
      default: return 'غير محدد';
    }
  };

  const calculateAttendanceStats = () => {
    const todayAttendance = attendance.filter(record => 
      new Date(record.date).toDateString() === new Date().toDateString()
    );

    const present = todayAttendance.filter(r => r.status === 'present').length;
    const absent = todayAttendance.filter(r => r.status === 'absent').length;
    const late = todayAttendance.filter(r => r.status === 'late').length;
    const excused = todayAttendance.filter(r => r.status === 'excused').length;

    return { present, absent, late, excused, total: todayAttendance.length };
  };

  const getAttendanceRate = () => {
    if (attendance.length === 0) return 0;
    const presentRecords = attendance.filter(r => r.status === 'present').length;
    return Math.round((presentRecords / attendance.length) * 100);
  };

  const markAttendanceForClass = (classId: string, date: string) => {
    const classStudents = students.filter(s => s.classId === classId);
    const existingRecords = attendance.filter(r => 
      r.classId === classId && 
      new Date(r.date).toDateString() === new Date(date).toDateString()
    );

    if (existingRecords.length > 0) {
      alert('تم تسجيل الحضور لهذا الصف في هذا التاريخ مسبقاً');
      return;
    }

    // إنشاء سجلات حضور جديدة لجميع طلاب الصف
    const newRecords: Attendance[] = classStudents.map(student => ({
      id: `attendance-${Date.now()}-${student.id}`,
      studentId: student.id,
      classId: classId,
      date: new Date(date),
      status: 'present', // افتراضياً جميع الطلاب حاضرون
      recordedBy: 'current-user', // يجب استبداله بمعرف المستخدم الحالي
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    newRecords.forEach(record => {
      localStorageManager.addAttendance(record);
    });

    loadData();
    alert(`تم تسجيل الحضور لـ ${classStudents.length} طالب في ${getClassName(classId)}`);
  };

  const stats = calculateAttendanceStats();

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الحضور</h1>
            <p className="text-gray-600 mt-1">تسجيل ومتابعة حضور الطلاب</p>
          </div>
          <div className="flex space-x-2 space-x-reverse mt-4 md:mt-0">
            <Button 
              variant="success" 
              onClick={() => setShowAddModal(true)}
            >
              تسجيل حضور فردي
            </Button>
          </div>
        </div>

        {/* تسجيل الحضور للصف */}
        <Card title="تسجيل الحضور للصف">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختر الصف
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="input-field"
              >
                <option value="">اختر الصف</option>
                {classes.map(classItem => (
                  <option key={classItem.id} value={classItem.id}>
                    {classItem.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <Button
                variant="primary"
                onClick={() => selectedClass && markAttendanceForClass(selectedClass, selectedDate)}
                disabled={!selectedClass}
                fullWidth
              >
                تسجيل الحضور للصف
              </Button>
            </div>
          </div>
        </Card>

        {/* أدوات التصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب الصف
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الصفوف</option>
                {classes.map(classItem => (
                  <option key={classItem.id} value={classItem.id}>
                    {classItem.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب التاريخ
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب الحالة
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الحالات</option>
                <option value="present">حاضر</option>
                <option value="absent">غائب</option>
                <option value="late">متأخر</option>
                <option value="excused">غياب مبرر</option>
              </select>
            </div>
          </div>
        </Card>

        {/* إحصائيات الحضور */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">{stats.present}</div>
              <div className="text-green-600 text-sm">حاضر اليوم</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-900">{stats.absent}</div>
              <div className="text-red-600 text-sm">غائب اليوم</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-900">{stats.late}</div>
              <div className="text-yellow-600 text-sm">متأخر اليوم</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">{stats.excused}</div>
              <div className="text-blue-600 text-sm">غياب مبرر</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">{getAttendanceRate()}%</div>
              <div className="text-purple-600 text-sm">معدل الحضور</div>
            </div>
          </Card>
        </div>

        {/* قائمة الحضور */}
        <Card title="سجل الحضور" subtitle={`عدد النتائج: ${filteredAttendance.length}`}>
          {filteredAttendance.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">✅</div>
              <p className="text-gray-600">لا توجد سجلات حضور مطابقة للمرشحات المحددة</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الطالب</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الصف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">التاريخ</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الحالة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">وقت الوصول</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">وقت المغادرة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">ملاحظات</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAttendance.map((record) => (
                    <tr key={record.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">
                          {getStudentName(record.studentId)}
                        </div>
                      </td>
                      <td className="py-3 px-4">{getClassName(record.classId)}</td>
                      <td className="py-3 px-4">
                        {new Date(record.date).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(record.status)}`}>
                          {getStatusText(record.status)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {record.checkInTime || '-'}
                      </td>
                      <td className="py-3 px-4">
                        {record.checkOutTime || '-'}
                      </td>
                      <td className="py-3 px-4">
                        {record.notes || '-'}
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          size="sm"
                          variant="secondary"
                        >
                          تعديل
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
}
