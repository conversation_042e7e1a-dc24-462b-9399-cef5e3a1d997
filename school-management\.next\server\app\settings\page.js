/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZzZXR0aW5ncyUyRnBhZ2UmcGFnZT0lMkZzZXR0aW5ncyUyRnBhZ2UmYXBwUGF0aHM9JTJGc2V0dGluZ3MlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGc2V0dGluZ3MlMkZwYWdlLnRzeCZhcHBEaXI9RSUzQSU1QyVEOSU4NiVEOCVCOCVEOCVBNyVEOSU4NSUyMCVEOSU4NSVEOCVBRiVEOCVCMSVEOCVCMyVEOCVBOSU1Q3NjaG9vbC1tYW5hZ2VtZW50JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1FJTNBJTVDJUQ5JTg2JUQ4JUI4JUQ4JUE3JUQ5JTg1JTIwJUQ5JTg1JUQ4JUFGJUQ4JUIxJUQ4JUIzJUQ4JUE5JTVDc2Nob29sLW1hbmFnZW1lbnQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBNEY7QUFDbEgsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLGtLQUFvRztBQUd0SDtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBb1A7QUFDeFI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUFvUDtBQUN4UjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcc3JjXFxcXGFwcFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnc2V0dGluZ3MnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9zZXR0aW5ncy9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9zZXR0aW5nc1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3NldHRpbmdzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFvRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e7050786830a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc2YbYuNin2YUg2YXYr9ix2LPYqVxcc2Nob29sLW1hbmFnZW1lbnRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU3MDUwNzg2ODMwYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"نظام إدارة المدرسة\",\n    description: \"نظام شامل لإدارة المدارس باللغة العربية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\نظام مدرسة\\school-management\\src\\app\\settings\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(ssr)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3NldHRpbmdzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFvRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* harmony import */ var _utils_appInfo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/appInfo */ \"(ssr)/./src/utils/appInfo.ts\");\n/* harmony import */ var _components_NavigationButtons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NavigationButtons */ \"(ssr)/./src/components/NavigationButtons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction SettingsPage() {\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const appInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_4__.getAppInfo)();\n    const developerInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_4__.getDeveloperInfo)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            loadSettings();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    const loadSettings = ()=>{\n        try {\n            const currentSettings = _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageManager.getSettings();\n            setSettings(currentSettings);\n            setLogoPreview(currentSettings?.schoolLogo || null);\n        } catch (error) {\n            console.error('خطأ في تحميل الإعدادات:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في تحميل الإعدادات'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogoUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            if (!file.type.startsWith('image/')) {\n                setMessage({\n                    type: 'error',\n                    text: 'يرجى اختيار ملف صورة صالح'\n                });\n                return;\n            }\n            if (file.size > 2 * 1024 * 1024) {\n                setMessage({\n                    type: 'error',\n                    text: 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت'\n                });\n                return;\n            }\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                const result = e.target?.result;\n                setLogoPreview(result);\n                if (settings) {\n                    setSettings({\n                        ...settings,\n                        schoolLogo: result\n                    });\n                }\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const handleRemoveLogo = ()=>{\n        setLogoPreview(null);\n        if (settings) {\n            setSettings({\n                ...settings,\n                schoolLogo: undefined\n            });\n        }\n    };\n    const handleSave = async ()=>{\n        if (!settings) return;\n        setSaving(true);\n        try {\n            const updatedSettings = {\n                ...settings,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageManager.saveSettings(updatedSettings);\n            setSettings(updatedSettings);\n            setMessage({\n                type: 'success',\n                text: 'تم حفظ الإعدادات وتطبيق التغييرات بنجاح'\n            });\n            applySettingsToApp(updatedSettings);\n            setTimeout(()=>setMessage(null), 3000);\n        } catch (error) {\n            console.error('خطأ في حفظ الإعدادات:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في حفظ الإعدادات'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const applySettingsToApp = (newSettings)=>{\n        try {\n            document.title = `${newSettings.schoolName} - نظام إدارة المدرسة`;\n            const sidebarTitle = document.querySelector('[data-sidebar-title]');\n            if (sidebarTitle) {\n                sidebarTitle.textContent = newSettings.schoolName;\n            }\n            window.dispatchEvent(new CustomEvent('settingsUpdated', {\n                detail: newSettings\n            }));\n            console.log('تم تطبيق الإعدادات الجديدة في التطبيق');\n        } catch (error) {\n            console.error('خطأ في تطبيق الإعدادات:', error);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        if (!settings) return;\n        setSettings({\n            ...settings,\n            [field]: value\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري تحميل الإعدادات...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!settings) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-lg\",\n                        children: \"فشل في تحميل الإعدادات\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadSettings,\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"إعادة المحاولة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationButtons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"mb-6\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl text-white\",\n                                children: \"⚙️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\",\n                            children: \"إعدادات النظام\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg mt-1\",\n                            children: \"إدارة وتخصيص إعدادات المدرسة والنظام\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-4 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `mb-8 p-6 rounded-2xl shadow-lg border-l-4 ${message.type === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 text-green-800' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-500 text-red-800'}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-10 h-10 rounded-full flex items-center justify-center ml-4 ${message.type === 'success' ? 'bg-green-100' : 'bg-red-100'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: message.type === 'success' ? '✅' : '❌'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-lg\",\n                                        children: message.type === 'success' ? 'تم بنجاح!' : 'خطأ!'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm opacity-90\",\n                                        children: message.text\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 xl:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center ml-4 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl text-white\",\n                                                children: \"\\uD83C\\uDFEB\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-gray-800\",\n                                                    children: \"معلومات المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"البيانات الأساسية للمؤسسة التعليمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"شعار المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-6 space-x-reverse\",\n                                                    children: [\n                                                        logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"شعار المدرسة\",\n                                                                    className: \"w-24 h-24 object-contain border-2 border-gray-200 rounded-lg bg-gray-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleRemoveLogo,\n                                                                    className: \"absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors\",\n                                                                    title: \"حذف الشعار\",\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-xl\",\n                                                                        children: \"\\uD83C\\uDFEB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: \"لا يوجد شعار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    id: \"logoUpload\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: handleLogoUpload,\n                                                                    className: \"hidden\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"logoUpload\",\n                                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2\",\n                                                                            children: \"\\uD83D\\uDCC1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        logoPreview ? 'تغيير الشعار' : 'رفع شعار'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 text-center\",\n                                                                    children: [\n                                                                        \"الحد الأقصى: 2 ميجابايت\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 46\n                                                                        }, this),\n                                                                        \"الصيغ المدعومة: JPG, PNG, GIF\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"اسم المدرسة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: settings.schoolName,\n                                                    onChange: (e)=>handleInputChange('schoolName', e.target.value),\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300\",\n                                                    placeholder: \"أدخل اسم المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"العنوان\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: settings.address,\n                                                    onChange: (e)=>handleInputChange('address', e.target.value),\n                                                    rows: 2,\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-green-100 focus:border-green-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300 resize-none\",\n                                                    placeholder: \"أدخل عنوان المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"رقم الهاتف\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"tel\",\n                                                    value: settings.phone,\n                                                    onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-purple-100 focus:border-purple-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300\",\n                                                    placeholder: \"أدخل رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"العام الدراسي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: settings.academicYear,\n                                                    onChange: (e)=>handleInputChange('academicYear', e.target.value),\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-orange-100 focus:border-orange-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300\",\n                                                    placeholder: \"مثال: 2024-2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center ml-4 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl text-white\",\n                                                children: \"\\uD83D\\uDCBB\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-gray-800\",\n                                                    children: \"معلومات التطبيق والمطور\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"تفاصيل النظام ومطور التطبيق\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3 shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl text-white\",\n                                                            children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-800\",\n                                                                children: developerInfo.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm\",\n                                                                children: \"مطور النظام\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDCF1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"رقم الهاتف:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600 font-mono font-semibold\",\n                                                                children: \"07813332882\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDCBC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"التخصص:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-purple-600 font-semibold\",\n                                                                children: \"مطور تطبيقات ويب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDEE0️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"التقنيات:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-indigo-600 font-semibold\",\n                                                                children: \"React, Next.js, TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDCC5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"سنة التطوير:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-green-600 font-semibold\",\n                                                                children: \"2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✈️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"تلكرام:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://t.me/ob992\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-sm text-blue-600 font-mono font-semibold hover:text-blue-800 transition-colors\",\n                                                                children: \"ob992\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center ml-3 shadow-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg text-white\",\n                                                                    children: \"\\uD83D\\uDCBB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"معلومات النظام\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm\",\n                                                                        children: \"تفاصيل التطبيق والإصدار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"إصدار النظام:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-purple-600 font-semibold\",\n                                                                        children: appInfo.version\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"آخر تحديث:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 font-semibold\",\n                                                                        children: settings.updatedAt ? new Date(settings.updatedAt).toLocaleDateString('en-GB') : 'غير محدد'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"نوع النظام:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-pink-600 font-semibold\",\n                                                                        children: \"نظام إدارة مدرسة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"حقوق الطبع:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 font-semibold\",\n                                                                        children: \"جميع الحقوق محفوظة 2025\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 flex justify-center space-x-6 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-2xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-semibold flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"↩️\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء التغييرات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSave,\n                            disabled: saving,\n                            className: \"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n                            children: [\n                                saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: saving ? '⏳' : '💾'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NavigationButtons.tsx":
/*!**********************************************!*\
  !*** ./src/components/NavigationButtons.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst NavigationButtons = ({ showBackButton = true, showHomeButton = true, customBackAction, customHomeAction, className = '' })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleBack = ()=>{\n        if (customBackAction) {\n            customBackAction();\n        } else {\n            router.back();\n        }\n    };\n    const handleHome = ()=>{\n        if (customHomeAction) {\n            customHomeAction();\n        } else {\n            router.push('/');\n        }\n    };\n    if (!showBackButton && !showHomeButton) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 space-x-reverse ${className}`,\n        children: [\n            showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleBack,\n                className: \"flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 group\",\n                title: \"الرجوع للخلف\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg ml-2 group-hover:transform group-hover:-translate-x-1 transition-transform duration-200\",\n                        children: \"←\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"رجوع\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined),\n            showHomeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleHome,\n                className: \"flex items-center px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200 group\",\n                title: \"الرجوع للواجهة الرئيسية\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg ml-2 group-hover:transform group-hover:scale-110 transition-transform duration-200\",\n                        children: \"\\uD83C\\uDFE0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"الرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavigationButtons);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NavigationButtons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"useAuth.useEffect\"], []);\n    const checkAuth = ()=>{\n        try {\n            const currentUser = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getCurrentUser();\n            setUser(currentUser);\n        } catch (error) {\n            console.error('خطأ في التحقق من المصادقة:', error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (emailOrUsername, password)=>{\n        try {\n            setLoading(true);\n            // محاكاة تأخير الشبكة\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const foundUser = users.find((u)=>(u.email === emailOrUsername || u.username === emailOrUsername) && u.password === password);\n            if (!foundUser) {\n                return {\n                    success: false,\n                    error: 'البريد الإلكتروني/اسم المستخدم أو كلمة المرور غير صحيحة'\n                };\n            }\n            // تسجيل الدخول بنجاح\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(foundUser);\n            setUser(foundUser);\n            // إضافة إشعار نجاح تسجيل الدخول\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'تم تسجيل الدخول بنجاح',\n                message: `مرحباً ${foundUser.name}، تم تسجيل دخولك بنجاح`,\n                type: 'success',\n                recipientId: foundUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: 'حدث خطأ أثناء تسجيل الدخول'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        try {\n            // إضافة إشعار تسجيل الخروج\n            if (user) {\n                const notification = {\n                    id: `notification-${Date.now()}`,\n                    title: 'تم تسجيل الخروج',\n                    message: `تم تسجيل خروجك من النظام بنجاح`,\n                    type: 'info',\n                    recipientId: user.id,\n                    recipientType: 'user',\n                    isRead: false,\n                    createdAt: new Date()\n                };\n                _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            }\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.logout();\n            setUser(null);\n            router.push('/login');\n        } catch (error) {\n            console.error('خطأ في تسجيل الخروج:', error);\n        }\n    };\n    const hasRole = (roles)=>{\n        if (!user) return false;\n        return roles.includes(user.role);\n    };\n    const updatePassword = async (currentPassword, newPassword)=>{\n        try {\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'المستخدم غير مسجل الدخول'\n                };\n            }\n            // التحقق من كلمة المرور الحالية\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const currentUser = users.find((u)=>u.id === user.id);\n            if (!currentUser || currentUser.password !== currentPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الحالية غير صحيحة'\n                };\n            }\n            // التحقق من قوة كلمة المرور الجديدة\n            if (newPassword.length < 3) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة قصيرة جداً (الحد الأدنى 3 أحرف)'\n                };\n            }\n            if (currentPassword === newPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية'\n                };\n            }\n            // تحديث كلمة المرور\n            const updatedUsers = users.map((u)=>u.id === user.id ? {\n                    ...u,\n                    password: newPassword,\n                    updatedAt: new Date()\n                } : u);\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.saveUsers(updatedUsers);\n            // تحديث المستخدم الحالي\n            const updatedUser = {\n                ...user,\n                password: newPassword,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(updatedUser);\n            setUser(updatedUser);\n            // إضافة إشعار نجاح تغيير كلمة المرور\n            const notification = {\n                id: `notification-${Date.now()}`,\n                title: 'تم تغيير كلمة المرور',\n                message: 'تم تغيير كلمة المرور بنجاح',\n                type: 'success',\n                recipientId: user.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تحديث كلمة المرور:', error);\n            return {\n                success: false,\n                error: 'فشل في تحديث كلمة المرور'\n            };\n        }\n    };\n    return {\n        user,\n        loading,\n        isAuthenticated: !!user,\n        login,\n        logout,\n        checkAuth,\n        hasRole,\n        updatePassword\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/appInfo.ts":
/*!******************************!*\
  !*** ./src/utils/appInfo.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_INFO: () => (/* binding */ APP_INFO),\n/* harmony export */   getAppInfo: () => (/* binding */ getAppInfo),\n/* harmony export */   getDeveloperInfo: () => (/* binding */ getDeveloperInfo),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo)\n/* harmony export */ });\n// معلومات التطبيق والمطور\nconst APP_INFO = {\n    name: 'نظام إدارة المدرسة',\n    version: '1.0.0',\n    description: 'نظام شامل لإدارة المدارس باللغة العربية',\n    developer: 'عبيدة العيثاوي',\n    developedBy: 'تم التطوير بواسطة عبيدة العيثاوي',\n    copyright: `© ${new Date().getFullYear()} عبيدة العيثاوي - جميع الحقوق محفوظة`,\n    features: [\n        'إدارة الطلاب والمعلمين',\n        'إدارة الصفوف والمواد الدراسية',\n        'نظام التقييم والدرجات',\n        'تقارير شاملة ومفصلة',\n        'واجهة عربية متجاوبة',\n        'تصميم نيومورفيك عصري'\n    ],\n    technologies: [\n        'Next.js 15',\n        'React 19',\n        'TypeScript',\n        'Tailwind CSS',\n        'localStorage'\n    ],\n    contact: {\n        developer: 'عبيدة العيثاوي',\n        email: '<EMAIL>',\n        phone: '07813332882'\n    }\n};\n// دالة للحصول على معلومات التطبيق\nconst getAppInfo = ()=>APP_INFO;\n// دالة للحصول على معلومات المطور\nconst getDeveloperInfo = ()=>({\n        name: APP_INFO.developer,\n        developedBy: APP_INFO.developedBy,\n        copyright: APP_INFO.copyright,\n        contact: APP_INFO.contact\n    });\n// دالة للحصول على معلومات الإصدار\nconst getVersionInfo = ()=>({\n        version: APP_INFO.version,\n        name: APP_INFO.name,\n        description: APP_INFO.description\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/appInfo.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/localStorage.ts":
/*!***********************************!*\
  !*** ./src/utils/localStorage.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageManager: () => (/* binding */ localStorageManager),\n/* harmony export */   searchUtils: () => (/* binding */ searchUtils)\n/* harmony export */ });\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    STUDENTS: 'school_students',\n    TEACHERS: 'school_teachers',\n    EMPLOYEES: 'school_employees',\n    CLASSES: 'school_classes',\n    SUBJECTS: 'school_subjects',\n    GRADES: 'school_grades',\n    ATTENDANCE: 'school_attendance',\n    USERS: 'school_users',\n    SETTINGS: 'school_settings',\n    EVENTS: 'school_events',\n    NOTIFICATIONS: 'school_notifications',\n    CURRENT_USER: 'school_current_user',\n    ACADEMIC_YEAR: 'school_academic_year',\n    CURRENT_SEMESTER: 'school_current_semester'\n};\n// فئة إدارة التخزين المحلي\nclass LocalStorageManager {\n    // حفظ البيانات\n    setItem(key, data) {\n        try {\n            const serializedData = JSON.stringify(data);\n            localStorage.setItem(key, serializedData);\n        } catch (error) {\n            console.error(`خطأ في حفظ البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // استرجاع البيانات\n    getItem(key) {\n        try {\n            const serializedData = localStorage.getItem(key);\n            if (serializedData === null) {\n                return null;\n            }\n            return JSON.parse(serializedData);\n        } catch (error) {\n            console.error(`خطأ في استرجاع البيانات للمفتاح ${key}:`, error);\n            return null;\n        }\n    }\n    // حذف البيانات\n    removeItem(key) {\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(`خطأ في حذف البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // مسح جميع البيانات\n    clearAll() {\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            this.removeItem(key);\n        });\n    }\n    // === إدارة الطلاب ===\n    getStudents() {\n        return this.getItem(STORAGE_KEYS.STUDENTS) || [];\n    }\n    saveStudents(students) {\n        this.setItem(STORAGE_KEYS.STUDENTS, students);\n    }\n    addStudent(student) {\n        const students = this.getStudents();\n        students.push(student);\n        this.saveStudents(students);\n    }\n    updateStudent(studentId, updatedStudent) {\n        const students = this.getStudents();\n        const index = students.findIndex((s)=>s.id === studentId);\n        if (index !== -1) {\n            students[index] = {\n                ...students[index],\n                ...updatedStudent,\n                updatedAt: new Date()\n            };\n            this.saveStudents(students);\n        }\n    }\n    deleteStudent(studentId) {\n        const students = this.getStudents();\n        const filteredStudents = students.filter((s)=>s.id !== studentId);\n        this.saveStudents(filteredStudents);\n    }\n    getStudentById(studentId) {\n        const students = this.getStudents();\n        return students.find((s)=>s.id === studentId) || null;\n    }\n    // === إدارة المعلمين ===\n    getTeachers() {\n        return this.getItem(STORAGE_KEYS.TEACHERS) || [];\n    }\n    saveTeachers(teachers) {\n        this.setItem(STORAGE_KEYS.TEACHERS, teachers);\n    }\n    addTeacher(teacher) {\n        const teachers = this.getTeachers();\n        teachers.push(teacher);\n        this.saveTeachers(teachers);\n    }\n    updateTeacher(teacherId, updatedTeacher) {\n        const teachers = this.getTeachers();\n        const index = teachers.findIndex((t)=>t.id === teacherId);\n        if (index !== -1) {\n            teachers[index] = {\n                ...teachers[index],\n                ...updatedTeacher,\n                updatedAt: new Date()\n            };\n            this.saveTeachers(teachers);\n        }\n    }\n    deleteTeacher(teacherId) {\n        const teachers = this.getTeachers();\n        const filteredTeachers = teachers.filter((t)=>t.id !== teacherId);\n        this.saveTeachers(filteredTeachers);\n    }\n    getTeacherById(teacherId) {\n        const teachers = this.getTeachers();\n        return teachers.find((t)=>t.id === teacherId) || null;\n    }\n    // === إدارة الموظفين ===\n    getEmployees() {\n        return this.getItem(STORAGE_KEYS.EMPLOYEES) || [];\n    }\n    saveEmployees(employees) {\n        this.setItem(STORAGE_KEYS.EMPLOYEES, employees);\n    }\n    addEmployee(employee) {\n        const employees = this.getEmployees();\n        employees.push(employee);\n        this.saveEmployees(employees);\n    }\n    updateEmployee(employeeId, updatedEmployee) {\n        const employees = this.getEmployees();\n        const index = employees.findIndex((e)=>e.id === employeeId);\n        if (index !== -1) {\n            employees[index] = {\n                ...employees[index],\n                ...updatedEmployee,\n                updatedAt: new Date()\n            };\n            this.saveEmployees(employees);\n        }\n    }\n    deleteEmployee(employeeId) {\n        const employees = this.getEmployees();\n        const filteredEmployees = employees.filter((e)=>e.id !== employeeId);\n        this.saveEmployees(filteredEmployees);\n    }\n    getEmployeeById(employeeId) {\n        const employees = this.getEmployees();\n        return employees.find((e)=>e.id === employeeId) || null;\n    }\n    // === إدارة الصفوف ===\n    getClasses() {\n        return this.getItem(STORAGE_KEYS.CLASSES) || [];\n    }\n    saveClasses(classes) {\n        this.setItem(STORAGE_KEYS.CLASSES, classes);\n    }\n    addClass(classData) {\n        const classes = this.getClasses();\n        classes.push(classData);\n        this.saveClasses(classes);\n    }\n    updateClass(classId, updatedClass) {\n        const classes = this.getClasses();\n        const index = classes.findIndex((c)=>c.id === classId);\n        if (index !== -1) {\n            classes[index] = {\n                ...classes[index],\n                ...updatedClass,\n                updatedAt: new Date()\n            };\n            this.saveClasses(classes);\n        }\n    }\n    deleteClass(classId) {\n        const classes = this.getClasses();\n        const filteredClasses = classes.filter((c)=>c.id !== classId);\n        this.saveClasses(filteredClasses);\n    }\n    getClassById(classId) {\n        const classes = this.getClasses();\n        return classes.find((c)=>c.id === classId) || null;\n    }\n    // === إدارة المواد ===\n    getSubjects() {\n        return this.getItem(STORAGE_KEYS.SUBJECTS) || [];\n    }\n    saveSubjects(subjects) {\n        this.setItem(STORAGE_KEYS.SUBJECTS, subjects);\n    }\n    addSubject(subject) {\n        const subjects = this.getSubjects();\n        subjects.push(subject);\n        this.saveSubjects(subjects);\n    }\n    updateSubject(subjectId, updatedSubject) {\n        const subjects = this.getSubjects();\n        const index = subjects.findIndex((s)=>s.id === subjectId);\n        if (index !== -1) {\n            subjects[index] = {\n                ...subjects[index],\n                ...updatedSubject,\n                updatedAt: new Date()\n            };\n            this.saveSubjects(subjects);\n        }\n    }\n    deleteSubject(subjectId) {\n        const subjects = this.getSubjects();\n        const filteredSubjects = subjects.filter((s)=>s.id !== subjectId);\n        this.saveSubjects(filteredSubjects);\n    }\n    getSubjectById(subjectId) {\n        const subjects = this.getSubjects();\n        return subjects.find((s)=>s.id === subjectId) || null;\n    }\n    // === إدارة الدرجات ===\n    getGrades() {\n        return this.getItem(STORAGE_KEYS.GRADES) || [];\n    }\n    saveGrades(grades) {\n        this.setItem(STORAGE_KEYS.GRADES, grades);\n    }\n    addGrade(grade) {\n        const grades = this.getGrades();\n        grades.push(grade);\n        this.saveGrades(grades);\n    }\n    updateGrade(gradeId, updatedGrade) {\n        const grades = this.getGrades();\n        const index = grades.findIndex((g)=>g.id === gradeId);\n        if (index !== -1) {\n            grades[index] = {\n                ...grades[index],\n                ...updatedGrade,\n                updatedAt: new Date()\n            };\n            this.saveGrades(grades);\n        }\n    }\n    deleteGrade(gradeId) {\n        const grades = this.getGrades();\n        const filteredGrades = grades.filter((g)=>g.id !== gradeId);\n        this.saveGrades(filteredGrades);\n    }\n    getGradesByStudent(studentId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.studentId === studentId);\n    }\n    getGradesByClass(classId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.classId === classId);\n    }\n    // === إدارة الحضور ===\n    getAttendance() {\n        return this.getItem(STORAGE_KEYS.ATTENDANCE) || [];\n    }\n    saveAttendance(attendance) {\n        this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);\n    }\n    addAttendance(attendance) {\n        const attendanceRecords = this.getAttendance();\n        attendanceRecords.push(attendance);\n        this.saveAttendance(attendanceRecords);\n    }\n    updateAttendance(attendanceId, updatedAttendance) {\n        const attendanceRecords = this.getAttendance();\n        const index = attendanceRecords.findIndex((a)=>a.id === attendanceId);\n        if (index !== -1) {\n            attendanceRecords[index] = {\n                ...attendanceRecords[index],\n                ...updatedAttendance,\n                updatedAt: new Date()\n            };\n            this.saveAttendance(attendanceRecords);\n        }\n    }\n    getAttendanceByStudent(studentId) {\n        const attendance = this.getAttendance();\n        return attendance.filter((a)=>a.studentId === studentId);\n    }\n    getAttendanceByDate(date) {\n        const attendance = this.getAttendance();\n        const dateString = date.toDateString();\n        return attendance.filter((a)=>new Date(a.date).toDateString() === dateString);\n    }\n    // === إدارة المستخدمين ===\n    getUsers() {\n        return this.getItem(STORAGE_KEYS.USERS) || [];\n    }\n    saveUsers(users) {\n        this.setItem(STORAGE_KEYS.USERS, users);\n    }\n    getCurrentUser() {\n        return this.getItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    setCurrentUser(user) {\n        this.setItem(STORAGE_KEYS.CURRENT_USER, user);\n    }\n    logout() {\n        this.removeItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    // === إدارة الإعدادات ===\n    getSettings() {\n        return this.getItem(STORAGE_KEYS.SETTINGS);\n    }\n    saveSettings(settings) {\n        this.setItem(STORAGE_KEYS.SETTINGS, settings);\n    }\n    // === إدارة الأحداث ===\n    getEvents() {\n        return this.getItem(STORAGE_KEYS.EVENTS) || [];\n    }\n    saveEvents(events) {\n        this.setItem(STORAGE_KEYS.EVENTS, events);\n    }\n    addEvent(event) {\n        const events = this.getEvents();\n        events.push(event);\n        this.saveEvents(events);\n    }\n    // === إدارة الإشعارات ===\n    getNotifications() {\n        return this.getItem(STORAGE_KEYS.NOTIFICATIONS) || [];\n    }\n    saveNotifications(notifications) {\n        this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);\n    }\n    addNotification(notification) {\n        const notifications = this.getNotifications();\n        notifications.push(notification);\n        this.saveNotifications(notifications);\n    }\n    markNotificationAsRead(notificationId) {\n        const notifications = this.getNotifications();\n        const index = notifications.findIndex((n)=>n.id === notificationId);\n        if (index !== -1) {\n            notifications[index].isRead = true;\n            notifications[index].readAt = new Date();\n            this.saveNotifications(notifications);\n        }\n    }\n}\n// إنشاء مثيل واحد للاستخدام في التطبيق\nconst localStorageManager = new LocalStorageManager();\n// دوال مساعدة للبحث والتصفية\nconst searchUtils = {\n    searchStudents: (query, students)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return students.filter((student)=>student.name.toLowerCase().includes(lowercaseQuery) || student.studentId.toLowerCase().includes(lowercaseQuery) || student.email?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchTeachers: (query, teachers)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return teachers.filter((teacher)=>teacher.name?.toLowerCase().includes(lowercaseQuery) || teacher.fullName?.toLowerCase().includes(lowercaseQuery) || teacher.teacherId?.toLowerCase().includes(lowercaseQuery) || teacher.serialNumber?.toLowerCase().includes(lowercaseQuery) || teacher.email?.toLowerCase().includes(lowercaseQuery) || teacher.specialization?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchEmployees: (query, employees)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return employees.filter((employee)=>employee.name?.toLowerCase().includes(lowercaseQuery) || employee.fullName?.toLowerCase().includes(lowercaseQuery) || employee.employeeId?.toLowerCase().includes(lowercaseQuery) || employee.serialNumber?.toLowerCase().includes(lowercaseQuery) || employee.email?.toLowerCase().includes(lowercaseQuery) || employee.department?.toLowerCase().includes(lowercaseQuery) || employee.position?.toLowerCase().includes(lowercaseQuery));\n    },\n    filterStudentsByClass: (classId, students)=>{\n        return students.filter((student)=>student.classId === classId);\n    },\n    filterGradesByDateRange: (startDate, endDate, grades)=>{\n        return grades.filter((grade)=>{\n            const gradeDate = new Date(grade.examDate);\n            return gradeDate >= startDate && gradeDate <= endDate;\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvbG9jYWxTdG9yYWdlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBY0Esd0JBQXdCO0FBQ3hCLE1BQU1BLGVBQWU7SUFDbkJDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLFNBQVM7SUFDVEMsVUFBVTtJQUNWQyxRQUFRO0lBQ1JDLFlBQVk7SUFDWkMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFFBQVE7SUFDUkMsZUFBZTtJQUNmQyxjQUFjO0lBQ2RDLGVBQWU7SUFDZkMsa0JBQWtCO0FBQ3BCO0FBRUEsMkJBQTJCO0FBQzNCLE1BQU1DO0lBQ0osZUFBZTtJQUNQQyxRQUFXQyxHQUFXLEVBQUVDLElBQU8sRUFBUTtRQUM3QyxJQUFJO1lBQ0YsTUFBTUMsaUJBQWlCQyxLQUFLQyxTQUFTLENBQUNIO1lBQ3RDSSxhQUFhTixPQUFPLENBQUNDLEtBQUtFO1FBQzVCLEVBQUUsT0FBT0ksT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyw0QkFBNEIsRUFBRU4sSUFBSSxDQUFDLENBQUMsRUFBRU07UUFDdkQ7SUFDRjtJQUVBLG1CQUFtQjtJQUNYRSxRQUFXUixHQUFXLEVBQVk7UUFDeEMsSUFBSTtZQUNGLE1BQU1FLGlCQUFpQkcsYUFBYUcsT0FBTyxDQUFDUjtZQUM1QyxJQUFJRSxtQkFBbUIsTUFBTTtnQkFDM0IsT0FBTztZQUNUO1lBQ0EsT0FBT0MsS0FBS00sS0FBSyxDQUFDUDtRQUNwQixFQUFFLE9BQU9JLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsZ0NBQWdDLEVBQUVOLElBQUksQ0FBQyxDQUFDLEVBQUVNO1lBQ3pELE9BQU87UUFDVDtJQUNGO0lBRUEsZUFBZTtJQUNQSSxXQUFXVixHQUFXLEVBQVE7UUFDcEMsSUFBSTtZQUNGSyxhQUFhSyxVQUFVLENBQUNWO1FBQzFCLEVBQUUsT0FBT00sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyw0QkFBNEIsRUFBRU4sSUFBSSxDQUFDLENBQUMsRUFBRU07UUFDdkQ7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQkssV0FBaUI7UUFDZkMsT0FBT0MsTUFBTSxDQUFDOUIsY0FBYytCLE9BQU8sQ0FBQ2QsQ0FBQUE7WUFDbEMsSUFBSSxDQUFDVSxVQUFVLENBQUNWO1FBQ2xCO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkJlLGNBQXlCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDUCxPQUFPLENBQVl6QixhQUFhQyxRQUFRLEtBQUssRUFBRTtJQUM3RDtJQUVBZ0MsYUFBYUMsUUFBbUIsRUFBUTtRQUN0QyxJQUFJLENBQUNsQixPQUFPLENBQUNoQixhQUFhQyxRQUFRLEVBQUVpQztJQUN0QztJQUVBQyxXQUFXQyxPQUFnQixFQUFRO1FBQ2pDLE1BQU1GLFdBQVcsSUFBSSxDQUFDRixXQUFXO1FBQ2pDRSxTQUFTRyxJQUFJLENBQUNEO1FBQ2QsSUFBSSxDQUFDSCxZQUFZLENBQUNDO0lBQ3BCO0lBRUFJLGNBQWNDLFNBQWlCLEVBQUVDLGNBQWdDLEVBQVE7UUFDdkUsTUFBTU4sV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTVMsUUFBUVAsU0FBU1EsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtMO1FBQy9DLElBQUlFLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCUCxRQUFRLENBQUNPLE1BQU0sR0FBRztnQkFBRSxHQUFHUCxRQUFRLENBQUNPLE1BQU07Z0JBQUUsR0FBR0QsY0FBYztnQkFBRUssV0FBVyxJQUFJQztZQUFPO1lBQ2pGLElBQUksQ0FBQ2IsWUFBWSxDQUFDQztRQUNwQjtJQUNGO0lBRUFhLGNBQWNSLFNBQWlCLEVBQVE7UUFDckMsTUFBTUwsV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTWdCLG1CQUFtQmQsU0FBU2UsTUFBTSxDQUFDTixDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtMO1FBQ3ZELElBQUksQ0FBQ04sWUFBWSxDQUFDZTtJQUNwQjtJQUVBRSxlQUFlWCxTQUFpQixFQUFrQjtRQUNoRCxNQUFNTCxXQUFXLElBQUksQ0FBQ0YsV0FBVztRQUNqQyxPQUFPRSxTQUFTaUIsSUFBSSxDQUFDUixDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtMLGNBQWM7SUFDbkQ7SUFFQSx5QkFBeUI7SUFDekJhLGNBQXlCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDM0IsT0FBTyxDQUFZekIsYUFBYUUsUUFBUSxLQUFLLEVBQUU7SUFDN0Q7SUFFQW1ELGFBQWFDLFFBQW1CLEVBQVE7UUFDdEMsSUFBSSxDQUFDdEMsT0FBTyxDQUFDaEIsYUFBYUUsUUFBUSxFQUFFb0Q7SUFDdEM7SUFFQUMsV0FBV0MsT0FBZ0IsRUFBUTtRQUNqQyxNQUFNRixXQUFXLElBQUksQ0FBQ0YsV0FBVztRQUNqQ0UsU0FBU2pCLElBQUksQ0FBQ21CO1FBQ2QsSUFBSSxDQUFDSCxZQUFZLENBQUNDO0lBQ3BCO0lBRUFHLGNBQWNDLFNBQWlCLEVBQUVDLGNBQWdDLEVBQVE7UUFDdkUsTUFBTUwsV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTVgsUUFBUWEsU0FBU1osU0FBUyxDQUFDa0IsQ0FBQUEsSUFBS0EsRUFBRWhCLEVBQUUsS0FBS2M7UUFDL0MsSUFBSWpCLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCYSxRQUFRLENBQUNiLE1BQU0sR0FBRztnQkFBRSxHQUFHYSxRQUFRLENBQUNiLE1BQU07Z0JBQUUsR0FBR2tCLGNBQWM7Z0JBQUVkLFdBQVcsSUFBSUM7WUFBTztZQUNqRixJQUFJLENBQUNPLFlBQVksQ0FBQ0M7UUFDcEI7SUFDRjtJQUVBTyxjQUFjSCxTQUFpQixFQUFRO1FBQ3JDLE1BQU1KLFdBQVcsSUFBSSxDQUFDRixXQUFXO1FBQ2pDLE1BQU1VLG1CQUFtQlIsU0FBU0wsTUFBTSxDQUFDVyxDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLYztRQUN2RCxJQUFJLENBQUNMLFlBQVksQ0FBQ1M7SUFDcEI7SUFFQUMsZUFBZUwsU0FBaUIsRUFBa0I7UUFDaEQsTUFBTUosV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsT0FBT0UsU0FBU0gsSUFBSSxDQUFDUyxDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLYyxjQUFjO0lBQ25EO0lBRUEseUJBQXlCO0lBQ3pCTSxlQUEyQjtRQUN6QixPQUFPLElBQUksQ0FBQ3ZDLE9BQU8sQ0FBYXpCLGFBQWFHLFNBQVMsS0FBSyxFQUFFO0lBQy9EO0lBRUE4RCxjQUFjQyxTQUFxQixFQUFRO1FBQ3pDLElBQUksQ0FBQ2xELE9BQU8sQ0FBQ2hCLGFBQWFHLFNBQVMsRUFBRStEO0lBQ3ZDO0lBRUFDLFlBQVlDLFFBQWtCLEVBQVE7UUFDcEMsTUFBTUYsWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkNFLFVBQVU3QixJQUFJLENBQUMrQjtRQUNmLElBQUksQ0FBQ0gsYUFBYSxDQUFDQztJQUNyQjtJQUVBRyxlQUFlQyxVQUFrQixFQUFFQyxlQUFrQyxFQUFRO1FBQzNFLE1BQU1MLFlBQVksSUFBSSxDQUFDRixZQUFZO1FBQ25DLE1BQU12QixRQUFReUIsVUFBVXhCLFNBQVMsQ0FBQzhCLENBQUFBLElBQUtBLEVBQUU1QixFQUFFLEtBQUswQjtRQUNoRCxJQUFJN0IsVUFBVSxDQUFDLEdBQUc7WUFDaEJ5QixTQUFTLENBQUN6QixNQUFNLEdBQUc7Z0JBQUUsR0FBR3lCLFNBQVMsQ0FBQ3pCLE1BQU07Z0JBQUUsR0FBRzhCLGVBQWU7Z0JBQUUxQixXQUFXLElBQUlDO1lBQU87WUFDcEYsSUFBSSxDQUFDbUIsYUFBYSxDQUFDQztRQUNyQjtJQUNGO0lBRUFPLGVBQWVILFVBQWtCLEVBQVE7UUFDdkMsTUFBTUosWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkMsTUFBTVUsb0JBQW9CUixVQUFVakIsTUFBTSxDQUFDdUIsQ0FBQUEsSUFBS0EsRUFBRTVCLEVBQUUsS0FBSzBCO1FBQ3pELElBQUksQ0FBQ0wsYUFBYSxDQUFDUztJQUNyQjtJQUVBQyxnQkFBZ0JMLFVBQWtCLEVBQW1CO1FBQ25ELE1BQU1KLFlBQVksSUFBSSxDQUFDRixZQUFZO1FBQ25DLE9BQU9FLFVBQVVmLElBQUksQ0FBQ3FCLENBQUFBLElBQUtBLEVBQUU1QixFQUFFLEtBQUswQixlQUFlO0lBQ3JEO0lBRUEsdUJBQXVCO0lBQ3ZCTSxhQUFzQjtRQUNwQixPQUFPLElBQUksQ0FBQ25ELE9BQU8sQ0FBVXpCLGFBQWFJLE9BQU8sS0FBSyxFQUFFO0lBQzFEO0lBRUF5RSxZQUFZQyxPQUFnQixFQUFRO1FBQ2xDLElBQUksQ0FBQzlELE9BQU8sQ0FBQ2hCLGFBQWFJLE9BQU8sRUFBRTBFO0lBQ3JDO0lBRUFDLFNBQVNDLFNBQWdCLEVBQVE7UUFDL0IsTUFBTUYsVUFBVSxJQUFJLENBQUNGLFVBQVU7UUFDL0JFLFFBQVF6QyxJQUFJLENBQUMyQztRQUNiLElBQUksQ0FBQ0gsV0FBVyxDQUFDQztJQUNuQjtJQUVBRyxZQUFZQyxPQUFlLEVBQUVDLFlBQTRCLEVBQVE7UUFDL0QsTUFBTUwsVUFBVSxJQUFJLENBQUNGLFVBQVU7UUFDL0IsTUFBTW5DLFFBQVFxQyxRQUFRcEMsU0FBUyxDQUFDMEMsQ0FBQUEsSUFBS0EsRUFBRXhDLEVBQUUsS0FBS3NDO1FBQzlDLElBQUl6QyxVQUFVLENBQUMsR0FBRztZQUNoQnFDLE9BQU8sQ0FBQ3JDLE1BQU0sR0FBRztnQkFBRSxHQUFHcUMsT0FBTyxDQUFDckMsTUFBTTtnQkFBRSxHQUFHMEMsWUFBWTtnQkFBRXRDLFdBQVcsSUFBSUM7WUFBTztZQUM3RSxJQUFJLENBQUMrQixXQUFXLENBQUNDO1FBQ25CO0lBQ0Y7SUFFQU8sWUFBWUgsT0FBZSxFQUFRO1FBQ2pDLE1BQU1KLFVBQVUsSUFBSSxDQUFDRixVQUFVO1FBQy9CLE1BQU1VLGtCQUFrQlIsUUFBUTdCLE1BQU0sQ0FBQ21DLENBQUFBLElBQUtBLEVBQUV4QyxFQUFFLEtBQUtzQztRQUNyRCxJQUFJLENBQUNMLFdBQVcsQ0FBQ1M7SUFDbkI7SUFFQUMsYUFBYUwsT0FBZSxFQUFnQjtRQUMxQyxNQUFNSixVQUFVLElBQUksQ0FBQ0YsVUFBVTtRQUMvQixPQUFPRSxRQUFRM0IsSUFBSSxDQUFDaUMsQ0FBQUEsSUFBS0EsRUFBRXhDLEVBQUUsS0FBS3NDLFlBQVk7SUFDaEQ7SUFFQSx1QkFBdUI7SUFDdkJNLGNBQXlCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDL0QsT0FBTyxDQUFZekIsYUFBYUssUUFBUSxLQUFLLEVBQUU7SUFDN0Q7SUFFQW9GLGFBQWFDLFFBQW1CLEVBQVE7UUFDdEMsSUFBSSxDQUFDMUUsT0FBTyxDQUFDaEIsYUFBYUssUUFBUSxFQUFFcUY7SUFDdEM7SUFFQUMsV0FBV0MsT0FBZ0IsRUFBUTtRQUNqQyxNQUFNRixXQUFXLElBQUksQ0FBQ0YsV0FBVztRQUNqQ0UsU0FBU3JELElBQUksQ0FBQ3VEO1FBQ2QsSUFBSSxDQUFDSCxZQUFZLENBQUNDO0lBQ3BCO0lBRUFHLGNBQWNDLFNBQWlCLEVBQUVDLGNBQWdDLEVBQVE7UUFDdkUsTUFBTUwsV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTS9DLFFBQVFpRCxTQUFTaEQsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtrRDtRQUMvQyxJQUFJckQsVUFBVSxDQUFDLEdBQUc7WUFDaEJpRCxRQUFRLENBQUNqRCxNQUFNLEdBQUc7Z0JBQUUsR0FBR2lELFFBQVEsQ0FBQ2pELE1BQU07Z0JBQUUsR0FBR3NELGNBQWM7Z0JBQUVsRCxXQUFXLElBQUlDO1lBQU87WUFDakYsSUFBSSxDQUFDMkMsWUFBWSxDQUFDQztRQUNwQjtJQUNGO0lBRUFNLGNBQWNGLFNBQWlCLEVBQVE7UUFDckMsTUFBTUosV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsTUFBTVMsbUJBQW1CUCxTQUFTekMsTUFBTSxDQUFDTixDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtrRDtRQUN2RCxJQUFJLENBQUNMLFlBQVksQ0FBQ1E7SUFDcEI7SUFFQUMsZUFBZUosU0FBaUIsRUFBa0I7UUFDaEQsTUFBTUosV0FBVyxJQUFJLENBQUNGLFdBQVc7UUFDakMsT0FBT0UsU0FBU3ZDLElBQUksQ0FBQ1IsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLa0QsY0FBYztJQUNuRDtJQUVBLHdCQUF3QjtJQUN4QkssWUFBcUI7UUFDbkIsT0FBTyxJQUFJLENBQUMxRSxPQUFPLENBQVV6QixhQUFhTSxNQUFNLEtBQUssRUFBRTtJQUN6RDtJQUVBOEYsV0FBV0MsTUFBZSxFQUFRO1FBQ2hDLElBQUksQ0FBQ3JGLE9BQU8sQ0FBQ2hCLGFBQWFNLE1BQU0sRUFBRStGO0lBQ3BDO0lBRUFDLFNBQVNDLEtBQVksRUFBUTtRQUMzQixNQUFNRixTQUFTLElBQUksQ0FBQ0YsU0FBUztRQUM3QkUsT0FBT2hFLElBQUksQ0FBQ2tFO1FBQ1osSUFBSSxDQUFDSCxVQUFVLENBQUNDO0lBQ2xCO0lBRUFHLFlBQVlDLE9BQWUsRUFBRUMsWUFBNEIsRUFBUTtRQUMvRCxNQUFNTCxTQUFTLElBQUksQ0FBQ0YsU0FBUztRQUM3QixNQUFNMUQsUUFBUTRELE9BQU8zRCxTQUFTLENBQUNpRSxDQUFBQSxJQUFLQSxFQUFFL0QsRUFBRSxLQUFLNkQ7UUFDN0MsSUFBSWhFLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCNEQsTUFBTSxDQUFDNUQsTUFBTSxHQUFHO2dCQUFFLEdBQUc0RCxNQUFNLENBQUM1RCxNQUFNO2dCQUFFLEdBQUdpRSxZQUFZO2dCQUFFN0QsV0FBVyxJQUFJQztZQUFPO1lBQzNFLElBQUksQ0FBQ3NELFVBQVUsQ0FBQ0M7UUFDbEI7SUFDRjtJQUVBTyxZQUFZSCxPQUFlLEVBQVE7UUFDakMsTUFBTUosU0FBUyxJQUFJLENBQUNGLFNBQVM7UUFDN0IsTUFBTVUsaUJBQWlCUixPQUFPcEQsTUFBTSxDQUFDMEQsQ0FBQUEsSUFBS0EsRUFBRS9ELEVBQUUsS0FBSzZEO1FBQ25ELElBQUksQ0FBQ0wsVUFBVSxDQUFDUztJQUNsQjtJQUVBQyxtQkFBbUJ2RSxTQUFpQixFQUFXO1FBQzdDLE1BQU04RCxTQUFTLElBQUksQ0FBQ0YsU0FBUztRQUM3QixPQUFPRSxPQUFPcEQsTUFBTSxDQUFDMEQsQ0FBQUEsSUFBS0EsRUFBRXBFLFNBQVMsS0FBS0E7SUFDNUM7SUFFQXdFLGlCQUFpQjdCLE9BQWUsRUFBVztRQUN6QyxNQUFNbUIsU0FBUyxJQUFJLENBQUNGLFNBQVM7UUFDN0IsT0FBT0UsT0FBT3BELE1BQU0sQ0FBQzBELENBQUFBLElBQUtBLEVBQUV6QixPQUFPLEtBQUtBO0lBQzFDO0lBRUEsdUJBQXVCO0lBQ3ZCOEIsZ0JBQThCO1FBQzVCLE9BQU8sSUFBSSxDQUFDdkYsT0FBTyxDQUFlekIsYUFBYU8sVUFBVSxLQUFLLEVBQUU7SUFDbEU7SUFFQTBHLGVBQWVDLFVBQXdCLEVBQVE7UUFDN0MsSUFBSSxDQUFDbEcsT0FBTyxDQUFDaEIsYUFBYU8sVUFBVSxFQUFFMkc7SUFDeEM7SUFFQUMsY0FBY0QsVUFBc0IsRUFBUTtRQUMxQyxNQUFNRSxvQkFBb0IsSUFBSSxDQUFDSixhQUFhO1FBQzVDSSxrQkFBa0IvRSxJQUFJLENBQUM2RTtRQUN2QixJQUFJLENBQUNELGNBQWMsQ0FBQ0c7SUFDdEI7SUFFQUMsaUJBQWlCQyxZQUFvQixFQUFFQyxpQkFBc0MsRUFBUTtRQUNuRixNQUFNSCxvQkFBb0IsSUFBSSxDQUFDSixhQUFhO1FBQzVDLE1BQU12RSxRQUFRMkUsa0JBQWtCMUUsU0FBUyxDQUFDOEUsQ0FBQUEsSUFBS0EsRUFBRTVFLEVBQUUsS0FBSzBFO1FBQ3hELElBQUk3RSxVQUFVLENBQUMsR0FBRztZQUNoQjJFLGlCQUFpQixDQUFDM0UsTUFBTSxHQUFHO2dCQUFFLEdBQUcyRSxpQkFBaUIsQ0FBQzNFLE1BQU07Z0JBQUUsR0FBRzhFLGlCQUFpQjtnQkFBRTFFLFdBQVcsSUFBSUM7WUFBTztZQUN0RyxJQUFJLENBQUNtRSxjQUFjLENBQUNHO1FBQ3RCO0lBQ0Y7SUFFQUssdUJBQXVCbEYsU0FBaUIsRUFBZ0I7UUFDdEQsTUFBTTJFLGFBQWEsSUFBSSxDQUFDRixhQUFhO1FBQ3JDLE9BQU9FLFdBQVdqRSxNQUFNLENBQUN1RSxDQUFBQSxJQUFLQSxFQUFFakYsU0FBUyxLQUFLQTtJQUNoRDtJQUVBbUYsb0JBQW9CQyxJQUFVLEVBQWdCO1FBQzVDLE1BQU1ULGFBQWEsSUFBSSxDQUFDRixhQUFhO1FBQ3JDLE1BQU1ZLGFBQWFELEtBQUtFLFlBQVk7UUFDcEMsT0FBT1gsV0FBV2pFLE1BQU0sQ0FBQ3VFLENBQUFBLElBQUssSUFBSTFFLEtBQUswRSxFQUFFRyxJQUFJLEVBQUVFLFlBQVksT0FBT0Q7SUFDcEU7SUFFQSwyQkFBMkI7SUFDM0JFLFdBQW1CO1FBQ2pCLE9BQU8sSUFBSSxDQUFDckcsT0FBTyxDQUFTekIsYUFBYVEsS0FBSyxLQUFLLEVBQUU7SUFDdkQ7SUFFQXVILFVBQVVDLEtBQWEsRUFBUTtRQUM3QixJQUFJLENBQUNoSCxPQUFPLENBQUNoQixhQUFhUSxLQUFLLEVBQUV3SDtJQUNuQztJQUVBQyxpQkFBOEI7UUFDNUIsT0FBTyxJQUFJLENBQUN4RyxPQUFPLENBQU96QixhQUFhWSxZQUFZO0lBQ3JEO0lBRUFzSCxlQUFlQyxJQUFVLEVBQVE7UUFDL0IsSUFBSSxDQUFDbkgsT0FBTyxDQUFDaEIsYUFBYVksWUFBWSxFQUFFdUg7SUFDMUM7SUFFQUMsU0FBZTtRQUNiLElBQUksQ0FBQ3pHLFVBQVUsQ0FBQzNCLGFBQWFZLFlBQVk7SUFDM0M7SUFFQSwwQkFBMEI7SUFDMUJ5SCxjQUErQjtRQUM3QixPQUFPLElBQUksQ0FBQzVHLE9BQU8sQ0FBV3pCLGFBQWFTLFFBQVE7SUFDckQ7SUFFQTZILGFBQWFDLFFBQWtCLEVBQVE7UUFDckMsSUFBSSxDQUFDdkgsT0FBTyxDQUFDaEIsYUFBYVMsUUFBUSxFQUFFOEg7SUFDdEM7SUFFQSx3QkFBd0I7SUFDeEJDLFlBQXFCO1FBQ25CLE9BQU8sSUFBSSxDQUFDL0csT0FBTyxDQUFVekIsYUFBYVUsTUFBTSxLQUFLLEVBQUU7SUFDekQ7SUFFQStILFdBQVdDLE1BQWUsRUFBUTtRQUNoQyxJQUFJLENBQUMxSCxPQUFPLENBQUNoQixhQUFhVSxNQUFNLEVBQUVnSTtJQUNwQztJQUVBQyxTQUFTQyxLQUFZLEVBQVE7UUFDM0IsTUFBTUYsU0FBUyxJQUFJLENBQUNGLFNBQVM7UUFDN0JFLE9BQU9yRyxJQUFJLENBQUN1RztRQUNaLElBQUksQ0FBQ0gsVUFBVSxDQUFDQztJQUNsQjtJQUVBLDBCQUEwQjtJQUMxQkcsbUJBQW1DO1FBQ2pDLE9BQU8sSUFBSSxDQUFDcEgsT0FBTyxDQUFpQnpCLGFBQWFXLGFBQWEsS0FBSyxFQUFFO0lBQ3ZFO0lBRUFtSSxrQkFBa0JDLGFBQTZCLEVBQVE7UUFDckQsSUFBSSxDQUFDL0gsT0FBTyxDQUFDaEIsYUFBYVcsYUFBYSxFQUFFb0k7SUFDM0M7SUFFQUMsZ0JBQWdCQyxZQUEwQixFQUFRO1FBQ2hELE1BQU1GLGdCQUFnQixJQUFJLENBQUNGLGdCQUFnQjtRQUMzQ0UsY0FBYzFHLElBQUksQ0FBQzRHO1FBQ25CLElBQUksQ0FBQ0gsaUJBQWlCLENBQUNDO0lBQ3pCO0lBRUFHLHVCQUF1QkMsY0FBc0IsRUFBUTtRQUNuRCxNQUFNSixnQkFBZ0IsSUFBSSxDQUFDRixnQkFBZ0I7UUFDM0MsTUFBTXBHLFFBQVFzRyxjQUFjckcsU0FBUyxDQUFDMEcsQ0FBQUEsSUFBS0EsRUFBRXhHLEVBQUUsS0FBS3VHO1FBQ3BELElBQUkxRyxVQUFVLENBQUMsR0FBRztZQUNoQnNHLGFBQWEsQ0FBQ3RHLE1BQU0sQ0FBQzRHLE1BQU0sR0FBRztZQUM5Qk4sYUFBYSxDQUFDdEcsTUFBTSxDQUFDNkcsTUFBTSxHQUFHLElBQUl4RztZQUNsQyxJQUFJLENBQUNnRyxpQkFBaUIsQ0FBQ0M7UUFDekI7SUFDRjtBQUNGO0FBRUEsdUNBQXVDO0FBQ2hDLE1BQU1RLHNCQUFzQixJQUFJeEksc0JBQXNCO0FBRTdELDZCQUE2QjtBQUN0QixNQUFNeUksY0FBYztJQUN6QkMsZ0JBQWdCLENBQUNDLE9BQWV4SDtRQUM5QixNQUFNeUgsaUJBQWlCRCxNQUFNRSxXQUFXO1FBQ3hDLE9BQU8xSCxTQUFTZSxNQUFNLENBQUNiLENBQUFBLFVBQ3JCQSxRQUFReUgsSUFBSSxDQUFDRCxXQUFXLEdBQUdFLFFBQVEsQ0FBQ0gsbUJBQ3BDdkgsUUFBUUcsU0FBUyxDQUFDcUgsV0FBVyxHQUFHRSxRQUFRLENBQUNILG1CQUN6Q3ZILFFBQVEySCxLQUFLLEVBQUVILGNBQWNFLFNBQVNIO0lBRTFDO0lBRUFLLGdCQUFnQixDQUFDTixPQUFlcEc7UUFDOUIsTUFBTXFHLGlCQUFpQkQsTUFBTUUsV0FBVztRQUN4QyxPQUFPdEcsU0FBU0wsTUFBTSxDQUFDTyxDQUFBQSxVQUNyQkEsUUFBUXFHLElBQUksRUFBRUQsY0FBY0UsU0FBU0gsbUJBQ3JDbkcsUUFBUXlHLFFBQVEsRUFBRUwsY0FBY0UsU0FBU0gsbUJBQ3pDbkcsUUFBUUUsU0FBUyxFQUFFa0csY0FBY0UsU0FBU0gsbUJBQzFDbkcsUUFBUTBHLFlBQVksRUFBRU4sY0FBY0UsU0FBU0gsbUJBQzdDbkcsUUFBUXVHLEtBQUssRUFBRUgsY0FBY0UsU0FBU0gsbUJBQ3RDbkcsUUFBUTJHLGNBQWMsRUFBRVAsY0FBY0UsU0FBU0g7SUFFbkQ7SUFFQVMsaUJBQWlCLENBQUNWLE9BQWV4RjtRQUMvQixNQUFNeUYsaUJBQWlCRCxNQUFNRSxXQUFXO1FBQ3hDLE9BQU8xRixVQUFVakIsTUFBTSxDQUFDbUIsQ0FBQUEsV0FDdEJBLFNBQVN5RixJQUFJLEVBQUVELGNBQWNFLFNBQVNILG1CQUN0Q3ZGLFNBQVM2RixRQUFRLEVBQUVMLGNBQWNFLFNBQVNILG1CQUMxQ3ZGLFNBQVNFLFVBQVUsRUFBRXNGLGNBQWNFLFNBQVNILG1CQUM1Q3ZGLFNBQVM4RixZQUFZLEVBQUVOLGNBQWNFLFNBQVNILG1CQUM5Q3ZGLFNBQVMyRixLQUFLLEVBQUVILGNBQWNFLFNBQVNILG1CQUN2Q3ZGLFNBQVNpRyxVQUFVLEVBQUVULGNBQWNFLFNBQVNILG1CQUM1Q3ZGLFNBQVNrRyxRQUFRLEVBQUVWLGNBQWNFLFNBQVNIO0lBRTlDO0lBRUFZLHVCQUF1QixDQUFDckYsU0FBaUJoRDtRQUN2QyxPQUFPQSxTQUFTZSxNQUFNLENBQUNiLENBQUFBLFVBQVdBLFFBQVE4QyxPQUFPLEtBQUtBO0lBQ3hEO0lBRUFzRix5QkFBeUIsQ0FBQ0MsV0FBaUJDLFNBQWVyRTtRQUN4RCxPQUFPQSxPQUFPcEQsTUFBTSxDQUFDc0QsQ0FBQUE7WUFDbkIsTUFBTW9FLFlBQVksSUFBSTdILEtBQUt5RCxNQUFNcUUsUUFBUTtZQUN6QyxPQUFPRCxhQUFhRixhQUFhRSxhQUFhRDtRQUNoRDtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxzcmNcXHV0aWxzXFxsb2NhbFN0b3JhZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgU3R1ZGVudCxcbiAgVGVhY2hlcixcbiAgRW1wbG95ZWUsXG4gIENsYXNzLFxuICBTdWJqZWN0LFxuICBHcmFkZSxcbiAgQXR0ZW5kYW5jZSxcbiAgVXNlcixcbiAgU2V0dGluZ3MsXG4gIEV2ZW50LFxuICBOb3RpZmljYXRpb25cbn0gZnJvbSAnQC90eXBlcyc7XG5cbi8vINmF2YHYp9iq2YrYrSDYp9mE2KrYrtiy2YrZhiDYp9mE2YXYrdmE2YpcbmNvbnN0IFNUT1JBR0VfS0VZUyA9IHtcbiAgU1RVREVOVFM6ICdzY2hvb2xfc3R1ZGVudHMnLFxuICBURUFDSEVSUzogJ3NjaG9vbF90ZWFjaGVycycsXG4gIEVNUExPWUVFUzogJ3NjaG9vbF9lbXBsb3llZXMnLFxuICBDTEFTU0VTOiAnc2Nob29sX2NsYXNzZXMnLFxuICBTVUJKRUNUUzogJ3NjaG9vbF9zdWJqZWN0cycsXG4gIEdSQURFUzogJ3NjaG9vbF9ncmFkZXMnLFxuICBBVFRFTkRBTkNFOiAnc2Nob29sX2F0dGVuZGFuY2UnLFxuICBVU0VSUzogJ3NjaG9vbF91c2VycycsXG4gIFNFVFRJTkdTOiAnc2Nob29sX3NldHRpbmdzJyxcbiAgRVZFTlRTOiAnc2Nob29sX2V2ZW50cycsXG4gIE5PVElGSUNBVElPTlM6ICdzY2hvb2xfbm90aWZpY2F0aW9ucycsXG4gIENVUlJFTlRfVVNFUjogJ3NjaG9vbF9jdXJyZW50X3VzZXInLFxuICBBQ0FERU1JQ19ZRUFSOiAnc2Nob29sX2FjYWRlbWljX3llYXInLFxuICBDVVJSRU5UX1NFTUVTVEVSOiAnc2Nob29sX2N1cnJlbnRfc2VtZXN0ZXInXG59O1xuXG4vLyDZgdim2Kkg2KXYr9in2LHYqSDYp9mE2KrYrtiy2YrZhiDYp9mE2YXYrdmE2YpcbmNsYXNzIExvY2FsU3RvcmFnZU1hbmFnZXIge1xuICAvLyDYrdmB2Lgg2KfZhNio2YrYp9mG2KfYqlxuICBwcml2YXRlIHNldEl0ZW08VD4oa2V5OiBzdHJpbmcsIGRhdGE6IFQpOiB2b2lkIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc2VyaWFsaXplZERhdGEgPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGtleSwgc2VyaWFsaXplZERhdGEpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGDYrti32KMg2YHZiiDYrdmB2Lgg2KfZhNio2YrYp9mG2KfYqiDZhNmE2YXZgdiq2KfYrSAke2tleX06YCwgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIC8vINin2LPYqtix2KzYp9i5INin2YTYqNmK2KfZhtin2KpcbiAgcHJpdmF0ZSBnZXRJdGVtPFQ+KGtleTogc3RyaW5nKTogVCB8IG51bGwge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXJpYWxpemVkRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgICBpZiAoc2VyaWFsaXplZERhdGEgPT09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShzZXJpYWxpemVkRGF0YSkgYXMgVDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihg2K7Yt9ijINmB2Yog2KfYs9iq2LHYrNin2Lkg2KfZhNio2YrYp9mG2KfYqiDZhNmE2YXZgdiq2KfYrSAke2tleX06YCwgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgLy8g2K3YsNmBINin2YTYqNmK2KfZhtin2KpcbiAgcHJpdmF0ZSByZW1vdmVJdGVtKGtleTogc3RyaW5nKTogdm9pZCB7XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYNiu2LfYoyDZgdmKINit2LDZgSDYp9mE2KjZitin2YbYp9iqINmE2YTZhdmB2KrYp9itICR7a2V5fTpgLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLy8g2YXYs9itINis2YXZiti5INin2YTYqNmK2KfZhtin2KpcbiAgY2xlYXJBbGwoKTogdm9pZCB7XG4gICAgT2JqZWN0LnZhbHVlcyhTVE9SQUdFX0tFWVMpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIHRoaXMucmVtb3ZlSXRlbShrZXkpO1xuICAgIH0pO1xuICB9XG5cbiAgLy8gPT09INil2K/Yp9ix2Kkg2KfZhNi32YTYp9ioID09PVxuICBnZXRTdHVkZW50cygpOiBTdHVkZW50W10ge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08U3R1ZGVudFtdPihTVE9SQUdFX0tFWVMuU1RVREVOVFMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZVN0dWRlbnRzKHN0dWRlbnRzOiBTdHVkZW50W10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLlNUVURFTlRTLCBzdHVkZW50cyk7XG4gIH1cblxuICBhZGRTdHVkZW50KHN0dWRlbnQ6IFN0dWRlbnQpOiB2b2lkIHtcbiAgICBjb25zdCBzdHVkZW50cyA9IHRoaXMuZ2V0U3R1ZGVudHMoKTtcbiAgICBzdHVkZW50cy5wdXNoKHN0dWRlbnQpO1xuICAgIHRoaXMuc2F2ZVN0dWRlbnRzKHN0dWRlbnRzKTtcbiAgfVxuXG4gIHVwZGF0ZVN0dWRlbnQoc3R1ZGVudElkOiBzdHJpbmcsIHVwZGF0ZWRTdHVkZW50OiBQYXJ0aWFsPFN0dWRlbnQ+KTogdm9pZCB7XG4gICAgY29uc3Qgc3R1ZGVudHMgPSB0aGlzLmdldFN0dWRlbnRzKCk7XG4gICAgY29uc3QgaW5kZXggPSBzdHVkZW50cy5maW5kSW5kZXgocyA9PiBzLmlkID09PSBzdHVkZW50SWQpO1xuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHN0dWRlbnRzW2luZGV4XSA9IHsgLi4uc3R1ZGVudHNbaW5kZXhdLCAuLi51cGRhdGVkU3R1ZGVudCwgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpIH07XG4gICAgICB0aGlzLnNhdmVTdHVkZW50cyhzdHVkZW50cyk7XG4gICAgfVxuICB9XG5cbiAgZGVsZXRlU3R1ZGVudChzdHVkZW50SWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHN0dWRlbnRzID0gdGhpcy5nZXRTdHVkZW50cygpO1xuICAgIGNvbnN0IGZpbHRlcmVkU3R1ZGVudHMgPSBzdHVkZW50cy5maWx0ZXIocyA9PiBzLmlkICE9PSBzdHVkZW50SWQpO1xuICAgIHRoaXMuc2F2ZVN0dWRlbnRzKGZpbHRlcmVkU3R1ZGVudHMpO1xuICB9XG5cbiAgZ2V0U3R1ZGVudEJ5SWQoc3R1ZGVudElkOiBzdHJpbmcpOiBTdHVkZW50IHwgbnVsbCB7XG4gICAgY29uc3Qgc3R1ZGVudHMgPSB0aGlzLmdldFN0dWRlbnRzKCk7XG4gICAgcmV0dXJuIHN0dWRlbnRzLmZpbmQocyA9PiBzLmlkID09PSBzdHVkZW50SWQpIHx8IG51bGw7XG4gIH1cblxuICAvLyA9PT0g2KXYr9in2LHYqSDYp9mE2YXYudmE2YXZitmGID09PVxuICBnZXRUZWFjaGVycygpOiBUZWFjaGVyW10ge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08VGVhY2hlcltdPihTVE9SQUdFX0tFWVMuVEVBQ0hFUlMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZVRlYWNoZXJzKHRlYWNoZXJzOiBUZWFjaGVyW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLlRFQUNIRVJTLCB0ZWFjaGVycyk7XG4gIH1cblxuICBhZGRUZWFjaGVyKHRlYWNoZXI6IFRlYWNoZXIpOiB2b2lkIHtcbiAgICBjb25zdCB0ZWFjaGVycyA9IHRoaXMuZ2V0VGVhY2hlcnMoKTtcbiAgICB0ZWFjaGVycy5wdXNoKHRlYWNoZXIpO1xuICAgIHRoaXMuc2F2ZVRlYWNoZXJzKHRlYWNoZXJzKTtcbiAgfVxuXG4gIHVwZGF0ZVRlYWNoZXIodGVhY2hlcklkOiBzdHJpbmcsIHVwZGF0ZWRUZWFjaGVyOiBQYXJ0aWFsPFRlYWNoZXI+KTogdm9pZCB7XG4gICAgY29uc3QgdGVhY2hlcnMgPSB0aGlzLmdldFRlYWNoZXJzKCk7XG4gICAgY29uc3QgaW5kZXggPSB0ZWFjaGVycy5maW5kSW5kZXgodCA9PiB0LmlkID09PSB0ZWFjaGVySWQpO1xuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHRlYWNoZXJzW2luZGV4XSA9IHsgLi4udGVhY2hlcnNbaW5kZXhdLCAuLi51cGRhdGVkVGVhY2hlciwgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpIH07XG4gICAgICB0aGlzLnNhdmVUZWFjaGVycyh0ZWFjaGVycyk7XG4gICAgfVxuICB9XG5cbiAgZGVsZXRlVGVhY2hlcih0ZWFjaGVySWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHRlYWNoZXJzID0gdGhpcy5nZXRUZWFjaGVycygpO1xuICAgIGNvbnN0IGZpbHRlcmVkVGVhY2hlcnMgPSB0ZWFjaGVycy5maWx0ZXIodCA9PiB0LmlkICE9PSB0ZWFjaGVySWQpO1xuICAgIHRoaXMuc2F2ZVRlYWNoZXJzKGZpbHRlcmVkVGVhY2hlcnMpO1xuICB9XG5cbiAgZ2V0VGVhY2hlckJ5SWQodGVhY2hlcklkOiBzdHJpbmcpOiBUZWFjaGVyIHwgbnVsbCB7XG4gICAgY29uc3QgdGVhY2hlcnMgPSB0aGlzLmdldFRlYWNoZXJzKCk7XG4gICAgcmV0dXJuIHRlYWNoZXJzLmZpbmQodCA9PiB0LmlkID09PSB0ZWFjaGVySWQpIHx8IG51bGw7XG4gIH1cblxuICAvLyA9PT0g2KXYr9in2LHYqSDYp9mE2YXZiNi42YHZitmGID09PVxuICBnZXRFbXBsb3llZXMoKTogRW1wbG95ZWVbXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0SXRlbTxFbXBsb3llZVtdPihTVE9SQUdFX0tFWVMuRU1QTE9ZRUVTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVFbXBsb3llZXMoZW1wbG95ZWVzOiBFbXBsb3llZVtdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5FTVBMT1lFRVMsIGVtcGxveWVlcyk7XG4gIH1cblxuICBhZGRFbXBsb3llZShlbXBsb3llZTogRW1wbG95ZWUpOiB2b2lkIHtcbiAgICBjb25zdCBlbXBsb3llZXMgPSB0aGlzLmdldEVtcGxveWVlcygpO1xuICAgIGVtcGxveWVlcy5wdXNoKGVtcGxveWVlKTtcbiAgICB0aGlzLnNhdmVFbXBsb3llZXMoZW1wbG95ZWVzKTtcbiAgfVxuXG4gIHVwZGF0ZUVtcGxveWVlKGVtcGxveWVlSWQ6IHN0cmluZywgdXBkYXRlZEVtcGxveWVlOiBQYXJ0aWFsPEVtcGxveWVlPik6IHZvaWQge1xuICAgIGNvbnN0IGVtcGxveWVlcyA9IHRoaXMuZ2V0RW1wbG95ZWVzKCk7XG4gICAgY29uc3QgaW5kZXggPSBlbXBsb3llZXMuZmluZEluZGV4KGUgPT4gZS5pZCA9PT0gZW1wbG95ZWVJZCk7XG4gICAgaWYgKGluZGV4ICE9PSAtMSkge1xuICAgICAgZW1wbG95ZWVzW2luZGV4XSA9IHsgLi4uZW1wbG95ZWVzW2luZGV4XSwgLi4udXBkYXRlZEVtcGxveWVlLCB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkgfTtcbiAgICAgIHRoaXMuc2F2ZUVtcGxveWVlcyhlbXBsb3llZXMpO1xuICAgIH1cbiAgfVxuXG4gIGRlbGV0ZUVtcGxveWVlKGVtcGxveWVlSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IGVtcGxveWVlcyA9IHRoaXMuZ2V0RW1wbG95ZWVzKCk7XG4gICAgY29uc3QgZmlsdGVyZWRFbXBsb3llZXMgPSBlbXBsb3llZXMuZmlsdGVyKGUgPT4gZS5pZCAhPT0gZW1wbG95ZWVJZCk7XG4gICAgdGhpcy5zYXZlRW1wbG95ZWVzKGZpbHRlcmVkRW1wbG95ZWVzKTtcbiAgfVxuXG4gIGdldEVtcGxveWVlQnlJZChlbXBsb3llZUlkOiBzdHJpbmcpOiBFbXBsb3llZSB8IG51bGwge1xuICAgIGNvbnN0IGVtcGxveWVlcyA9IHRoaXMuZ2V0RW1wbG95ZWVzKCk7XG4gICAgcmV0dXJuIGVtcGxveWVlcy5maW5kKGUgPT4gZS5pZCA9PT0gZW1wbG95ZWVJZCkgfHwgbnVsbDtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYtdmB2YjZgSA9PT1cbiAgZ2V0Q2xhc3NlcygpOiBDbGFzc1tdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPENsYXNzW10+KFNUT1JBR0VfS0VZUy5DTEFTU0VTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVDbGFzc2VzKGNsYXNzZXM6IENsYXNzW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkNMQVNTRVMsIGNsYXNzZXMpO1xuICB9XG5cbiAgYWRkQ2xhc3MoY2xhc3NEYXRhOiBDbGFzcyk6IHZvaWQge1xuICAgIGNvbnN0IGNsYXNzZXMgPSB0aGlzLmdldENsYXNzZXMoKTtcbiAgICBjbGFzc2VzLnB1c2goY2xhc3NEYXRhKTtcbiAgICB0aGlzLnNhdmVDbGFzc2VzKGNsYXNzZXMpO1xuICB9XG5cbiAgdXBkYXRlQ2xhc3MoY2xhc3NJZDogc3RyaW5nLCB1cGRhdGVkQ2xhc3M6IFBhcnRpYWw8Q2xhc3M+KTogdm9pZCB7XG4gICAgY29uc3QgY2xhc3NlcyA9IHRoaXMuZ2V0Q2xhc3NlcygpO1xuICAgIGNvbnN0IGluZGV4ID0gY2xhc3Nlcy5maW5kSW5kZXgoYyA9PiBjLmlkID09PSBjbGFzc0lkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBjbGFzc2VzW2luZGV4XSA9IHsgLi4uY2xhc3Nlc1tpbmRleF0sIC4uLnVwZGF0ZWRDbGFzcywgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpIH07XG4gICAgICB0aGlzLnNhdmVDbGFzc2VzKGNsYXNzZXMpO1xuICAgIH1cbiAgfVxuXG4gIGRlbGV0ZUNsYXNzKGNsYXNzSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IGNsYXNzZXMgPSB0aGlzLmdldENsYXNzZXMoKTtcbiAgICBjb25zdCBmaWx0ZXJlZENsYXNzZXMgPSBjbGFzc2VzLmZpbHRlcihjID0+IGMuaWQgIT09IGNsYXNzSWQpO1xuICAgIHRoaXMuc2F2ZUNsYXNzZXMoZmlsdGVyZWRDbGFzc2VzKTtcbiAgfVxuXG4gIGdldENsYXNzQnlJZChjbGFzc0lkOiBzdHJpbmcpOiBDbGFzcyB8IG51bGwge1xuICAgIGNvbnN0IGNsYXNzZXMgPSB0aGlzLmdldENsYXNzZXMoKTtcbiAgICByZXR1cm4gY2xhc3Nlcy5maW5kKGMgPT4gYy5pZCA9PT0gY2xhc3NJZCkgfHwgbnVsbDtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTZhdmI2KfYryA9PT1cbiAgZ2V0U3ViamVjdHMoKTogU3ViamVjdFtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPFN1YmplY3RbXT4oU1RPUkFHRV9LRVlTLlNVQkpFQ1RTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVTdWJqZWN0cyhzdWJqZWN0czogU3ViamVjdFtdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5TVUJKRUNUUywgc3ViamVjdHMpO1xuICB9XG5cbiAgYWRkU3ViamVjdChzdWJqZWN0OiBTdWJqZWN0KTogdm9pZCB7XG4gICAgY29uc3Qgc3ViamVjdHMgPSB0aGlzLmdldFN1YmplY3RzKCk7XG4gICAgc3ViamVjdHMucHVzaChzdWJqZWN0KTtcbiAgICB0aGlzLnNhdmVTdWJqZWN0cyhzdWJqZWN0cyk7XG4gIH1cblxuICB1cGRhdGVTdWJqZWN0KHN1YmplY3RJZDogc3RyaW5nLCB1cGRhdGVkU3ViamVjdDogUGFydGlhbDxTdWJqZWN0Pik6IHZvaWQge1xuICAgIGNvbnN0IHN1YmplY3RzID0gdGhpcy5nZXRTdWJqZWN0cygpO1xuICAgIGNvbnN0IGluZGV4ID0gc3ViamVjdHMuZmluZEluZGV4KHMgPT4gcy5pZCA9PT0gc3ViamVjdElkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBzdWJqZWN0c1tpbmRleF0gPSB7IC4uLnN1YmplY3RzW2luZGV4XSwgLi4udXBkYXRlZFN1YmplY3QsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9O1xuICAgICAgdGhpcy5zYXZlU3ViamVjdHMoc3ViamVjdHMpO1xuICAgIH1cbiAgfVxuXG4gIGRlbGV0ZVN1YmplY3Qoc3ViamVjdElkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBzdWJqZWN0cyA9IHRoaXMuZ2V0U3ViamVjdHMoKTtcbiAgICBjb25zdCBmaWx0ZXJlZFN1YmplY3RzID0gc3ViamVjdHMuZmlsdGVyKHMgPT4gcy5pZCAhPT0gc3ViamVjdElkKTtcbiAgICB0aGlzLnNhdmVTdWJqZWN0cyhmaWx0ZXJlZFN1YmplY3RzKTtcbiAgfVxuXG4gIGdldFN1YmplY3RCeUlkKHN1YmplY3RJZDogc3RyaW5nKTogU3ViamVjdCB8IG51bGwge1xuICAgIGNvbnN0IHN1YmplY3RzID0gdGhpcy5nZXRTdWJqZWN0cygpO1xuICAgIHJldHVybiBzdWJqZWN0cy5maW5kKHMgPT4gcy5pZCA9PT0gc3ViamVjdElkKSB8fCBudWxsO1xuICB9XG5cbiAgLy8gPT09INil2K/Yp9ix2Kkg2KfZhNiv2LHYrNin2KogPT09XG4gIGdldEdyYWRlcygpOiBHcmFkZVtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPEdyYWRlW10+KFNUT1JBR0VfS0VZUy5HUkFERVMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZUdyYWRlcyhncmFkZXM6IEdyYWRlW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkdSQURFUywgZ3JhZGVzKTtcbiAgfVxuXG4gIGFkZEdyYWRlKGdyYWRlOiBHcmFkZSk6IHZvaWQge1xuICAgIGNvbnN0IGdyYWRlcyA9IHRoaXMuZ2V0R3JhZGVzKCk7XG4gICAgZ3JhZGVzLnB1c2goZ3JhZGUpO1xuICAgIHRoaXMuc2F2ZUdyYWRlcyhncmFkZXMpO1xuICB9XG5cbiAgdXBkYXRlR3JhZGUoZ3JhZGVJZDogc3RyaW5nLCB1cGRhdGVkR3JhZGU6IFBhcnRpYWw8R3JhZGU+KTogdm9pZCB7XG4gICAgY29uc3QgZ3JhZGVzID0gdGhpcy5nZXRHcmFkZXMoKTtcbiAgICBjb25zdCBpbmRleCA9IGdyYWRlcy5maW5kSW5kZXgoZyA9PiBnLmlkID09PSBncmFkZUlkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBncmFkZXNbaW5kZXhdID0geyAuLi5ncmFkZXNbaW5kZXhdLCAuLi51cGRhdGVkR3JhZGUsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9O1xuICAgICAgdGhpcy5zYXZlR3JhZGVzKGdyYWRlcyk7XG4gICAgfVxuICB9XG5cbiAgZGVsZXRlR3JhZGUoZ3JhZGVJZDogc3RyaW5nKTogdm9pZCB7XG4gICAgY29uc3QgZ3JhZGVzID0gdGhpcy5nZXRHcmFkZXMoKTtcbiAgICBjb25zdCBmaWx0ZXJlZEdyYWRlcyA9IGdyYWRlcy5maWx0ZXIoZyA9PiBnLmlkICE9PSBncmFkZUlkKTtcbiAgICB0aGlzLnNhdmVHcmFkZXMoZmlsdGVyZWRHcmFkZXMpO1xuICB9XG5cbiAgZ2V0R3JhZGVzQnlTdHVkZW50KHN0dWRlbnRJZDogc3RyaW5nKTogR3JhZGVbXSB7XG4gICAgY29uc3QgZ3JhZGVzID0gdGhpcy5nZXRHcmFkZXMoKTtcbiAgICByZXR1cm4gZ3JhZGVzLmZpbHRlcihnID0+IGcuc3R1ZGVudElkID09PSBzdHVkZW50SWQpO1xuICB9XG5cbiAgZ2V0R3JhZGVzQnlDbGFzcyhjbGFzc0lkOiBzdHJpbmcpOiBHcmFkZVtdIHtcbiAgICBjb25zdCBncmFkZXMgPSB0aGlzLmdldEdyYWRlcygpO1xuICAgIHJldHVybiBncmFkZXMuZmlsdGVyKGcgPT4gZy5jbGFzc0lkID09PSBjbGFzc0lkKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYrdi22YjYsSA9PT1cbiAgZ2V0QXR0ZW5kYW5jZSgpOiBBdHRlbmRhbmNlW10ge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08QXR0ZW5kYW5jZVtdPihTVE9SQUdFX0tFWVMuQVRURU5EQU5DRSkgfHwgW107XG4gIH1cblxuICBzYXZlQXR0ZW5kYW5jZShhdHRlbmRhbmNlOiBBdHRlbmRhbmNlW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkFUVEVOREFOQ0UsIGF0dGVuZGFuY2UpO1xuICB9XG5cbiAgYWRkQXR0ZW5kYW5jZShhdHRlbmRhbmNlOiBBdHRlbmRhbmNlKTogdm9pZCB7XG4gICAgY29uc3QgYXR0ZW5kYW5jZVJlY29yZHMgPSB0aGlzLmdldEF0dGVuZGFuY2UoKTtcbiAgICBhdHRlbmRhbmNlUmVjb3Jkcy5wdXNoKGF0dGVuZGFuY2UpO1xuICAgIHRoaXMuc2F2ZUF0dGVuZGFuY2UoYXR0ZW5kYW5jZVJlY29yZHMpO1xuICB9XG5cbiAgdXBkYXRlQXR0ZW5kYW5jZShhdHRlbmRhbmNlSWQ6IHN0cmluZywgdXBkYXRlZEF0dGVuZGFuY2U6IFBhcnRpYWw8QXR0ZW5kYW5jZT4pOiB2b2lkIHtcbiAgICBjb25zdCBhdHRlbmRhbmNlUmVjb3JkcyA9IHRoaXMuZ2V0QXR0ZW5kYW5jZSgpO1xuICAgIGNvbnN0IGluZGV4ID0gYXR0ZW5kYW5jZVJlY29yZHMuZmluZEluZGV4KGEgPT4gYS5pZCA9PT0gYXR0ZW5kYW5jZUlkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBhdHRlbmRhbmNlUmVjb3Jkc1tpbmRleF0gPSB7IC4uLmF0dGVuZGFuY2VSZWNvcmRzW2luZGV4XSwgLi4udXBkYXRlZEF0dGVuZGFuY2UsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9O1xuICAgICAgdGhpcy5zYXZlQXR0ZW5kYW5jZShhdHRlbmRhbmNlUmVjb3Jkcyk7XG4gICAgfVxuICB9XG5cbiAgZ2V0QXR0ZW5kYW5jZUJ5U3R1ZGVudChzdHVkZW50SWQ6IHN0cmluZyk6IEF0dGVuZGFuY2VbXSB7XG4gICAgY29uc3QgYXR0ZW5kYW5jZSA9IHRoaXMuZ2V0QXR0ZW5kYW5jZSgpO1xuICAgIHJldHVybiBhdHRlbmRhbmNlLmZpbHRlcihhID0+IGEuc3R1ZGVudElkID09PSBzdHVkZW50SWQpO1xuICB9XG5cbiAgZ2V0QXR0ZW5kYW5jZUJ5RGF0ZShkYXRlOiBEYXRlKTogQXR0ZW5kYW5jZVtdIHtcbiAgICBjb25zdCBhdHRlbmRhbmNlID0gdGhpcy5nZXRBdHRlbmRhbmNlKCk7XG4gICAgY29uc3QgZGF0ZVN0cmluZyA9IGRhdGUudG9EYXRlU3RyaW5nKCk7XG4gICAgcmV0dXJuIGF0dGVuZGFuY2UuZmlsdGVyKGEgPT4gbmV3IERhdGUoYS5kYXRlKS50b0RhdGVTdHJpbmcoKSA9PT0gZGF0ZVN0cmluZyk7XG4gIH1cblxuICAvLyA9PT0g2KXYr9in2LHYqSDYp9mE2YXYs9iq2K7Yr9mF2YrZhiA9PT1cbiAgZ2V0VXNlcnMoKTogVXNlcltdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPFVzZXJbXT4oU1RPUkFHRV9LRVlTLlVTRVJTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVVc2Vycyh1c2VyczogVXNlcltdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5VU0VSUywgdXNlcnMpO1xuICB9XG5cbiAgZ2V0Q3VycmVudFVzZXIoKTogVXNlciB8IG51bGwge1xuICAgIHJldHVybiB0aGlzLmdldEl0ZW08VXNlcj4oU1RPUkFHRV9LRVlTLkNVUlJFTlRfVVNFUik7XG4gIH1cblxuICBzZXRDdXJyZW50VXNlcih1c2VyOiBVc2VyKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5DVVJSRU5UX1VTRVIsIHVzZXIpO1xuICB9XG5cbiAgbG9nb3V0KCk6IHZvaWQge1xuICAgIHRoaXMucmVtb3ZlSXRlbShTVE9SQUdFX0tFWVMuQ1VSUkVOVF9VU0VSKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYpdi52K/Yp9iv2KfYqiA9PT1cbiAgZ2V0U2V0dGluZ3MoKTogU2V0dGluZ3MgfCBudWxsIHtcbiAgICByZXR1cm4gdGhpcy5nZXRJdGVtPFNldHRpbmdzPihTVE9SQUdFX0tFWVMuU0VUVElOR1MpO1xuICB9XG5cbiAgc2F2ZVNldHRpbmdzKHNldHRpbmdzOiBTZXR0aW5ncyk6IHZvaWQge1xuICAgIHRoaXMuc2V0SXRlbShTVE9SQUdFX0tFWVMuU0VUVElOR1MsIHNldHRpbmdzKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYo9it2K/Yp9irID09PVxuICBnZXRFdmVudHMoKTogRXZlbnRbXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0SXRlbTxFdmVudFtdPihTVE9SQUdFX0tFWVMuRVZFTlRTKSB8fCBbXTtcbiAgfVxuXG4gIHNhdmVFdmVudHMoZXZlbnRzOiBFdmVudFtdKTogdm9pZCB7XG4gICAgdGhpcy5zZXRJdGVtKFNUT1JBR0VfS0VZUy5FVkVOVFMsIGV2ZW50cyk7XG4gIH1cblxuICBhZGRFdmVudChldmVudDogRXZlbnQpOiB2b2lkIHtcbiAgICBjb25zdCBldmVudHMgPSB0aGlzLmdldEV2ZW50cygpO1xuICAgIGV2ZW50cy5wdXNoKGV2ZW50KTtcbiAgICB0aGlzLnNhdmVFdmVudHMoZXZlbnRzKTtcbiAgfVxuXG4gIC8vID09PSDYpdiv2KfYsdipINin2YTYpdi02LnYp9ix2KfYqiA9PT1cbiAgZ2V0Tm90aWZpY2F0aW9ucygpOiBOb3RpZmljYXRpb25bXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0SXRlbTxOb3RpZmljYXRpb25bXT4oU1RPUkFHRV9LRVlTLk5PVElGSUNBVElPTlMpIHx8IFtdO1xuICB9XG5cbiAgc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9uczogTm90aWZpY2F0aW9uW10pOiB2b2lkIHtcbiAgICB0aGlzLnNldEl0ZW0oU1RPUkFHRV9LRVlTLk5PVElGSUNBVElPTlMsIG5vdGlmaWNhdGlvbnMpO1xuICB9XG5cbiAgYWRkTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKTogdm9pZCB7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9ucyA9IHRoaXMuZ2V0Tm90aWZpY2F0aW9ucygpO1xuICAgIG5vdGlmaWNhdGlvbnMucHVzaChub3RpZmljYXRpb24pO1xuICAgIHRoaXMuc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucyk7XG4gIH1cblxuICBtYXJrTm90aWZpY2F0aW9uQXNSZWFkKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBub3RpZmljYXRpb25zID0gdGhpcy5nZXROb3RpZmljYXRpb25zKCk7XG4gICAgY29uc3QgaW5kZXggPSBub3RpZmljYXRpb25zLmZpbmRJbmRleChuID0+IG4uaWQgPT09IG5vdGlmaWNhdGlvbklkKTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBub3RpZmljYXRpb25zW2luZGV4XS5pc1JlYWQgPSB0cnVlO1xuICAgICAgbm90aWZpY2F0aW9uc1tpbmRleF0ucmVhZEF0ID0gbmV3IERhdGUoKTtcbiAgICAgIHRoaXMuc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucyk7XG4gICAgfVxuICB9XG59XG5cbi8vINil2YbYtNin2KEg2YXYq9mK2YQg2YjYp9it2K8g2YTZhNin2LPYqtiu2K/Yp9mFINmB2Yog2KfZhNiq2LfYqNmK2YJcbmV4cG9ydCBjb25zdCBsb2NhbFN0b3JhZ2VNYW5hZ2VyID0gbmV3IExvY2FsU3RvcmFnZU1hbmFnZXIoKTtcblxuLy8g2K/ZiNin2YQg2YXYs9in2LnYr9ipINmE2YTYqNit2Ksg2YjYp9mE2KrYtdmB2YrYqVxuZXhwb3J0IGNvbnN0IHNlYXJjaFV0aWxzID0ge1xuICBzZWFyY2hTdHVkZW50czogKHF1ZXJ5OiBzdHJpbmcsIHN0dWRlbnRzOiBTdHVkZW50W10pOiBTdHVkZW50W10gPT4ge1xuICAgIGNvbnN0IGxvd2VyY2FzZVF1ZXJ5ID0gcXVlcnkudG9Mb3dlckNhc2UoKTtcbiAgICByZXR1cm4gc3R1ZGVudHMuZmlsdGVyKHN0dWRlbnQgPT4gXG4gICAgICBzdHVkZW50Lm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlcmNhc2VRdWVyeSkgfHxcbiAgICAgIHN0dWRlbnQuc3R1ZGVudElkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICBzdHVkZW50LmVtYWlsPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KVxuICAgICk7XG4gIH0sXG5cbiAgc2VhcmNoVGVhY2hlcnM6IChxdWVyeTogc3RyaW5nLCB0ZWFjaGVyczogVGVhY2hlcltdKTogVGVhY2hlcltdID0+IHtcbiAgICBjb25zdCBsb3dlcmNhc2VRdWVyeSA9IHF1ZXJ5LnRvTG93ZXJDYXNlKCk7XG4gICAgcmV0dXJuIHRlYWNoZXJzLmZpbHRlcih0ZWFjaGVyID0+XG4gICAgICB0ZWFjaGVyLm5hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICB0ZWFjaGVyLmZ1bGxOYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgdGVhY2hlci50ZWFjaGVySWQ/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICB0ZWFjaGVyLnNlcmlhbE51bWJlcj8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlcmNhc2VRdWVyeSkgfHxcbiAgICAgIHRlYWNoZXIuZW1haWw/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICB0ZWFjaGVyLnNwZWNpYWxpemF0aW9uPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KVxuICAgICk7XG4gIH0sXG5cbiAgc2VhcmNoRW1wbG95ZWVzOiAocXVlcnk6IHN0cmluZywgZW1wbG95ZWVzOiBFbXBsb3llZVtdKTogRW1wbG95ZWVbXSA9PiB7XG4gICAgY29uc3QgbG93ZXJjYXNlUXVlcnkgPSBxdWVyeS50b0xvd2VyQ2FzZSgpO1xuICAgIHJldHVybiBlbXBsb3llZXMuZmlsdGVyKGVtcGxveWVlID0+XG4gICAgICBlbXBsb3llZS5uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUuZnVsbE5hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICBlbXBsb3llZS5lbXBsb3llZUlkPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUuc2VyaWFsTnVtYmVyPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUuZW1haWw/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgICBlbXBsb3llZS5kZXBhcnRtZW50Py50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KSB8fFxuICAgICAgZW1wbG95ZWUucG9zaXRpb24/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpXG4gICAgKTtcbiAgfSxcblxuICBmaWx0ZXJTdHVkZW50c0J5Q2xhc3M6IChjbGFzc0lkOiBzdHJpbmcsIHN0dWRlbnRzOiBTdHVkZW50W10pOiBTdHVkZW50W10gPT4ge1xuICAgIHJldHVybiBzdHVkZW50cy5maWx0ZXIoc3R1ZGVudCA9PiBzdHVkZW50LmNsYXNzSWQgPT09IGNsYXNzSWQpO1xuICB9LFxuXG4gIGZpbHRlckdyYWRlc0J5RGF0ZVJhbmdlOiAoc3RhcnREYXRlOiBEYXRlLCBlbmREYXRlOiBEYXRlLCBncmFkZXM6IEdyYWRlW10pOiBHcmFkZVtdID0+IHtcbiAgICByZXR1cm4gZ3JhZGVzLmZpbHRlcihncmFkZSA9PiB7XG4gICAgICBjb25zdCBncmFkZURhdGUgPSBuZXcgRGF0ZShncmFkZS5leGFtRGF0ZSk7XG4gICAgICByZXR1cm4gZ3JhZGVEYXRlID49IHN0YXJ0RGF0ZSAmJiBncmFkZURhdGUgPD0gZW5kRGF0ZTtcbiAgICB9KTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6WyJTVE9SQUdFX0tFWVMiLCJTVFVERU5UUyIsIlRFQUNIRVJTIiwiRU1QTE9ZRUVTIiwiQ0xBU1NFUyIsIlNVQkpFQ1RTIiwiR1JBREVTIiwiQVRURU5EQU5DRSIsIlVTRVJTIiwiU0VUVElOR1MiLCJFVkVOVFMiLCJOT1RJRklDQVRJT05TIiwiQ1VSUkVOVF9VU0VSIiwiQUNBREVNSUNfWUVBUiIsIkNVUlJFTlRfU0VNRVNURVIiLCJMb2NhbFN0b3JhZ2VNYW5hZ2VyIiwic2V0SXRlbSIsImtleSIsImRhdGEiLCJzZXJpYWxpemVkRGF0YSIsIkpTT04iLCJzdHJpbmdpZnkiLCJsb2NhbFN0b3JhZ2UiLCJlcnJvciIsImNvbnNvbGUiLCJnZXRJdGVtIiwicGFyc2UiLCJyZW1vdmVJdGVtIiwiY2xlYXJBbGwiLCJPYmplY3QiLCJ2YWx1ZXMiLCJmb3JFYWNoIiwiZ2V0U3R1ZGVudHMiLCJzYXZlU3R1ZGVudHMiLCJzdHVkZW50cyIsImFkZFN0dWRlbnQiLCJzdHVkZW50IiwicHVzaCIsInVwZGF0ZVN0dWRlbnQiLCJzdHVkZW50SWQiLCJ1cGRhdGVkU3R1ZGVudCIsImluZGV4IiwiZmluZEluZGV4IiwicyIsImlkIiwidXBkYXRlZEF0IiwiRGF0ZSIsImRlbGV0ZVN0dWRlbnQiLCJmaWx0ZXJlZFN0dWRlbnRzIiwiZmlsdGVyIiwiZ2V0U3R1ZGVudEJ5SWQiLCJmaW5kIiwiZ2V0VGVhY2hlcnMiLCJzYXZlVGVhY2hlcnMiLCJ0ZWFjaGVycyIsImFkZFRlYWNoZXIiLCJ0ZWFjaGVyIiwidXBkYXRlVGVhY2hlciIsInRlYWNoZXJJZCIsInVwZGF0ZWRUZWFjaGVyIiwidCIsImRlbGV0ZVRlYWNoZXIiLCJmaWx0ZXJlZFRlYWNoZXJzIiwiZ2V0VGVhY2hlckJ5SWQiLCJnZXRFbXBsb3llZXMiLCJzYXZlRW1wbG95ZWVzIiwiZW1wbG95ZWVzIiwiYWRkRW1wbG95ZWUiLCJlbXBsb3llZSIsInVwZGF0ZUVtcGxveWVlIiwiZW1wbG95ZWVJZCIsInVwZGF0ZWRFbXBsb3llZSIsImUiLCJkZWxldGVFbXBsb3llZSIsImZpbHRlcmVkRW1wbG95ZWVzIiwiZ2V0RW1wbG95ZWVCeUlkIiwiZ2V0Q2xhc3NlcyIsInNhdmVDbGFzc2VzIiwiY2xhc3NlcyIsImFkZENsYXNzIiwiY2xhc3NEYXRhIiwidXBkYXRlQ2xhc3MiLCJjbGFzc0lkIiwidXBkYXRlZENsYXNzIiwiYyIsImRlbGV0ZUNsYXNzIiwiZmlsdGVyZWRDbGFzc2VzIiwiZ2V0Q2xhc3NCeUlkIiwiZ2V0U3ViamVjdHMiLCJzYXZlU3ViamVjdHMiLCJzdWJqZWN0cyIsImFkZFN1YmplY3QiLCJzdWJqZWN0IiwidXBkYXRlU3ViamVjdCIsInN1YmplY3RJZCIsInVwZGF0ZWRTdWJqZWN0IiwiZGVsZXRlU3ViamVjdCIsImZpbHRlcmVkU3ViamVjdHMiLCJnZXRTdWJqZWN0QnlJZCIsImdldEdyYWRlcyIsInNhdmVHcmFkZXMiLCJncmFkZXMiLCJhZGRHcmFkZSIsImdyYWRlIiwidXBkYXRlR3JhZGUiLCJncmFkZUlkIiwidXBkYXRlZEdyYWRlIiwiZyIsImRlbGV0ZUdyYWRlIiwiZmlsdGVyZWRHcmFkZXMiLCJnZXRHcmFkZXNCeVN0dWRlbnQiLCJnZXRHcmFkZXNCeUNsYXNzIiwiZ2V0QXR0ZW5kYW5jZSIsInNhdmVBdHRlbmRhbmNlIiwiYXR0ZW5kYW5jZSIsImFkZEF0dGVuZGFuY2UiLCJhdHRlbmRhbmNlUmVjb3JkcyIsInVwZGF0ZUF0dGVuZGFuY2UiLCJhdHRlbmRhbmNlSWQiLCJ1cGRhdGVkQXR0ZW5kYW5jZSIsImEiLCJnZXRBdHRlbmRhbmNlQnlTdHVkZW50IiwiZ2V0QXR0ZW5kYW5jZUJ5RGF0ZSIsImRhdGUiLCJkYXRlU3RyaW5nIiwidG9EYXRlU3RyaW5nIiwiZ2V0VXNlcnMiLCJzYXZlVXNlcnMiLCJ1c2VycyIsImdldEN1cnJlbnRVc2VyIiwic2V0Q3VycmVudFVzZXIiLCJ1c2VyIiwibG9nb3V0IiwiZ2V0U2V0dGluZ3MiLCJzYXZlU2V0dGluZ3MiLCJzZXR0aW5ncyIsImdldEV2ZW50cyIsInNhdmVFdmVudHMiLCJldmVudHMiLCJhZGRFdmVudCIsImV2ZW50IiwiZ2V0Tm90aWZpY2F0aW9ucyIsInNhdmVOb3RpZmljYXRpb25zIiwibm90aWZpY2F0aW9ucyIsImFkZE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbiIsIm1hcmtOb3RpZmljYXRpb25Bc1JlYWQiLCJub3RpZmljYXRpb25JZCIsIm4iLCJpc1JlYWQiLCJyZWFkQXQiLCJsb2NhbFN0b3JhZ2VNYW5hZ2VyIiwic2VhcmNoVXRpbHMiLCJzZWFyY2hTdHVkZW50cyIsInF1ZXJ5IiwibG93ZXJjYXNlUXVlcnkiLCJ0b0xvd2VyQ2FzZSIsIm5hbWUiLCJpbmNsdWRlcyIsImVtYWlsIiwic2VhcmNoVGVhY2hlcnMiLCJmdWxsTmFtZSIsInNlcmlhbE51bWJlciIsInNwZWNpYWxpemF0aW9uIiwic2VhcmNoRW1wbG95ZWVzIiwiZGVwYXJ0bWVudCIsInBvc2l0aW9uIiwiZmlsdGVyU3R1ZGVudHNCeUNsYXNzIiwiZmlsdGVyR3JhZGVzQnlEYXRlUmFuZ2UiLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZ3JhZGVEYXRlIiwiZXhhbURhdGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/localStorage.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();