/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/reset-data/page";
exports.ids = ["app/reset-data/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freset-data%2Fpage&page=%2Freset-data%2Fpage&appPaths=%2Freset-data%2Fpage&pagePath=private-next-app-dir%2Freset-data%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freset-data%2Fpage&page=%2Freset-data%2Fpage&appPaths=%2Freset-data%2Fpage&pagePath=private-next-app-dir%2Freset-data%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reset-data/page.tsx */ \"(rsc)/./src/app/reset-data/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'reset-data',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/reset-data/page\",\n        pathname: \"/reset-data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freset-data%2Fpage&page=%2Freset-data%2Fpage&appPaths=%2Freset-data%2Fpage&pagePath=private-next-app-dir%2Freset-data%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1QyVEOSU4NiVEOCVCOCVEOCVBNyVEOSU4NSUyMCVEOSU4NSVEOCVBRiVEOCVCMSVEOCVCMyVEOCVBOSU1QyU1Q3NjaG9vbC1tYW5hZ2VtZW50JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDJUQ5JTg2JUQ4JUI4JUQ4JUE3JUQ5JTg1JTIwJUQ5JTg1JUQ4JUFGJUQ4JUIxJUQ4JUIzJUQ4JUE5JTVDJTVDc2Nob29sLW1hbmFnZW1lbnQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1QyVEOSU4NiVEOCVCOCVEOCVBNyVEOSU4NSUyMCVEOSU4NSVEOCVBRiVEOCVCMSVEOCVCMyVEOCVBOSU1QyU1Q3NjaG9vbC1tYW5hZ2VtZW50JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQW9JO0FBQ3BJO0FBQ0EsME9BQXVJO0FBQ3ZJO0FBQ0EsME9BQXVJO0FBQ3ZJO0FBQ0Esb1JBQTZKO0FBQzdKO0FBQ0Esd09BQXNJO0FBQ3RJO0FBQ0EsNFBBQWlKO0FBQ2pKO0FBQ0Esa1FBQW9KO0FBQ3BKO0FBQ0Esc1FBQXFKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhti42KfZhSDZhdiv2LHYs9ipXFxcXHNjaG9vbC1tYW5hZ2VtZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhti42KfZhSDZhdiv2LHYs9ipXFxcXHNjaG9vbC1tYW5hZ2VtZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhti42KfZhSDZhdiv2LHYs9ipXFxcXHNjaG9vbC1tYW5hZ2VtZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXFxcc2Nob29sLW1hbmFnZW1lbnRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Creset-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Creset-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reset-data/page.tsx */ \"(rsc)/./src/app/reset-data/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Jlc2V0LWRhdGElNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhti42KfZhSDZhdiv2LHYs9ipXFxcXHNjaG9vbC1tYW5hZ2VtZW50XFxcXHNyY1xcXFxhcHBcXFxccmVzZXQtZGF0YVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Creset-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e7050786830a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc2YbYuNin2YUg2YXYr9ix2LPYqVxcc2Nob29sLW1hbmFnZW1lbnRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU3MDUwNzg2ODMwYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"نظام إدارة المدرسة\",\n    description: \"نظام شامل لإدارة المدارس باللغة العربية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/reset-data/page.tsx":
/*!*************************************!*\
  !*** ./src/app/reset-data/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\نظام مدرسة\\school-management\\src\\app\\reset-data\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Creset-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Creset-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reset-data/page.tsx */ \"(ssr)/./src/app/reset-data/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODYlRDglQjglRDglQTclRDklODUlMjAlRDklODUlRDglQUYlRDglQjElRDglQjMlRDglQTklNUMlNUNzY2hvb2wtbWFuYWdlbWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Jlc2V0LWRhdGElNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhti42KfZhSDZhdiv2LHYs9ipXFxcXHNjaG9vbC1tYW5hZ2VtZW50XFxcXHNyY1xcXFxhcHBcXFxccmVzZXQtZGF0YVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Creset-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/reset-data/page.tsx":
/*!*************************************!*\
  !*** ./src/app/reset-data/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetDataPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Button */ \"(ssr)/./src/components/Button.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./src/components/Card.tsx\");\n/* harmony import */ var _utils_sampleData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/sampleData */ \"(ssr)/./src/utils/sampleData.ts\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ResetDataPage() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messageType, setMessageType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('success');\n    // بيانات إنشاء حساب المطور\n    const [developerData, setDeveloperData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: 'عبيدة العيثاوي',\n        username: 'obeida',\n        email: '<EMAIL>',\n        password: '12345'\n    });\n    const handleResetData = async ()=>{\n        if (!confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم حذف جميع البيانات الموجودة!')) {\n            return;\n        }\n        setLoading(true);\n        setMessage('');\n        try {\n            // إعادة تعيين البيانات\n            (0,_utils_sampleData__WEBPACK_IMPORTED_MODULE_4__.resetSampleData)();\n            setMessage('تم إعادة تعيين البيانات بنجاح! يمكنك الآن تسجيل الدخول باستخدام البيانات الافتراضية.');\n            setMessageType('success');\n            // إعادة توجيه إلى صفحة تسجيل الدخول بعد 3 ثوان\n            setTimeout(()=>{\n                window.location.href = '/login';\n            }, 3000);\n        } catch (error) {\n            console.error('خطأ في إعادة تعيين البيانات:', error);\n            setMessage('حدث خطأ أثناء إعادة تعيين البيانات');\n            setMessageType('error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateDeveloper = async ()=>{\n        if (!developerData.username || !developerData.email || !developerData.password) {\n            setMessage('يرجى ملء جميع الحقول المطلوبة');\n            setMessageType('error');\n            return;\n        }\n        setLoading(true);\n        setMessage('');\n        try {\n            // إنشاء حساب المطور\n            (0,_utils_sampleData__WEBPACK_IMPORTED_MODULE_4__.createDeveloperAccount)(developerData.username, developerData.email, developerData.password, developerData.name);\n            setMessage(`تم إنشاء حساب المطور بنجاح!\\nاسم المستخدم: ${developerData.username}\\nكلمة المرور: ${developerData.password}`);\n            setMessageType('success');\n            // إعادة توجيه إلى صفحة تسجيل الدخول بعد 3 ثوان\n            setTimeout(()=>{\n                window.location.href = '/login';\n            }, 3000);\n        } catch (error) {\n            console.error('خطأ في إنشاء حساب المطور:', error);\n            setMessage('حدث خطأ أثناء إنشاء حساب المطور');\n            setMessageType('error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const showCurrentUsers = ()=>{\n        const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_5__.localStorageManager.getUsers();\n        if (users.length === 0) {\n            setMessage('لا يوجد مستخدمين في النظام');\n            setMessageType('error');\n        } else {\n            const usersList = users.map((u)=>`${u.name} (${u.username || u.email}) - ${u.role}`).join('\\n');\n            setMessage(`المستخدمين الموجودين:\\n${usersList}`);\n            setMessageType('success');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl w-full space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"إعادة تعيين البيانات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"أدوات لإعادة تعيين البيانات وإنشاء حساب مطور جديد\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: \"المستخدمين الحاليين\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onClick: showCurrentUsers,\n                            variant: \"secondary\",\n                            fullWidth: true,\n                            children: \"عرض المستخدمين الموجودين\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: \"إنشاء/تحديث حساب المطور\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"الاسم\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: developerData.name,\n                                            onChange: (e)=>setDeveloperData({\n                                                    ...developerData,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"اسم المطور\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"اسم المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: developerData.username,\n                                            onChange: (e)=>setDeveloperData({\n                                                    ...developerData,\n                                                    username: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"اسم المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"البريد الإلكتروني\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: developerData.email,\n                                            onChange: (e)=>setDeveloperData({\n                                                    ...developerData,\n                                                    email: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"البريد الإلكتروني\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: developerData.password,\n                                            onChange: (e)=>setDeveloperData({\n                                                    ...developerData,\n                                                    password: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    onClick: handleCreateDeveloper,\n                                    variant: \"primary\",\n                                    fullWidth: true,\n                                    loading: loading,\n                                    children: \"إنشاء/تحديث حساب المطور\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4 text-red-600\",\n                            children: \"إعادة تعيين جميع البيانات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"سيتم حذف جميع البيانات الموجودة وإنشاء بيانات تجريبية جديدة تتضمن:\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside text-gray-600 mb-4 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"المطور: obeida / 12345\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"الأدمن: admin / admin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"بيانات تجريبية للطلاب والمعلمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onClick: handleResetData,\n                            variant: \"danger\",\n                            fullWidth: true,\n                            loading: loading,\n                            children: \"إعادة تعيين جميع البيانات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: messageType === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50',\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm whitespace-pre-line ${messageType === 'success' ? 'text-green-700' : 'text-red-700'}`,\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/login\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"العودة إلى صفحة تسجيل الدخول\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\reset-data\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/reset-data/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button.tsx":
/*!***********************************!*\
  !*** ./src/components/Button.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Button = ({ children, onClick, type = 'button', variant = 'primary', size = 'md', disabled = false, loading = false, fullWidth = false, className = '', icon, iconPosition = 'right', title })=>{\n    const baseClasses = `\n    inline-flex items-center justify-center font-medium rounded-lg\n    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${fullWidth ? 'w-full' : ''}\n  `;\n    const sizeClasses = {\n        sm: 'px-3 py-2 text-sm',\n        md: 'px-4 py-2.5 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    const variantClasses = {\n        primary: `\n      bg-gradient-to-r from-blue-500 to-purple-600 text-white\n      hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        secondary: `\n      bg-gray-100 text-gray-700 border border-gray-300\n      hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500\n      shadow-md hover:shadow-lg\n    `,\n        success: `\n      bg-gradient-to-r from-green-500 to-emerald-600 text-white\n      hover:from-green-600 hover:to-emerald-700 focus:ring-green-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        danger: `\n      bg-gradient-to-r from-red-500 to-pink-600 text-white\n      hover:from-red-600 hover:to-pink-700 focus:ring-red-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        warning: `\n      bg-gradient-to-r from-yellow-500 to-orange-600 text-white\n      hover:from-yellow-600 hover:to-orange-700 focus:ring-yellow-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `,\n        info: `\n      bg-gradient-to-r from-cyan-500 to-blue-600 text-white\n      hover:from-cyan-600 hover:to-blue-700 focus:ring-cyan-500\n      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\n    `\n    };\n    const combinedClasses = `\n    ${baseClasses}\n    ${sizeClasses[size]}\n    ${variantClasses[variant]}\n    ${className}\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled || loading,\n        className: combinedClasses,\n        title: title,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-current\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            icon && iconPosition === 'right' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            icon && iconPosition === 'left' && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Button.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9CdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUUwQjtBQWlCMUIsTUFBTUMsU0FBZ0MsQ0FBQyxFQUNyQ0MsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLE9BQU8sUUFBUSxFQUNmQyxVQUFVLFNBQVMsRUFDbkJDLE9BQU8sSUFBSSxFQUNYQyxXQUFXLEtBQUssRUFDaEJDLFVBQVUsS0FBSyxFQUNmQyxZQUFZLEtBQUssRUFDakJDLFlBQVksRUFBRSxFQUNkQyxJQUFJLEVBQ0pDLGVBQWUsT0FBTyxFQUN0QkMsS0FBSyxFQUNOO0lBQ0MsTUFBTUMsY0FBYyxDQUFDOzs7O0lBSW5CLEVBQUVMLFlBQVksV0FBVyxHQUFHO0VBQzlCLENBQUM7SUFFRCxNQUFNTSxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCQyxTQUFTLENBQUM7Ozs7SUFJVixDQUFDO1FBQ0RDLFdBQVcsQ0FBQzs7OztJQUlaLENBQUM7UUFDREMsU0FBUyxDQUFDOzs7O0lBSVYsQ0FBQztRQUNEQyxRQUFRLENBQUM7Ozs7SUFJVCxDQUFDO1FBQ0RDLFNBQVMsQ0FBQzs7OztJQUlWLENBQUM7UUFDREMsTUFBTSxDQUFDOzs7O0lBSVAsQ0FBQztJQUNIO0lBRUEsTUFBTUMsa0JBQWtCLENBQUM7SUFDdkIsRUFBRVosWUFBWTtJQUNkLEVBQUVDLFdBQVcsQ0FBQ1QsS0FBSyxDQUFDO0lBQ3BCLEVBQUVhLGNBQWMsQ0FBQ2QsUUFBUSxDQUFDO0lBQzFCLEVBQUVLLFVBQVU7RUFDZCxDQUFDO0lBRUQscUJBQ0UsOERBQUNpQjtRQUNDdkIsTUFBTUE7UUFDTkQsU0FBU0E7UUFDVEksVUFBVUEsWUFBWUM7UUFDdEJFLFdBQVdnQjtRQUNYYixPQUFPQTs7WUFFTkwseUJBQ0MsOERBQUNvQjtnQkFDQ2xCLFdBQVU7Z0JBQ1ZtQixPQUFNO2dCQUNOQyxNQUFLO2dCQUNMQyxTQUFROztrQ0FFUiw4REFBQ0M7d0JBQ0N0QixXQUFVO3dCQUNWdUIsSUFBRzt3QkFDSEMsSUFBRzt3QkFDSEMsR0FBRTt3QkFDRkMsUUFBTzt3QkFDUEMsYUFBWTs7Ozs7O2tDQUVkLDhEQUFDQzt3QkFDQzVCLFdBQVU7d0JBQ1ZvQixNQUFLO3dCQUNMUyxHQUFFOzs7Ozs7Ozs7Ozs7WUFLUDVCLFFBQVFDLGlCQUFpQixXQUFXLENBQUNKLHlCQUNwQyw4REFBQ2dDO2dCQUFLOUIsV0FBVTswQkFBUUM7Ozs7OzswQkFHMUIsOERBQUM2QjswQkFBTXRDOzs7Ozs7WUFFTlMsUUFBUUMsaUJBQWlCLFVBQVUsQ0FBQ0oseUJBQ25DLDhEQUFDZ0M7Z0JBQUs5QixXQUFVOzBCQUFRQzs7Ozs7Ozs7Ozs7O0FBSWhDO0FBRUEsaUVBQWVWLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcc3JjXFxjb21wb25lbnRzXFxCdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIEJ1dHRvblByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgb25DbGljaz86ICgpID0+IHZvaWQ7XG4gIHR5cGU/OiAnYnV0dG9uJyB8ICdzdWJtaXQnIHwgJ3Jlc2V0JztcbiAgdmFyaWFudD86ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ3N1Y2Nlc3MnIHwgJ2RhbmdlcicgfCAnd2FybmluZycgfCAnaW5mbyc7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZyc7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgbG9hZGluZz86IGJvb2xlYW47XG4gIGZ1bGxXaWR0aD86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgaWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgaWNvblBvc2l0aW9uPzogJ2xlZnQnIHwgJ3JpZ2h0JztcbiAgdGl0bGU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IEJ1dHRvbjogUmVhY3QuRkM8QnV0dG9uUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIG9uQ2xpY2ssXG4gIHR5cGUgPSAnYnV0dG9uJyxcbiAgdmFyaWFudCA9ICdwcmltYXJ5JyxcbiAgc2l6ZSA9ICdtZCcsXG4gIGRpc2FibGVkID0gZmFsc2UsXG4gIGxvYWRpbmcgPSBmYWxzZSxcbiAgZnVsbFdpZHRoID0gZmFsc2UsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBpY29uLFxuICBpY29uUG9zaXRpb24gPSAncmlnaHQnLFxuICB0aXRsZVxufSkgPT4ge1xuICBjb25zdCBiYXNlQ2xhc3NlcyA9IGBcbiAgICBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZm9udC1tZWRpdW0gcm91bmRlZC1sZ1xuICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTJcbiAgICBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFxuICAgICR7ZnVsbFdpZHRoID8gJ3ctZnVsbCcgOiAnJ31cbiAgYDtcblxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbTogJ3B4LTMgcHktMiB0ZXh0LXNtJyxcbiAgICBtZDogJ3B4LTQgcHktMi41IHRleHQtc20nLFxuICAgIGxnOiAncHgtNiBweS0zIHRleHQtYmFzZSdcbiAgfTtcblxuICBjb25zdCB2YXJpYW50Q2xhc3NlcyA9IHtcbiAgICBwcmltYXJ5OiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlXG4gICAgICBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLXB1cnBsZS03MDAgZm9jdXM6cmluZy1ibHVlLTUwMFxuICAgICAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2Zvcm0gaG92ZXI6LXRyYW5zbGF0ZS15LTAuNVxuICAgIGAsXG4gICAgc2Vjb25kYXJ5OiBgXG4gICAgICBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS0zMDBcbiAgICAgIGhvdmVyOmJnLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ncmF5LTQwMCBmb2N1czpyaW5nLWdyYXktNTAwXG4gICAgICBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnXG4gICAgYCxcbiAgICBzdWNjZXNzOiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAwIHRvLWVtZXJhbGQtNjAwIHRleHQtd2hpdGVcbiAgICAgIGhvdmVyOmZyb20tZ3JlZW4tNjAwIGhvdmVyOnRvLWVtZXJhbGQtNzAwIGZvY3VzOnJpbmctZ3JlZW4tNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYCxcbiAgICBkYW5nZXI6IGBcbiAgICAgIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNTAwIHRvLXBpbmstNjAwIHRleHQtd2hpdGVcbiAgICAgIGhvdmVyOmZyb20tcmVkLTYwMCBob3Zlcjp0by1waW5rLTcwMCBmb2N1czpyaW5nLXJlZC01MDBcbiAgICAgIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0wLjVcbiAgICBgLFxuICAgIHdhcm5pbmc6IGBcbiAgICAgIGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNTAwIHRvLW9yYW5nZS02MDAgdGV4dC13aGl0ZVxuICAgICAgaG92ZXI6ZnJvbS15ZWxsb3ctNjAwIGhvdmVyOnRvLW9yYW5nZS03MDAgZm9jdXM6cmluZy15ZWxsb3ctNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYCxcbiAgICBpbmZvOiBgXG4gICAgICBiZy1ncmFkaWVudC10by1yIGZyb20tY3lhbi01MDAgdG8tYmx1ZS02MDAgdGV4dC13aGl0ZVxuICAgICAgaG92ZXI6ZnJvbS1jeWFuLTYwMCBob3Zlcjp0by1ibHVlLTcwMCBmb2N1czpyaW5nLWN5YW4tNTAwXG4gICAgICBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XG4gICAgYFxuICB9O1xuXG4gIGNvbnN0IGNvbWJpbmVkQ2xhc3NlcyA9IGBcbiAgICAke2Jhc2VDbGFzc2VzfVxuICAgICR7c2l6ZUNsYXNzZXNbc2l6ZV19XG4gICAgJHt2YXJpYW50Q2xhc3Nlc1t2YXJpYW50XX1cbiAgICAke2NsYXNzTmFtZX1cbiAgYDtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkIHx8IGxvYWRpbmd9XG4gICAgICBjbGFzc05hbWU9e2NvbWJpbmVkQ2xhc3Nlc31cbiAgICAgIHRpdGxlPXt0aXRsZX1cbiAgICA+XG4gICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgIDxzdmdcbiAgICAgICAgICBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gLW1sLTEgbXItMiBoLTQgdy00IHRleHQtY3VycmVudFwiXG4gICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICA+XG4gICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiXG4gICAgICAgICAgICBjeD1cIjEyXCJcbiAgICAgICAgICAgIGN5PVwiMTJcIlxuICAgICAgICAgICAgcj1cIjEwXCJcbiAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgIC8+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIlxuICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIHtpY29uICYmIGljb25Qb3NpdGlvbiA9PT0gJ3JpZ2h0JyAmJiAhbG9hZGluZyAmJiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTJcIj57aWNvbn08L3NwYW4+XG4gICAgICApfVxuICAgICAgXG4gICAgICA8c3Bhbj57Y2hpbGRyZW59PC9zcGFuPlxuICAgICAgXG4gICAgICB7aWNvbiAmJiBpY29uUG9zaXRpb24gPT09ICdsZWZ0JyAmJiAhbG9hZGluZyAmJiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj57aWNvbn08L3NwYW4+XG4gICAgICApfVxuICAgIDwvYnV0dG9uPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQnV0dG9uO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiY2hpbGRyZW4iLCJvbkNsaWNrIiwidHlwZSIsInZhcmlhbnQiLCJzaXplIiwiZGlzYWJsZWQiLCJsb2FkaW5nIiwiZnVsbFdpZHRoIiwiY2xhc3NOYW1lIiwiaWNvbiIsImljb25Qb3NpdGlvbiIsInRpdGxlIiwiYmFzZUNsYXNzZXMiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInZhcmlhbnRDbGFzc2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsInN1Y2Nlc3MiLCJkYW5nZXIiLCJ3YXJuaW5nIiwiaW5mbyIsImNvbWJpbmVkQ2xhc3NlcyIsImJ1dHRvbiIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwicGF0aCIsImQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Card.tsx":
/*!*********************************!*\
  !*** ./src/components/Card.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Card = ({ children, title, subtitle, className = '', onClick, hoverable = false, padding = 'md', shadow = 'md' })=>{\n    const paddingClasses = {\n        sm: 'p-4',\n        md: 'p-6',\n        lg: 'p-8'\n    };\n    const shadowClasses = {\n        sm: 'shadow-sm',\n        md: 'shadow-md',\n        lg: 'shadow-lg',\n        xl: 'shadow-xl'\n    };\n    const baseClasses = `\n    bg-white rounded-xl border border-gray-200 transition-all duration-300\n    ${paddingClasses[padding]}\n    ${shadowClasses[shadow]}\n    ${hoverable ? 'hover:shadow-xl hover:scale-105 cursor-pointer' : ''}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: baseClasses,\n        onClick: onClick,\n        children: [\n            (title || subtitle) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-1\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, undefined),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\Card.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFMEI7QUFhMUIsTUFBTUMsT0FBNEIsQ0FBQyxFQUNqQ0MsUUFBUSxFQUNSQyxLQUFLLEVBQ0xDLFFBQVEsRUFDUkMsWUFBWSxFQUFFLEVBQ2RDLE9BQU8sRUFDUEMsWUFBWSxLQUFLLEVBQ2pCQyxVQUFVLElBQUksRUFDZEMsU0FBUyxJQUFJLEVBQ2Q7SUFDQyxNQUFNQyxpQkFBaUI7UUFDckJDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEJILElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pFLElBQUk7SUFDTjtJQUVBLE1BQU1DLGNBQWMsQ0FBQzs7SUFFbkIsRUFBRU4sY0FBYyxDQUFDRixRQUFRLENBQUM7SUFDMUIsRUFBRU0sYUFBYSxDQUFDTCxPQUFPLENBQUM7SUFDeEIsRUFBRUYsWUFBWSxtREFBbUQsR0FBRztJQUNwRSxFQUFFRCxVQUFVLG1CQUFtQixHQUFHO0lBQ2xDLEVBQUVELFVBQVU7RUFDZCxDQUFDO0lBRUQscUJBQ0UsOERBQUNZO1FBQUlaLFdBQVdXO1FBQWFWLFNBQVNBOztZQUNsQ0gsQ0FBQUEsU0FBU0MsUUFBTyxtQkFDaEIsOERBQUNhO2dCQUFJWixXQUFVOztvQkFDWkYsdUJBQ0MsOERBQUNlO3dCQUFHYixXQUFVO2tDQUNYRjs7Ozs7O29CQUdKQywwQkFDQyw4REFBQ2U7d0JBQUVkLFdBQVU7a0NBQ1ZEOzs7Ozs7Ozs7Ozs7WUFLUkY7Ozs7Ozs7QUFHUDtBQUVBLGlFQUFlRCxJQUFJQSxFQUFDIiwic291cmNlcyI6WyJFOlxc2YbYuNin2YUg2YXYr9ix2LPYqVxcc2Nob29sLW1hbmFnZW1lbnRcXHNyY1xcY29tcG9uZW50c1xcQ2FyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgQ2FyZFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgdGl0bGU/OiBzdHJpbmc7XG4gIHN1YnRpdGxlPzogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xuICBob3ZlcmFibGU/OiBib29sZWFuO1xuICBwYWRkaW5nPzogJ3NtJyB8ICdtZCcgfCAnbGcnO1xuICBzaGFkb3c/OiAnc20nIHwgJ21kJyB8ICdsZycgfCAneGwnO1xufVxuXG5jb25zdCBDYXJkOiBSZWFjdC5GQzxDYXJkUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIHRpdGxlLFxuICBzdWJ0aXRsZSxcbiAgY2xhc3NOYW1lID0gJycsXG4gIG9uQ2xpY2ssXG4gIGhvdmVyYWJsZSA9IGZhbHNlLFxuICBwYWRkaW5nID0gJ21kJyxcbiAgc2hhZG93ID0gJ21kJ1xufSkgPT4ge1xuICBjb25zdCBwYWRkaW5nQ2xhc3NlcyA9IHtcbiAgICBzbTogJ3AtNCcsXG4gICAgbWQ6ICdwLTYnLFxuICAgIGxnOiAncC04J1xuICB9O1xuXG4gIGNvbnN0IHNoYWRvd0NsYXNzZXMgPSB7XG4gICAgc206ICdzaGFkb3ctc20nLFxuICAgIG1kOiAnc2hhZG93LW1kJyxcbiAgICBsZzogJ3NoYWRvdy1sZycsXG4gICAgeGw6ICdzaGFkb3cteGwnXG4gIH07XG5cbiAgY29uc3QgYmFzZUNsYXNzZXMgPSBgXG4gICAgYmctd2hpdGUgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFxuICAgICR7cGFkZGluZ0NsYXNzZXNbcGFkZGluZ119XG4gICAgJHtzaGFkb3dDbGFzc2VzW3NoYWRvd119XG4gICAgJHtob3ZlcmFibGUgPyAnaG92ZXI6c2hhZG93LXhsIGhvdmVyOnNjYWxlLTEwNSBjdXJzb3ItcG9pbnRlcicgOiAnJ31cbiAgICAke29uQ2xpY2sgPyAnY3Vyc29yLXBvaW50ZXInIDogJyd9XG4gICAgJHtjbGFzc05hbWV9XG4gIGA7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YmFzZUNsYXNzZXN9IG9uQ2xpY2s9e29uQ2xpY2t9PlxuICAgICAgeyh0aXRsZSB8fCBzdWJ0aXRsZSkgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICB7dGl0bGUgJiYgKFxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTFcIj5cbiAgICAgICAgICAgICAge3RpdGxlfVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICApfVxuICAgICAgICAgIHtzdWJ0aXRsZSAmJiAoXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAge3N1YnRpdGxlfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENhcmQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDYXJkIiwiY2hpbGRyZW4iLCJ0aXRsZSIsInN1YnRpdGxlIiwiY2xhc3NOYW1lIiwib25DbGljayIsImhvdmVyYWJsZSIsInBhZGRpbmciLCJzaGFkb3ciLCJwYWRkaW5nQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInNoYWRvd0NsYXNzZXMiLCJ4bCIsImJhc2VDbGFzc2VzIiwiZGl2IiwiaDMiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/localStorage.ts":
/*!***********************************!*\
  !*** ./src/utils/localStorage.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageManager: () => (/* binding */ localStorageManager),\n/* harmony export */   searchUtils: () => (/* binding */ searchUtils)\n/* harmony export */ });\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    STUDENTS: 'school_students',\n    TEACHERS: 'school_teachers',\n    EMPLOYEES: 'school_employees',\n    CLASSES: 'school_classes',\n    SUBJECTS: 'school_subjects',\n    GRADES: 'school_grades',\n    ATTENDANCE: 'school_attendance',\n    USERS: 'school_users',\n    SETTINGS: 'school_settings',\n    EVENTS: 'school_events',\n    NOTIFICATIONS: 'school_notifications',\n    CURRENT_USER: 'school_current_user',\n    ACADEMIC_YEAR: 'school_academic_year',\n    CURRENT_SEMESTER: 'school_current_semester'\n};\n// فئة إدارة التخزين المحلي\nclass LocalStorageManager {\n    // حفظ البيانات\n    setItem(key, data) {\n        try {\n            const serializedData = JSON.stringify(data);\n            localStorage.setItem(key, serializedData);\n        } catch (error) {\n            console.error(`خطأ في حفظ البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // استرجاع البيانات\n    getItem(key) {\n        try {\n            const serializedData = localStorage.getItem(key);\n            if (serializedData === null) {\n                return null;\n            }\n            return JSON.parse(serializedData);\n        } catch (error) {\n            console.error(`خطأ في استرجاع البيانات للمفتاح ${key}:`, error);\n            return null;\n        }\n    }\n    // حذف البيانات\n    removeItem(key) {\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(`خطأ في حذف البيانات للمفتاح ${key}:`, error);\n        }\n    }\n    // مسح جميع البيانات\n    clearAll() {\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            this.removeItem(key);\n        });\n    }\n    // === إدارة الطلاب ===\n    getStudents() {\n        return this.getItem(STORAGE_KEYS.STUDENTS) || [];\n    }\n    saveStudents(students) {\n        this.setItem(STORAGE_KEYS.STUDENTS, students);\n    }\n    addStudent(student) {\n        const students = this.getStudents();\n        students.push(student);\n        this.saveStudents(students);\n    }\n    updateStudent(studentId, updatedStudent) {\n        const students = this.getStudents();\n        const index = students.findIndex((s)=>s.id === studentId);\n        if (index !== -1) {\n            students[index] = {\n                ...students[index],\n                ...updatedStudent,\n                updatedAt: new Date()\n            };\n            this.saveStudents(students);\n        }\n    }\n    deleteStudent(studentId) {\n        const students = this.getStudents();\n        const filteredStudents = students.filter((s)=>s.id !== studentId);\n        this.saveStudents(filteredStudents);\n    }\n    getStudentById(studentId) {\n        const students = this.getStudents();\n        return students.find((s)=>s.id === studentId) || null;\n    }\n    // === إدارة المعلمين ===\n    getTeachers() {\n        return this.getItem(STORAGE_KEYS.TEACHERS) || [];\n    }\n    saveTeachers(teachers) {\n        this.setItem(STORAGE_KEYS.TEACHERS, teachers);\n    }\n    addTeacher(teacher) {\n        const teachers = this.getTeachers();\n        teachers.push(teacher);\n        this.saveTeachers(teachers);\n    }\n    updateTeacher(teacherId, updatedTeacher) {\n        const teachers = this.getTeachers();\n        const index = teachers.findIndex((t)=>t.id === teacherId);\n        if (index !== -1) {\n            teachers[index] = {\n                ...teachers[index],\n                ...updatedTeacher,\n                updatedAt: new Date()\n            };\n            this.saveTeachers(teachers);\n        }\n    }\n    deleteTeacher(teacherId) {\n        const teachers = this.getTeachers();\n        const filteredTeachers = teachers.filter((t)=>t.id !== teacherId);\n        this.saveTeachers(filteredTeachers);\n    }\n    getTeacherById(teacherId) {\n        const teachers = this.getTeachers();\n        return teachers.find((t)=>t.id === teacherId) || null;\n    }\n    // === إدارة الموظفين ===\n    getEmployees() {\n        return this.getItem(STORAGE_KEYS.EMPLOYEES) || [];\n    }\n    saveEmployees(employees) {\n        this.setItem(STORAGE_KEYS.EMPLOYEES, employees);\n    }\n    addEmployee(employee) {\n        const employees = this.getEmployees();\n        employees.push(employee);\n        this.saveEmployees(employees);\n    }\n    updateEmployee(employeeId, updatedEmployee) {\n        const employees = this.getEmployees();\n        const index = employees.findIndex((e)=>e.id === employeeId);\n        if (index !== -1) {\n            employees[index] = {\n                ...employees[index],\n                ...updatedEmployee,\n                updatedAt: new Date()\n            };\n            this.saveEmployees(employees);\n        }\n    }\n    deleteEmployee(employeeId) {\n        const employees = this.getEmployees();\n        const filteredEmployees = employees.filter((e)=>e.id !== employeeId);\n        this.saveEmployees(filteredEmployees);\n    }\n    getEmployeeById(employeeId) {\n        const employees = this.getEmployees();\n        return employees.find((e)=>e.id === employeeId) || null;\n    }\n    // === إدارة الصفوف ===\n    getClasses() {\n        return this.getItem(STORAGE_KEYS.CLASSES) || [];\n    }\n    saveClasses(classes) {\n        this.setItem(STORAGE_KEYS.CLASSES, classes);\n    }\n    addClass(classData) {\n        const classes = this.getClasses();\n        classes.push(classData);\n        this.saveClasses(classes);\n    }\n    updateClass(classId, updatedClass) {\n        const classes = this.getClasses();\n        const index = classes.findIndex((c)=>c.id === classId);\n        if (index !== -1) {\n            classes[index] = {\n                ...classes[index],\n                ...updatedClass,\n                updatedAt: new Date()\n            };\n            this.saveClasses(classes);\n        }\n    }\n    deleteClass(classId) {\n        const classes = this.getClasses();\n        const filteredClasses = classes.filter((c)=>c.id !== classId);\n        this.saveClasses(filteredClasses);\n    }\n    getClassById(classId) {\n        const classes = this.getClasses();\n        return classes.find((c)=>c.id === classId) || null;\n    }\n    // === إدارة المواد ===\n    getSubjects() {\n        return this.getItem(STORAGE_KEYS.SUBJECTS) || [];\n    }\n    saveSubjects(subjects) {\n        this.setItem(STORAGE_KEYS.SUBJECTS, subjects);\n    }\n    addSubject(subject) {\n        const subjects = this.getSubjects();\n        subjects.push(subject);\n        this.saveSubjects(subjects);\n    }\n    updateSubject(subjectId, updatedSubject) {\n        const subjects = this.getSubjects();\n        const index = subjects.findIndex((s)=>s.id === subjectId);\n        if (index !== -1) {\n            subjects[index] = {\n                ...subjects[index],\n                ...updatedSubject,\n                updatedAt: new Date()\n            };\n            this.saveSubjects(subjects);\n        }\n    }\n    deleteSubject(subjectId) {\n        const subjects = this.getSubjects();\n        const filteredSubjects = subjects.filter((s)=>s.id !== subjectId);\n        this.saveSubjects(filteredSubjects);\n    }\n    getSubjectById(subjectId) {\n        const subjects = this.getSubjects();\n        return subjects.find((s)=>s.id === subjectId) || null;\n    }\n    // === إدارة الدرجات ===\n    getGrades() {\n        return this.getItem(STORAGE_KEYS.GRADES) || [];\n    }\n    saveGrades(grades) {\n        this.setItem(STORAGE_KEYS.GRADES, grades);\n    }\n    addGrade(grade) {\n        const grades = this.getGrades();\n        grades.push(grade);\n        this.saveGrades(grades);\n    }\n    updateGrade(gradeId, updatedGrade) {\n        const grades = this.getGrades();\n        const index = grades.findIndex((g)=>g.id === gradeId);\n        if (index !== -1) {\n            grades[index] = {\n                ...grades[index],\n                ...updatedGrade,\n                updatedAt: new Date()\n            };\n            this.saveGrades(grades);\n        }\n    }\n    deleteGrade(gradeId) {\n        const grades = this.getGrades();\n        const filteredGrades = grades.filter((g)=>g.id !== gradeId);\n        this.saveGrades(filteredGrades);\n    }\n    getGradesByStudent(studentId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.studentId === studentId);\n    }\n    getGradesByClass(classId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.classId === classId);\n    }\n    // === إدارة الحضور ===\n    getAttendance() {\n        return this.getItem(STORAGE_KEYS.ATTENDANCE) || [];\n    }\n    saveAttendance(attendance) {\n        this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);\n    }\n    addAttendance(attendance) {\n        const attendanceRecords = this.getAttendance();\n        attendanceRecords.push(attendance);\n        this.saveAttendance(attendanceRecords);\n    }\n    updateAttendance(attendanceId, updatedAttendance) {\n        const attendanceRecords = this.getAttendance();\n        const index = attendanceRecords.findIndex((a)=>a.id === attendanceId);\n        if (index !== -1) {\n            attendanceRecords[index] = {\n                ...attendanceRecords[index],\n                ...updatedAttendance,\n                updatedAt: new Date()\n            };\n            this.saveAttendance(attendanceRecords);\n        }\n    }\n    getAttendanceByStudent(studentId) {\n        const attendance = this.getAttendance();\n        return attendance.filter((a)=>a.studentId === studentId);\n    }\n    getAttendanceByDate(date) {\n        const attendance = this.getAttendance();\n        const dateString = date.toDateString();\n        return attendance.filter((a)=>new Date(a.date).toDateString() === dateString);\n    }\n    // === إدارة المستخدمين ===\n    getUsers() {\n        return this.getItem(STORAGE_KEYS.USERS) || [];\n    }\n    saveUsers(users) {\n        this.setItem(STORAGE_KEYS.USERS, users);\n    }\n    getCurrentUser() {\n        return this.getItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    setCurrentUser(user) {\n        this.setItem(STORAGE_KEYS.CURRENT_USER, user);\n    }\n    logout() {\n        this.removeItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    // === إدارة الإعدادات ===\n    getSettings() {\n        return this.getItem(STORAGE_KEYS.SETTINGS);\n    }\n    saveSettings(settings) {\n        this.setItem(STORAGE_KEYS.SETTINGS, settings);\n    }\n    // === إدارة الأحداث ===\n    getEvents() {\n        return this.getItem(STORAGE_KEYS.EVENTS) || [];\n    }\n    saveEvents(events) {\n        this.setItem(STORAGE_KEYS.EVENTS, events);\n    }\n    addEvent(event) {\n        const events = this.getEvents();\n        events.push(event);\n        this.saveEvents(events);\n    }\n    // === إدارة الإشعارات ===\n    getNotifications() {\n        return this.getItem(STORAGE_KEYS.NOTIFICATIONS) || [];\n    }\n    saveNotifications(notifications) {\n        this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);\n    }\n    addNotification(notification) {\n        const notifications = this.getNotifications();\n        notifications.push(notification);\n        this.saveNotifications(notifications);\n    }\n    markNotificationAsRead(notificationId) {\n        const notifications = this.getNotifications();\n        const index = notifications.findIndex((n)=>n.id === notificationId);\n        if (index !== -1) {\n            notifications[index].isRead = true;\n            notifications[index].readAt = new Date();\n            this.saveNotifications(notifications);\n        }\n    }\n}\n// إنشاء مثيل واحد للاستخدام في التطبيق\nconst localStorageManager = new LocalStorageManager();\n// دوال مساعدة للبحث والتصفية\nconst searchUtils = {\n    searchStudents: (query, students)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return students.filter((student)=>student.name.toLowerCase().includes(lowercaseQuery) || student.studentId.toLowerCase().includes(lowercaseQuery) || student.email?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchTeachers: (query, teachers)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return teachers.filter((teacher)=>teacher.name?.toLowerCase().includes(lowercaseQuery) || teacher.fullName?.toLowerCase().includes(lowercaseQuery) || teacher.teacherId?.toLowerCase().includes(lowercaseQuery) || teacher.serialNumber?.toLowerCase().includes(lowercaseQuery) || teacher.email?.toLowerCase().includes(lowercaseQuery) || teacher.specialization?.toLowerCase().includes(lowercaseQuery));\n    },\n    searchEmployees: (query, employees)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return employees.filter((employee)=>employee.name?.toLowerCase().includes(lowercaseQuery) || employee.fullName?.toLowerCase().includes(lowercaseQuery) || employee.employeeId?.toLowerCase().includes(lowercaseQuery) || employee.serialNumber?.toLowerCase().includes(lowercaseQuery) || employee.email?.toLowerCase().includes(lowercaseQuery) || employee.department?.toLowerCase().includes(lowercaseQuery) || employee.position?.toLowerCase().includes(lowercaseQuery));\n    },\n    filterStudentsByClass: (classId, students)=>{\n        return students.filter((student)=>student.classId === classId);\n    },\n    filterGradesByDateRange: (startDate, endDate, grades)=>{\n        return grades.filter((grade)=>{\n            const gradeDate = new Date(grade.examDate);\n            return gradeDate >= startDate && gradeDate <= endDate;\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/localStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/sampleData.ts":
/*!*********************************!*\
  !*** ./src/utils/sampleData.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeveloperAccount: () => (/* binding */ createDeveloperAccount),\n/* harmony export */   initializeSampleData: () => (/* binding */ initializeSampleData),\n/* harmony export */   resetSampleData: () => (/* binding */ resetSampleData)\n/* harmony export */ });\n/* harmony import */ var _localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./localStorage */ \"(ssr)/./src/utils/localStorage.ts\");\n\n// إنشاء بيانات تجريبية للنظام\nconst initializeSampleData = ()=>{\n    // التحقق من وجود بيانات مسبقة\n    const existingUsers = _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.getUsers();\n    if (existingUsers.length > 0) {\n        return; // البيانات موجودة بالفعل\n    }\n    createSampleData();\n};\n// إعادة تعيين البيانات التجريبية (حذف البيانات الموجودة وإنشاء بيانات جديدة)\nconst resetSampleData = ()=>{\n    // مسح جميع البيانات الموجودة\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.clearAll();\n    // إنشاء البيانات التجريبية الجديدة\n    createSampleData();\n    console.log('تم إعادة تعيين البيانات التجريبية بنجاح');\n};\n// دالة منفصلة لإنشاء البيانات\nconst createSampleData = ()=>{\n    // إنشاء المستخدمين\n    const users = [\n        {\n            id: 'user-1',\n            name: 'عبيدة العيثاوي',\n            username: 'obeida',\n            email: '<EMAIL>',\n            password: '12345',\n            role: 'developer',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'user-2',\n            name: 'admin',\n            username: 'admin',\n            email: '<EMAIL>',\n            password: 'admin',\n            role: 'admin',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المواد الدراسية\n    const subjects = [\n        {\n            id: 'subject-1',\n            name: 'الرياضيات',\n            code: 'MATH101',\n            description: 'مادة الرياضيات للصف الأول',\n            grade: 1,\n            credits: 4,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-2',\n            name: 'اللغة العربية',\n            code: 'ARAB101',\n            description: 'مادة اللغة العربية للصف الأول',\n            grade: 1,\n            credits: 5,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-3',\n            name: 'العلوم',\n            code: 'SCI101',\n            description: 'مادة العلوم للصف الأول',\n            grade: 1,\n            credits: 3,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-4',\n            name: 'التاريخ',\n            code: 'HIST101',\n            description: 'مادة التاريخ للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'subject-5',\n            name: 'الجغرافيا',\n            code: 'GEO101',\n            description: 'مادة الجغرافيا للصف الأول',\n            grade: 1,\n            credits: 2,\n            type: 'core',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء المعلمين (بيانات تجريبية)\n    const teachers = [\n        {\n            id: 'teacher-1',\n            serialNumber: '001',\n            fullName: 'فاطمة أحمد محمد علي',\n            shortName: 'زينب حسن محمد',\n            title: 'أستاذة',\n            specialization: 'الرياضيات',\n            firstAppointmentDate: new Date('2018-09-01'),\n            schoolStartDate: new Date('2020-09-01'),\n            dateOfBirth: new Date('1985-05-15'),\n            address: 'بغداد - الكرادة - شارع الجامعة',\n            phone: '07901234567',\n            employmentType: 'permanent',\n            status: 'active',\n            subjects: [\n                'subject-1'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '001',\n            name: 'فاطمة أحمد محمد',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس رياضيات',\n            experience: 8,\n            salary: 800000,\n            hireDate: new Date('2020-09-01')\n        },\n        {\n            id: 'teacher-2',\n            serialNumber: '002',\n            fullName: 'محمد علي حسن الأستاذ',\n            shortName: 'فاطمة أحمد علي',\n            title: 'أستاذ',\n            specialization: 'اللغة العربية',\n            firstAppointmentDate: new Date('2016-09-01'),\n            schoolStartDate: new Date('2018-09-01'),\n            dateOfBirth: new Date('1980-03-20'),\n            address: 'بغداد - الجادرية - المنطقة الثانية',\n            phone: '07801234567',\n            employmentType: 'assignment',\n            status: 'active',\n            subjects: [\n                'subject-2'\n            ],\n            classes: [\n                'class-1'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '002',\n            name: 'محمد علي حسن الأستاذ',\n            email: '<EMAIL>',\n            gender: 'male',\n            qualification: 'ماجستير لغة عربية',\n            experience: 12,\n            salary: 900000,\n            hireDate: new Date('2018-09-01')\n        },\n        {\n            id: 'teacher-3',\n            serialNumber: '003',\n            fullName: 'سارة خالد أحمد المدرسة',\n            shortName: 'مريم حسين محمد',\n            title: 'مدرسة',\n            specialization: 'العلوم',\n            firstAppointmentDate: new Date('2021-09-01'),\n            schoolStartDate: new Date('2021-09-01'),\n            dateOfBirth: new Date('1990-08-12'),\n            address: 'بغداد - الكاظمية - حي الأطباء',\n            phone: '07701234567',\n            employmentType: 'contract',\n            status: 'active',\n            subjects: [\n                'subject-3'\n            ],\n            classes: [\n                'class-2'\n            ],\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            // للتوافق مع النظام القديم\n            teacherId: '003',\n            name: 'سارة خالد أحمد المدرسة',\n            email: '<EMAIL>',\n            gender: 'female',\n            qualification: 'بكالوريوس علوم',\n            experience: 3,\n            salary: 700000,\n            hireDate: new Date('2021-09-01')\n        }\n    ];\n    // إنشاء الصفوف\n    const classes = [\n        {\n            id: 'class-1',\n            name: 'الصف الأول أ',\n            grade: 1,\n            section: 'أ',\n            capacity: 30,\n            currentStudents: 20,\n            classTeacherId: 'teacher-1',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'class-2',\n            name: 'الصف الأول ب',\n            grade: 1,\n            section: 'ب',\n            capacity: 30,\n            currentStudents: 18,\n            classTeacherId: 'teacher-2',\n            subjects: [\n                'subject-1',\n                'subject-2',\n                'subject-3',\n                'subject-4',\n                'subject-5'\n            ],\n            schedule: [],\n            academicYear: '2024-2025',\n            status: 'active',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الطلاب (بيانات تجريبية)\n    const students = [\n        {\n            id: 'student-1',\n            studentId: 'S001',\n            name: 'علي أحمد محمد',\n            email: '<EMAIL>',\n            phone: '07701234567',\n            dateOfBirth: new Date('2012-01-15'),\n            gender: 'male',\n            address: 'بغداد - الكرادة - شارع الرشيد',\n            parentName: 'أحمد محمد علي',\n            parentPhone: '07901234567',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالب متفوق',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-2',\n            studentId: 'S002',\n            name: 'فاطمة حسن علي',\n            email: '<EMAIL>',\n            phone: '07701234568',\n            dateOfBirth: new Date('2012-03-20'),\n            gender: 'female',\n            address: 'بغداد - الجادرية - شارع الجامعة',\n            parentName: 'حسن علي محمد',\n            parentPhone: '07901234568',\n            parentEmail: '<EMAIL>',\n            classId: 'class-1',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'حساسية من الفول السوداني',\n            notes: 'طالبة نشيطة',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: 'student-3',\n            studentId: 'S003',\n            name: 'زينب محمد حسن',\n            email: '<EMAIL>',\n            phone: '07701234570',\n            dateOfBirth: new Date('2012-07-25'),\n            gender: 'female',\n            address: 'بغداد - الكاظمية - شارع الإمام',\n            parentName: 'محمد حسن علي',\n            parentPhone: '07901234570',\n            parentEmail: '<EMAIL>',\n            classId: 'class-2',\n            enrollmentDate: new Date('2024-09-01'),\n            status: 'active',\n            medicalInfo: 'لا يوجد',\n            notes: 'طالبة متميزة في اللغة العربية',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ];\n    // إنشاء الإعدادات\n    const settings = {\n        id: 'settings-1',\n        schoolName: 'مدرسة النور الابتدائية',\n        address: 'بغداد - الكرادة - شارع الرشيد',\n        phone: '07901234567',\n        email: '<EMAIL>',\n        website: 'www.alnoor-school.edu.iq',\n        academicYear: '2024-2025',\n        currentSemester: 'first',\n        gradeSystem: 'percentage',\n        attendanceRequired: true,\n        maxAbsences: 10,\n        workingDays: [\n            'sunday',\n            'monday',\n            'tuesday',\n            'wednesday',\n            'thursday'\n        ],\n        schoolStartTime: '08:00',\n        schoolEndTime: '14:00',\n        updatedAt: new Date()\n    };\n    // حفظ البيانات في localStorage\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSubjects(subjects);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveTeachers(teachers);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveClasses(classes);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveStudents(students);\n    _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveSettings(settings);\n    console.log('تم إنشاء البيانات التجريبية بنجاح');\n};\n// إنشاء حساب مطور جديد\nconst createDeveloperAccount = (username, email, password, name = 'المطور')=>{\n    const users = _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.getUsers();\n    // التحقق من عدم وجود مطور آخر\n    const existingDeveloper = users.find((u)=>u.role === 'developer');\n    if (existingDeveloper) {\n        // تحديث بيانات المطور الموجود\n        existingDeveloper.username = username;\n        existingDeveloper.email = email;\n        existingDeveloper.password = password;\n        existingDeveloper.name = name;\n        existingDeveloper.updatedAt = new Date();\n        _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n        console.log('تم تحديث بيانات المطور بنجاح');\n        return existingDeveloper;\n    } else {\n        // إنشاء مطور جديد\n        const newDeveloper = {\n            id: `user-dev-${Date.now()}`,\n            name,\n            username,\n            email,\n            password,\n            role: 'developer',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        users.push(newDeveloper);\n        _localStorage__WEBPACK_IMPORTED_MODULE_0__.localStorageManager.saveUsers(users);\n        console.log('تم إنشاء حساب المطور بنجاح');\n        return newDeveloper;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/sampleData.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freset-data%2Fpage&page=%2Freset-data%2Fpage&appPaths=%2Freset-data%2Fpage&pagePath=private-next-app-dir%2Freset-data%2Fpage.tsx&appDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5Cschool-management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();