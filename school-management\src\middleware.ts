import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// الصفحات التي لا تحتاج إلى مصادقة
const publicPaths = ['/login', '/unauthorized'];

// الصفحات المحمية وصلاحياتها
const protectedPaths = {
  '/': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/students': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/teachers': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/classes': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/subjects': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/users': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/grades': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/attendance': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/reports': ['developer', 'admin', 'teacher', 'student', 'parent'],
  '/settings': ['developer', 'admin', 'teacher', 'student', 'parent']
};

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // السماح بالوصول للصفحات العامة
  if (publicPaths.includes(pathname)) {
    return NextResponse.next();
  }

  // السماح بالوصول للملفات الثابتة
  if (pathname.startsWith('/_next') || 
      pathname.startsWith('/favicon') || 
      pathname.includes('.')) {
    return NextResponse.next();
  }

  // التحقق من وجود مستخدم مسجل دخول (في بيئة الإنتاج)
  // في هذا المثال، سنعتمد على localStorage في الجانب العميل
  // لذا سنسمح بالمرور هنا ونتحقق في المكونات

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
