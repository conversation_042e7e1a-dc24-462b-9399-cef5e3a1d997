import { Student, Teacher, Class, Subject, Grade, Attendance, User, Settings } from '@/types';
import { localStorageManager } from './localStorage';

// إنشاء بيانات تجريبية للنظام
export const initializeSampleData = () => {
  // التحقق من وجود بيانات مسبقة
  const existingUsers = localStorageManager.getUsers();
  if (existingUsers.length > 0) {
    return; // البيانات موجودة بالفعل
  }

  // إنشاء المستخدمين
  const users: User[] = [
    {
      id: 'user-1',
      name: 'عبيدة العيثاوي',
      username: 'obeida',
      email: '<EMAIL>',
      password: '12345',
      role: 'developer',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'user-2',
      name: 'admin',
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin',
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // إنشاء المواد الدراسية
  const subjects: Subject[] = [
    {
      id: 'subject-1',
      name: 'الرياضيات',
      code: 'MATH101',
      description: 'مادة الرياضيات للصف الأول',
      grade: 1,
      credits: 4,
      type: 'core',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'subject-2',
      name: 'اللغة العربية',
      code: 'ARAB101',
      description: 'مادة اللغة العربية للصف الأول',
      grade: 1,
      credits: 5,
      type: 'core',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'subject-3',
      name: 'العلوم',
      code: 'SCI101',
      description: 'مادة العلوم للصف الأول',
      grade: 1,
      credits: 3,
      type: 'core',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'subject-4',
      name: 'التاريخ',
      code: 'HIST101',
      description: 'مادة التاريخ للصف الأول',
      grade: 1,
      credits: 2,
      type: 'core',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'subject-5',
      name: 'الجغرافيا',
      code: 'GEO101',
      description: 'مادة الجغرافيا للصف الأول',
      grade: 1,
      credits: 2,
      type: 'core',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // إنشاء المعلمين (بيانات تجريبية)
  const teachers: Teacher[] = [
    {
      id: 'teacher-1',
      serialNumber: '001',
      fullName: 'فاطمة أحمد محمد علي',
      shortName: 'زينب حسن محمد',
      title: 'أستاذة',
      specialization: 'الرياضيات',
      firstAppointmentDate: new Date('2018-09-01'),
      schoolStartDate: new Date('2020-09-01'),
      dateOfBirth: new Date('1985-05-15'),
      address: 'بغداد - الكرادة - شارع الجامعة',
      phone: '07901234567',
      employmentType: 'permanent',
      status: 'active',
      subjects: ['subject-1'],
      classes: ['class-1'],
      createdAt: new Date(),
      updatedAt: new Date(),
      // للتوافق مع النظام القديم
      teacherId: '001',
      name: 'فاطمة أحمد محمد',
      email: '<EMAIL>',
      gender: 'female',
      qualification: 'بكالوريوس رياضيات',
      experience: 8,
      salary: 800000,
      hireDate: new Date('2020-09-01')
    },
    {
      id: 'teacher-2',
      serialNumber: '002',
      fullName: 'محمد علي حسن الأستاذ',
      shortName: 'فاطمة أحمد علي',
      title: 'أستاذ',
      specialization: 'اللغة العربية',
      firstAppointmentDate: new Date('2016-09-01'),
      schoolStartDate: new Date('2018-09-01'),
      dateOfBirth: new Date('1980-03-20'),
      address: 'بغداد - الجادرية - المنطقة الثانية',
      phone: '07801234567',
      employmentType: 'assignment',
      status: 'active',
      subjects: ['subject-2'],
      classes: ['class-1'],
      createdAt: new Date(),
      updatedAt: new Date(),
      // للتوافق مع النظام القديم
      teacherId: '002',
      name: 'محمد علي حسن الأستاذ',
      email: '<EMAIL>',
      gender: 'male',
      qualification: 'ماجستير لغة عربية',
      experience: 12,
      salary: 900000,
      hireDate: new Date('2018-09-01')
    },
    {
      id: 'teacher-3',
      serialNumber: '003',
      fullName: 'سارة خالد أحمد المدرسة',
      shortName: 'مريم حسين محمد',
      title: 'مدرسة',
      specialization: 'العلوم',
      firstAppointmentDate: new Date('2021-09-01'),
      schoolStartDate: new Date('2021-09-01'),
      dateOfBirth: new Date('1990-08-12'),
      address: 'بغداد - الكاظمية - حي الأطباء',
      phone: '07701234567',
      employmentType: 'contract',
      status: 'active',
      subjects: ['subject-3'],
      classes: ['class-2'],
      createdAt: new Date(),
      updatedAt: new Date(),
      // للتوافق مع النظام القديم
      teacherId: '003',
      name: 'سارة خالد أحمد المدرسة',
      email: '<EMAIL>',
      gender: 'female',
      qualification: 'بكالوريوس علوم',
      experience: 3,
      salary: 700000,
      hireDate: new Date('2021-09-01')
    }
  ];

  // إنشاء الصفوف
  const classes: Class[] = [
    {
      id: 'class-1',
      name: 'الصف الأول أ',
      grade: 1,
      section: 'أ',
      capacity: 30,
      currentStudents: 20,
      classTeacherId: 'teacher-1',
      subjects: ['subject-1', 'subject-2', 'subject-3', 'subject-4', 'subject-5'],
      schedule: [],
      academicYear: '2024-2025',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'class-2',
      name: 'الصف الأول ب',
      grade: 1,
      section: 'ب',
      capacity: 30,
      currentStudents: 18,
      classTeacherId: 'teacher-2',
      subjects: ['subject-1', 'subject-2', 'subject-3', 'subject-4', 'subject-5'],
      schedule: [],
      academicYear: '2024-2025',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // إنشاء الطلاب (بيانات تجريبية)
  const students: Student[] = [
    {
      id: 'student-1',
      studentId: 'S001',
      name: 'علي أحمد محمد',
      email: '<EMAIL>',
      phone: '07701234567',
      dateOfBirth: new Date('2012-01-15'),
      gender: 'male',
      address: 'بغداد - الكرادة - شارع الرشيد',
      parentName: 'أحمد محمد علي',
      parentPhone: '07901234567',
      parentEmail: '<EMAIL>',
      classId: 'class-1',
      enrollmentDate: new Date('2024-09-01'),
      status: 'active',
      medicalInfo: 'لا يوجد',
      notes: 'طالب متفوق',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'student-2',
      studentId: 'S002',
      name: 'فاطمة حسن علي',
      email: '<EMAIL>',
      phone: '07701234568',
      dateOfBirth: new Date('2012-03-20'),
      gender: 'female',
      address: 'بغداد - الجادرية - شارع الجامعة',
      parentName: 'حسن علي محمد',
      parentPhone: '07901234568',
      parentEmail: '<EMAIL>',
      classId: 'class-1',
      enrollmentDate: new Date('2024-09-01'),
      status: 'active',
      medicalInfo: 'حساسية من الفول السوداني',
      notes: 'طالبة نشيطة',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'student-3',
      studentId: 'S003',
      name: 'زينب محمد حسن',
      email: '<EMAIL>',
      phone: '07701234570',
      dateOfBirth: new Date('2012-07-25'),
      gender: 'female',
      address: 'بغداد - الكاظمية - شارع الإمام',
      parentName: 'محمد حسن علي',
      parentPhone: '07901234570',
      parentEmail: '<EMAIL>',
      classId: 'class-2',
      enrollmentDate: new Date('2024-09-01'),
      status: 'active',
      medicalInfo: 'لا يوجد',
      notes: 'طالبة متميزة في اللغة العربية',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // إنشاء الإعدادات
  const settings: Settings = {
    id: 'settings-1',
    schoolName: 'مدرسة النور الابتدائية',
    address: 'بغداد - الكرادة - شارع الرشيد',
    phone: '07901234567',
    email: '<EMAIL>',
    website: 'www.alnoor-school.edu.iq',
    academicYear: '2024-2025',
    currentSemester: 'first',
    gradeSystem: 'percentage',
    attendanceRequired: true,
    maxAbsences: 10,
    workingDays: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
    schoolStartTime: '08:00',
    schoolEndTime: '14:00',
    updatedAt: new Date()
  };

  // حفظ البيانات في localStorage
  localStorageManager.saveUsers(users);
  localStorageManager.saveSubjects(subjects);
  localStorageManager.saveTeachers(teachers);
  localStorageManager.saveClasses(classes);
  localStorageManager.saveStudents(students);
  localStorageManager.saveSettings(settings);

  console.log('تم إنشاء البيانات التجريبية بنجاح');
};
