'use client';

import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  title?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  type = 'button',
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  className = '',
  icon,
  iconPosition = 'right',
  title
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-lg
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-blue-500 to-purple-600 text-white
      hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500
      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5
    `,
    secondary: `
      bg-gray-100 text-gray-700 border border-gray-300
      hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500
      shadow-md hover:shadow-lg
    `,
    success: `
      bg-gradient-to-r from-green-500 to-emerald-600 text-white
      hover:from-green-600 hover:to-emerald-700 focus:ring-green-500
      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5
    `,
    danger: `
      bg-gradient-to-r from-red-500 to-pink-600 text-white
      hover:from-red-600 hover:to-pink-700 focus:ring-red-500
      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5
    `,
    warning: `
      bg-gradient-to-r from-yellow-500 to-orange-600 text-white
      hover:from-yellow-600 hover:to-orange-700 focus:ring-yellow-500
      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5
    `,
    info: `
      bg-gradient-to-r from-cyan-500 to-blue-600 text-white
      hover:from-cyan-600 hover:to-blue-700 focus:ring-cyan-500
      shadow-lg hover:shadow-xl transform hover:-translate-y-0.5
    `
  };

  const combinedClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={combinedClasses}
      title={title}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      
      {icon && iconPosition === 'right' && !loading && (
        <span className="ml-2">{icon}</span>
      )}
      
      <span>{children}</span>
      
      {icon && iconPosition === 'left' && !loading && (
        <span className="mr-2">{icon}</span>
      )}
    </button>
  );
};

export default Button;
