'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface AuthState {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
}

interface AuthActions {
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  checkAuth: () => void;
  hasRole: (roles: string[]) => boolean;
}

export const useAuth = (): AuthState & AuthActions => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = () => {
    try {
      const currentUser = localStorageManager.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (emailOrUsername: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم
      const users = localStorageManager.getUsers();
      const foundUser = users.find(u =>
        (u.email === emailOrUsername || u.username === emailOrUsername) &&
        u.password === password
      );

      if (!foundUser) {
        return { success: false, error: 'البريد الإلكتروني/اسم المستخدم أو كلمة المرور غير صحيحة' };
      }

      // تسجيل الدخول بنجاح
      localStorageManager.setCurrentUser(foundUser);
      setUser(foundUser);

      // إضافة إشعار نجاح تسجيل الدخول
      const notification = {
        id: `notification-${Date.now()}`,
        title: 'تم تسجيل الدخول بنجاح',
        message: `مرحباً ${foundUser.name}، تم تسجيل دخولك بنجاح`,
        type: 'success' as const,
        recipientId: foundUser.id,
        recipientType: 'user' as const,
        isRead: false,
        createdAt: new Date()
      };
      localStorageManager.addNotification(notification);

      return { success: true };
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      return { success: false, error: 'حدث خطأ أثناء تسجيل الدخول' };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    try {
      // إضافة إشعار تسجيل الخروج
      if (user) {
        const notification = {
          id: `notification-${Date.now()}`,
          title: 'تم تسجيل الخروج',
          message: `تم تسجيل خروجك من النظام بنجاح`,
          type: 'info' as const,
          recipientId: user.id,
          recipientType: 'user' as const,
          isRead: false,
          createdAt: new Date()
        };
        localStorageManager.addNotification(notification);
      }

      localStorageManager.logout();
      setUser(null);
      router.push('/login');
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
    }
  };

  const hasRole = (roles: string[]): boolean => {
    if (!user) return false;
    return roles.includes(user.role);
  };

  const updatePassword = async (currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!user) {
        return { success: false, error: 'المستخدم غير مسجل الدخول' };
      }

      // التحقق من كلمة المرور الحالية
      const users = localStorageManager.getUsers();
      const currentUser = users.find(u => u.id === user.id);

      if (!currentUser || currentUser.password !== currentPassword) {
        return { success: false, error: 'كلمة المرور الحالية غير صحيحة' };
      }

      // التحقق من قوة كلمة المرور الجديدة
      if (newPassword.length < 3) {
        return { success: false, error: 'كلمة المرور الجديدة قصيرة جداً (الحد الأدنى 3 أحرف)' };
      }

      if (currentPassword === newPassword) {
        return { success: false, error: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية' };
      }

      // تحديث كلمة المرور
      const updatedUsers = users.map(u =>
        u.id === user.id
          ? { ...u, password: newPassword, updatedAt: new Date() }
          : u
      );

      localStorageManager.saveUsers(updatedUsers);

      // تحديث المستخدم الحالي
      const updatedUser = { ...user, password: newPassword, updatedAt: new Date() };
      localStorageManager.setCurrentUser(updatedUser);
      setUser(updatedUser);

      // إضافة إشعار نجاح تغيير كلمة المرور
      const notification = {
        id: `notification-${Date.now()}`,
        title: 'تم تغيير كلمة المرور',
        message: 'تم تغيير كلمة المرور بنجاح',
        type: 'success' as const,
        recipientId: user.id,
        recipientType: 'user' as const,
        isRead: false,
        createdAt: new Date()
      };
      localStorageManager.addNotification(notification);

      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث كلمة المرور:', error);
      return { success: false, error: 'فشل في تحديث كلمة المرور' };
    }
  };

  return {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    logout,
    checkAuth,
    hasRole,
    updatePassword
  };
};
