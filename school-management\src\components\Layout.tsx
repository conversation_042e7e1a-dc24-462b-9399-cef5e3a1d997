'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types';
import { localStorageManager } from '@/utils/localStorage';
import { getDeveloperInfo } from '@/utils/appInfo';
import Sidebar from './Sidebar';
import Header from './Header';
import ProtectedRoute from './ProtectedRoute';

interface LayoutProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  requireAuth?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ children, allowedRoles, requireAuth = true }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // تحديد حجم الشاشة
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarOpen(false);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    // استرجاع المستخدم الحالي
    const user = localStorageManager.getCurrentUser();
    setCurrentUser(user);

    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLogout = () => {
    localStorageManager.logout();
    setCurrentUser(null);
    window.location.href = '/login';
  };

  // إذا كانت الحماية مطلوبة، استخدم ProtectedRoute
  if (requireAuth) {
    return (
      <ProtectedRoute allowedRoles={allowedRoles}>
        <LayoutContent
          currentUser={currentUser}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          isMobile={isMobile}
          toggleSidebar={toggleSidebar}
          handleLogout={handleLogout}
        >
          {children}
        </LayoutContent>
      </ProtectedRoute>
    );
  }

  // إذا لم تكن الحماية مطلوبة، اعرض المحتوى مباشرة
  return (
    <LayoutContent
      currentUser={currentUser}
      sidebarOpen={sidebarOpen}
      setSidebarOpen={setSidebarOpen}
      isMobile={isMobile}
      toggleSidebar={toggleSidebar}
      handleLogout={handleLogout}
    >
      {children}
    </LayoutContent>
  );
};

interface LayoutContentProps {
  children: React.ReactNode;
  currentUser: User | null;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  isMobile: boolean;
  toggleSidebar: () => void;
  handleLogout: () => void;
}

const LayoutContent: React.FC<LayoutContentProps> = ({
  children,
  currentUser,
  sidebarOpen,
  setSidebarOpen,
  isMobile,
  toggleSidebar,
  handleLogout
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50" dir="rtl">
      {/* الشريط الجانبي */}
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)}
        isMobile={isMobile}
        currentUser={currentUser}
      />

      {/* المحتوى الرئيسي */}
      <div className={`transition-all duration-300 ${
        sidebarOpen && !isMobile ? 'mr-64' : 'mr-0'
      }`}>
        {/* رأس الصفحة */}
        <Header 
          onToggleSidebar={toggleSidebar}
          currentUser={currentUser}
          onLogout={handleLogout}
        />

        {/* محتوى الصفحة */}
        <main className="p-4 md:p-6 lg:p-8 pb-20">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>

        {/* Footer ثابت */}
        <footer className="fixed bottom-0 right-0 bg-white/90 backdrop-blur-sm border-t border-l border-gray-200 rounded-tl-lg shadow-lg p-3 z-40">
          <div className="text-xs text-gray-600 text-right">
            <div className="font-semibold text-blue-600 mb-1">نظام إدارة المدرسة</div>
            <div className="text-gray-500">تطوير: عبيدة العيثاوي</div>
            <div className="text-gray-400">📞 07813332882</div>
          </div>
        </footer>
      </div>

      {/* طبقة تغطية للهاتف المحمول */}
      {isMobile && sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default Layout;
