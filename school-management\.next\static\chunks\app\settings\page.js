/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/settings/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRTpcXNmG2LjYp9mFINmF2K/Ysdiz2KlcXHNjaG9vbC1tYW5hZ2VtZW50XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(app-pages-browser)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1QyVEOSU4NiVEOCVCOCVEOCVBNyVEOSU4NSUyMCVEOSU4NSVEOCVBRiVEOCVCMSVEOCVCMyVEOCVBOSU1QyU1Q3NjaG9vbC1tYW5hZ2VtZW50JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDc2V0dGluZ3MlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUFvRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc2YbYuNin2YUg2YXYr9ix2LPYqVxcXFxzY2hvb2wtbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFzZhti42KfZhSDZhdiv2LHYs9ipXFxzY2hvb2wtbWFuYWdlbWVudFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/localStorage */ \"(app-pages-browser)/./src/utils/localStorage.ts\");\n/* harmony import */ var _utils_appInfo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/appInfo */ \"(app-pages-browser)/./src/utils/appInfo.ts\");\n/* harmony import */ var _components_NavigationButtons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NavigationButtons */ \"(app-pages-browser)/./src/components/NavigationButtons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const appInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_4__.getAppInfo)();\n    const developerInfo = (0,_utils_appInfo__WEBPACK_IMPORTED_MODULE_4__.getDeveloperInfo)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            loadSettings();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    const loadSettings = ()=>{\n        try {\n            const currentSettings = _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageManager.getSettings();\n            setSettings(currentSettings);\n            setLogoPreview((currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.schoolLogo) || null);\n        } catch (error) {\n            console.error('خطأ في تحميل الإعدادات:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في تحميل الإعدادات'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogoUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            if (!file.type.startsWith('image/')) {\n                setMessage({\n                    type: 'error',\n                    text: 'يرجى اختيار ملف صورة صالح'\n                });\n                return;\n            }\n            if (file.size > 2 * 1024 * 1024) {\n                setMessage({\n                    type: 'error',\n                    text: 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت'\n                });\n                return;\n            }\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setLogoPreview(result);\n                if (settings) {\n                    setSettings({\n                        ...settings,\n                        schoolLogo: result\n                    });\n                }\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const handleRemoveLogo = ()=>{\n        setLogoPreview(null);\n        if (settings) {\n            setSettings({\n                ...settings,\n                schoolLogo: undefined\n            });\n        }\n    };\n    const handleSave = async ()=>{\n        if (!settings) return;\n        setSaving(true);\n        try {\n            const updatedSettings = {\n                ...settings,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageManager.saveSettings(updatedSettings);\n            setSettings(updatedSettings);\n            setMessage({\n                type: 'success',\n                text: 'تم حفظ الإعدادات وتطبيق التغييرات بنجاح'\n            });\n            applySettingsToApp(updatedSettings);\n            setTimeout(()=>setMessage(null), 3000);\n        } catch (error) {\n            console.error('خطأ في حفظ الإعدادات:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في حفظ الإعدادات'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const applySettingsToApp = (newSettings)=>{\n        try {\n            document.title = \"\".concat(newSettings.schoolName, \" - نظام إدارة المدرسة\");\n            const sidebarTitle = document.querySelector('[data-sidebar-title]');\n            if (sidebarTitle) {\n                sidebarTitle.textContent = newSettings.schoolName;\n            }\n            window.dispatchEvent(new CustomEvent('settingsUpdated', {\n                detail: newSettings\n            }));\n            console.log('تم تطبيق الإعدادات الجديدة في التطبيق');\n        } catch (error) {\n            console.error('خطأ في تطبيق الإعدادات:', error);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        if (!settings) return;\n        setSettings({\n            ...settings,\n            [field]: value\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري تحميل الإعدادات...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!settings) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-lg\",\n                        children: \"فشل في تحميل الإعدادات\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadSettings,\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"إعادة المحاولة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationButtons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"mb-6\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl text-white\",\n                                children: \"⚙️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\",\n                            children: \"إعدادات النظام\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg mt-1\",\n                            children: \"إدارة وتخصيص إعدادات المدرسة والنظام\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-4 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 p-6 rounded-2xl shadow-lg border-l-4 \".concat(message.type === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 text-green-800' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-500 text-red-800'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 rounded-full flex items-center justify-center ml-4 \".concat(message.type === 'success' ? 'bg-green-100' : 'bg-red-100'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: message.type === 'success' ? '✅' : '❌'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-lg\",\n                                        children: message.type === 'success' ? 'تم بنجاح!' : 'خطأ!'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm opacity-90\",\n                                        children: message.text\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 xl:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center ml-4 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl text-white\",\n                                                children: \"\\uD83C\\uDFEB\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-gray-800\",\n                                                    children: \"معلومات المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"البيانات الأساسية للمؤسسة التعليمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"شعار المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-6 space-x-reverse\",\n                                                    children: [\n                                                        logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"شعار المدرسة\",\n                                                                    className: \"w-24 h-24 object-contain border-2 border-gray-200 rounded-lg bg-gray-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleRemoveLogo,\n                                                                    className: \"absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors\",\n                                                                    title: \"حذف الشعار\",\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-xl\",\n                                                                        children: \"\\uD83C\\uDFEB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: \"لا يوجد شعار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    id: \"logoUpload\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: handleLogoUpload,\n                                                                    className: \"hidden\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"logoUpload\",\n                                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2\",\n                                                                            children: \"\\uD83D\\uDCC1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        logoPreview ? 'تغيير الشعار' : 'رفع شعار'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 text-center\",\n                                                                    children: [\n                                                                        \"الحد الأقصى: 2 ميجابايت\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 46\n                                                                        }, this),\n                                                                        \"الصيغ المدعومة: JPG, PNG, GIF\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"اسم المدرسة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: settings.schoolName,\n                                                    onChange: (e)=>handleInputChange('schoolName', e.target.value),\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300\",\n                                                    placeholder: \"أدخل اسم المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"العنوان\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: settings.address,\n                                                    onChange: (e)=>handleInputChange('address', e.target.value),\n                                                    rows: 2,\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-green-100 focus:border-green-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300 resize-none\",\n                                                    placeholder: \"أدخل عنوان المدرسة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"رقم الهاتف\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"tel\",\n                                                    value: settings.phone,\n                                                    onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-purple-100 focus:border-purple-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300\",\n                                                    placeholder: \"أدخل رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-gray-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"العام الدراسي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: settings.academicYear,\n                                                    onChange: (e)=>handleInputChange('academicYear', e.target.value),\n                                                    className: \"w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-orange-100 focus:border-orange-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300\",\n                                                    placeholder: \"مثال: 2024-2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center ml-4 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl text-white\",\n                                                children: \"\\uD83D\\uDCBB\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-gray-800\",\n                                                    children: \"معلومات التطبيق والمطور\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"تفاصيل النظام ومطور التطبيق\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3 shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl text-white\",\n                                                            children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-800\",\n                                                                children: developerInfo.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm\",\n                                                                children: \"مطور النظام\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDCF1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"رقم الهاتف:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600 font-mono font-semibold\",\n                                                                children: \"07813332882\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDCBC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"التخصص:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-purple-600 font-semibold\",\n                                                                children: \"مطور تطبيقات ويب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDEE0️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"التقنيات:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-indigo-600 font-semibold\",\n                                                                children: \"React, Next.js, TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"\\uD83D\\uDCC5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"سنة التطوير:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-green-600 font-semibold\",\n                                                                children: \"2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✈️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"تلكرام:\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://t.me/ob992\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-sm text-blue-600 font-mono font-semibold hover:text-blue-800 transition-colors\",\n                                                                children: \"ob992\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center ml-3 shadow-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg text-white\",\n                                                                    children: \"\\uD83D\\uDCBB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"معلومات النظام\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm\",\n                                                                        children: \"تفاصيل التطبيق والإصدار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"إصدار النظام:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-purple-600 font-semibold\",\n                                                                        children: appInfo.version\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"آخر تحديث:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 font-semibold\",\n                                                                        children: settings.updatedAt ? new Date(settings.updatedAt).toLocaleDateString('en-GB') : 'غير محدد'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"نوع النظام:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-pink-600 font-semibold\",\n                                                                        children: \"نظام إدارة مدرسة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: \"حقوق الطبع:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 font-semibold\",\n                                                                        children: \"جميع الحقوق محفوظة 2025\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 flex justify-center space-x-6 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-2xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-semibold flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"↩️\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء التغييرات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSave,\n                            disabled: saving,\n                            className: \"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n                            children: [\n                                saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: saving ? '⏳' : '💾'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"rqIP04KjnxE7a0B/Pd0Ub1wVBMQ=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/NavigationButtons.tsx":
/*!**********************************************!*\
  !*** ./src/components/NavigationButtons.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst NavigationButtons = (param)=>{\n    let { showBackButton = true, showHomeButton = true, customBackAction, customHomeAction, className = '' } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleBack = ()=>{\n        if (customBackAction) {\n            customBackAction();\n        } else {\n            router.back();\n        }\n    };\n    const handleHome = ()=>{\n        if (customHomeAction) {\n            customHomeAction();\n        } else {\n            router.push('/');\n        }\n    };\n    if (!showBackButton && !showHomeButton) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3 space-x-reverse \".concat(className),\n        children: [\n            showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleBack,\n                className: \"flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 group\",\n                title: \"الرجوع للخلف\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg ml-2 group-hover:transform group-hover:-translate-x-1 transition-transform duration-200\",\n                        children: \"←\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"رجوع\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined),\n            showHomeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleHome,\n                className: \"flex items-center px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200 group\",\n                title: \"الرجوع للواجهة الرئيسية\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg ml-2 group-hover:transform group-hover:scale-110 transition-transform duration-200\",\n                        children: \"\\uD83C\\uDFE0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"الرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\نظام مدرسة\\\\school-management\\\\src\\\\components\\\\NavigationButtons.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationButtons, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NavigationButtons;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavigationButtons);\nvar _c;\n$RefreshReg$(_c, \"NavigationButtons\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NavigationButtons.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/localStorage */ \"(app-pages-browser)/./src/utils/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"useAuth.useEffect\"], []);\n    const checkAuth = ()=>{\n        try {\n            const currentUser = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getCurrentUser();\n            setUser(currentUser);\n        } catch (error) {\n            console.error('خطأ في التحقق من المصادقة:', error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (emailOrUsername, password)=>{\n        try {\n            setLoading(true);\n            // محاكاة تأخير الشبكة\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const foundUser = users.find((u)=>(u.email === emailOrUsername || u.username === emailOrUsername) && u.password === password);\n            if (!foundUser) {\n                return {\n                    success: false,\n                    error: 'البريد الإلكتروني/اسم المستخدم أو كلمة المرور غير صحيحة'\n                };\n            }\n            // تسجيل الدخول بنجاح\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(foundUser);\n            setUser(foundUser);\n            // إضافة إشعار نجاح تسجيل الدخول\n            const notification = {\n                id: \"notification-\".concat(Date.now()),\n                title: 'تم تسجيل الدخول بنجاح',\n                message: \"مرحباً \".concat(foundUser.name, \"، تم تسجيل دخولك بنجاح\"),\n                type: 'success',\n                recipientId: foundUser.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: 'حدث خطأ أثناء تسجيل الدخول'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        try {\n            // إضافة إشعار تسجيل الخروج\n            if (user) {\n                const notification = {\n                    id: \"notification-\".concat(Date.now()),\n                    title: 'تم تسجيل الخروج',\n                    message: \"تم تسجيل خروجك من النظام بنجاح\",\n                    type: 'info',\n                    recipientId: user.id,\n                    recipientType: 'user',\n                    isRead: false,\n                    createdAt: new Date()\n                };\n                _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            }\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.logout();\n            setUser(null);\n            router.push('/login');\n        } catch (error) {\n            console.error('خطأ في تسجيل الخروج:', error);\n        }\n    };\n    const hasRole = (roles)=>{\n        if (!user) return false;\n        return roles.includes(user.role);\n    };\n    const updatePassword = async (currentPassword, newPassword)=>{\n        try {\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'المستخدم غير مسجل الدخول'\n                };\n            }\n            // التحقق من كلمة المرور الحالية\n            const users = _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.getUsers();\n            const currentUser = users.find((u)=>u.id === user.id);\n            if (!currentUser || currentUser.password !== currentPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الحالية غير صحيحة'\n                };\n            }\n            // التحقق من قوة كلمة المرور الجديدة\n            if (newPassword.length < 3) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة قصيرة جداً (الحد الأدنى 3 أحرف)'\n                };\n            }\n            if (currentPassword === newPassword) {\n                return {\n                    success: false,\n                    error: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية'\n                };\n            }\n            // تحديث كلمة المرور\n            const updatedUsers = users.map((u)=>u.id === user.id ? {\n                    ...u,\n                    password: newPassword,\n                    updatedAt: new Date()\n                } : u);\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.saveUsers(updatedUsers);\n            // تحديث المستخدم الحالي\n            const updatedUser = {\n                ...user,\n                password: newPassword,\n                updatedAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.setCurrentUser(updatedUser);\n            setUser(updatedUser);\n            // إضافة إشعار نجاح تغيير كلمة المرور\n            const notification = {\n                id: \"notification-\".concat(Date.now()),\n                title: 'تم تغيير كلمة المرور',\n                message: 'تم تغيير كلمة المرور بنجاح',\n                type: 'success',\n                recipientId: user.id,\n                recipientType: 'user',\n                isRead: false,\n                createdAt: new Date()\n            };\n            _utils_localStorage__WEBPACK_IMPORTED_MODULE_2__.localStorageManager.addNotification(notification);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('خطأ في تحديث كلمة المرور:', error);\n            return {\n                success: false,\n                error: 'فشل في تحديث كلمة المرور'\n            };\n        }\n    };\n    return {\n        user,\n        loading,\n        isAuthenticated: !!user,\n        login,\n        logout,\n        checkAuth,\n        hasRole,\n        updatePassword\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/appInfo.ts":
/*!******************************!*\
  !*** ./src/utils/appInfo.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_INFO: () => (/* binding */ APP_INFO),\n/* harmony export */   getAppInfo: () => (/* binding */ getAppInfo),\n/* harmony export */   getDeveloperInfo: () => (/* binding */ getDeveloperInfo),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo)\n/* harmony export */ });\n// معلومات التطبيق والمطور\nconst APP_INFO = {\n    name: 'نظام إدارة المدرسة',\n    version: '1.0.0',\n    description: 'نظام شامل لإدارة المدارس باللغة العربية',\n    developer: 'عبيدة العيثاوي',\n    developedBy: 'تم التطوير بواسطة عبيدة العيثاوي',\n    copyright: \"\\xa9 \".concat(new Date().getFullYear(), \" عبيدة العيثاوي - جميع الحقوق محفوظة\"),\n    features: [\n        'إدارة الطلاب والمعلمين',\n        'إدارة الصفوف والمواد الدراسية',\n        'نظام التقييم والدرجات',\n        'تقارير شاملة ومفصلة',\n        'واجهة عربية متجاوبة',\n        'تصميم نيومورفيك عصري'\n    ],\n    technologies: [\n        'Next.js 15',\n        'React 19',\n        'TypeScript',\n        'Tailwind CSS',\n        'localStorage'\n    ],\n    contact: {\n        developer: 'عبيدة العيثاوي',\n        email: '<EMAIL>',\n        phone: '07813332882'\n    }\n};\n// دالة للحصول على معلومات التطبيق\nconst getAppInfo = ()=>APP_INFO;\n// دالة للحصول على معلومات المطور\nconst getDeveloperInfo = ()=>({\n        name: APP_INFO.developer,\n        developedBy: APP_INFO.developedBy,\n        copyright: APP_INFO.copyright,\n        contact: APP_INFO.contact\n    });\n// دالة للحصول على معلومات الإصدار\nconst getVersionInfo = ()=>({\n        version: APP_INFO.version,\n        name: APP_INFO.name,\n        description: APP_INFO.description\n    });\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/appInfo.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/localStorage.ts":
/*!***********************************!*\
  !*** ./src/utils/localStorage.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageManager: () => (/* binding */ localStorageManager),\n/* harmony export */   searchUtils: () => (/* binding */ searchUtils)\n/* harmony export */ });\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    STUDENTS: 'school_students',\n    TEACHERS: 'school_teachers',\n    EMPLOYEES: 'school_employees',\n    CLASSES: 'school_classes',\n    SUBJECTS: 'school_subjects',\n    GRADES: 'school_grades',\n    ATTENDANCE: 'school_attendance',\n    USERS: 'school_users',\n    SETTINGS: 'school_settings',\n    EVENTS: 'school_events',\n    NOTIFICATIONS: 'school_notifications',\n    CURRENT_USER: 'school_current_user',\n    ACADEMIC_YEAR: 'school_academic_year',\n    CURRENT_SEMESTER: 'school_current_semester'\n};\n// فئة إدارة التخزين المحلي\nclass LocalStorageManager {\n    // حفظ البيانات\n    setItem(key, data) {\n        try {\n            const serializedData = JSON.stringify(data);\n            localStorage.setItem(key, serializedData);\n        } catch (error) {\n            console.error(\"خطأ في حفظ البيانات للمفتاح \".concat(key, \":\"), error);\n        }\n    }\n    // استرجاع البيانات\n    getItem(key) {\n        try {\n            const serializedData = localStorage.getItem(key);\n            if (serializedData === null) {\n                return null;\n            }\n            return JSON.parse(serializedData);\n        } catch (error) {\n            console.error(\"خطأ في استرجاع البيانات للمفتاح \".concat(key, \":\"), error);\n            return null;\n        }\n    }\n    // حذف البيانات\n    removeItem(key) {\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(\"خطأ في حذف البيانات للمفتاح \".concat(key, \":\"), error);\n        }\n    }\n    // مسح جميع البيانات\n    clearAll() {\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            this.removeItem(key);\n        });\n    }\n    // === إدارة الطلاب ===\n    getStudents() {\n        return this.getItem(STORAGE_KEYS.STUDENTS) || [];\n    }\n    saveStudents(students) {\n        this.setItem(STORAGE_KEYS.STUDENTS, students);\n    }\n    addStudent(student) {\n        const students = this.getStudents();\n        students.push(student);\n        this.saveStudents(students);\n    }\n    updateStudent(studentId, updatedStudent) {\n        const students = this.getStudents();\n        const index = students.findIndex((s)=>s.id === studentId);\n        if (index !== -1) {\n            students[index] = {\n                ...students[index],\n                ...updatedStudent,\n                updatedAt: new Date()\n            };\n            this.saveStudents(students);\n        }\n    }\n    deleteStudent(studentId) {\n        const students = this.getStudents();\n        const filteredStudents = students.filter((s)=>s.id !== studentId);\n        this.saveStudents(filteredStudents);\n    }\n    getStudentById(studentId) {\n        const students = this.getStudents();\n        return students.find((s)=>s.id === studentId) || null;\n    }\n    // === إدارة المعلمين ===\n    getTeachers() {\n        return this.getItem(STORAGE_KEYS.TEACHERS) || [];\n    }\n    saveTeachers(teachers) {\n        this.setItem(STORAGE_KEYS.TEACHERS, teachers);\n    }\n    addTeacher(teacher) {\n        const teachers = this.getTeachers();\n        teachers.push(teacher);\n        this.saveTeachers(teachers);\n    }\n    updateTeacher(teacherId, updatedTeacher) {\n        const teachers = this.getTeachers();\n        const index = teachers.findIndex((t)=>t.id === teacherId);\n        if (index !== -1) {\n            teachers[index] = {\n                ...teachers[index],\n                ...updatedTeacher,\n                updatedAt: new Date()\n            };\n            this.saveTeachers(teachers);\n        }\n    }\n    deleteTeacher(teacherId) {\n        const teachers = this.getTeachers();\n        const filteredTeachers = teachers.filter((t)=>t.id !== teacherId);\n        this.saveTeachers(filteredTeachers);\n    }\n    getTeacherById(teacherId) {\n        const teachers = this.getTeachers();\n        return teachers.find((t)=>t.id === teacherId) || null;\n    }\n    // === إدارة الموظفين ===\n    getEmployees() {\n        return this.getItem(STORAGE_KEYS.EMPLOYEES) || [];\n    }\n    saveEmployees(employees) {\n        this.setItem(STORAGE_KEYS.EMPLOYEES, employees);\n    }\n    addEmployee(employee) {\n        const employees = this.getEmployees();\n        employees.push(employee);\n        this.saveEmployees(employees);\n    }\n    updateEmployee(employeeId, updatedEmployee) {\n        const employees = this.getEmployees();\n        const index = employees.findIndex((e)=>e.id === employeeId);\n        if (index !== -1) {\n            employees[index] = {\n                ...employees[index],\n                ...updatedEmployee,\n                updatedAt: new Date()\n            };\n            this.saveEmployees(employees);\n        }\n    }\n    deleteEmployee(employeeId) {\n        const employees = this.getEmployees();\n        const filteredEmployees = employees.filter((e)=>e.id !== employeeId);\n        this.saveEmployees(filteredEmployees);\n    }\n    getEmployeeById(employeeId) {\n        const employees = this.getEmployees();\n        return employees.find((e)=>e.id === employeeId) || null;\n    }\n    // === إدارة الصفوف ===\n    getClasses() {\n        return this.getItem(STORAGE_KEYS.CLASSES) || [];\n    }\n    saveClasses(classes) {\n        this.setItem(STORAGE_KEYS.CLASSES, classes);\n    }\n    addClass(classData) {\n        const classes = this.getClasses();\n        classes.push(classData);\n        this.saveClasses(classes);\n    }\n    updateClass(classId, updatedClass) {\n        const classes = this.getClasses();\n        const index = classes.findIndex((c)=>c.id === classId);\n        if (index !== -1) {\n            classes[index] = {\n                ...classes[index],\n                ...updatedClass,\n                updatedAt: new Date()\n            };\n            this.saveClasses(classes);\n        }\n    }\n    deleteClass(classId) {\n        const classes = this.getClasses();\n        const filteredClasses = classes.filter((c)=>c.id !== classId);\n        this.saveClasses(filteredClasses);\n    }\n    getClassById(classId) {\n        const classes = this.getClasses();\n        return classes.find((c)=>c.id === classId) || null;\n    }\n    // === إدارة المواد ===\n    getSubjects() {\n        return this.getItem(STORAGE_KEYS.SUBJECTS) || [];\n    }\n    saveSubjects(subjects) {\n        this.setItem(STORAGE_KEYS.SUBJECTS, subjects);\n    }\n    addSubject(subject) {\n        const subjects = this.getSubjects();\n        subjects.push(subject);\n        this.saveSubjects(subjects);\n    }\n    updateSubject(subjectId, updatedSubject) {\n        const subjects = this.getSubjects();\n        const index = subjects.findIndex((s)=>s.id === subjectId);\n        if (index !== -1) {\n            subjects[index] = {\n                ...subjects[index],\n                ...updatedSubject,\n                updatedAt: new Date()\n            };\n            this.saveSubjects(subjects);\n        }\n    }\n    deleteSubject(subjectId) {\n        const subjects = this.getSubjects();\n        const filteredSubjects = subjects.filter((s)=>s.id !== subjectId);\n        this.saveSubjects(filteredSubjects);\n    }\n    getSubjectById(subjectId) {\n        const subjects = this.getSubjects();\n        return subjects.find((s)=>s.id === subjectId) || null;\n    }\n    // === إدارة الدرجات ===\n    getGrades() {\n        return this.getItem(STORAGE_KEYS.GRADES) || [];\n    }\n    saveGrades(grades) {\n        this.setItem(STORAGE_KEYS.GRADES, grades);\n    }\n    addGrade(grade) {\n        const grades = this.getGrades();\n        grades.push(grade);\n        this.saveGrades(grades);\n    }\n    updateGrade(gradeId, updatedGrade) {\n        const grades = this.getGrades();\n        const index = grades.findIndex((g)=>g.id === gradeId);\n        if (index !== -1) {\n            grades[index] = {\n                ...grades[index],\n                ...updatedGrade,\n                updatedAt: new Date()\n            };\n            this.saveGrades(grades);\n        }\n    }\n    deleteGrade(gradeId) {\n        const grades = this.getGrades();\n        const filteredGrades = grades.filter((g)=>g.id !== gradeId);\n        this.saveGrades(filteredGrades);\n    }\n    getGradesByStudent(studentId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.studentId === studentId);\n    }\n    getGradesByClass(classId) {\n        const grades = this.getGrades();\n        return grades.filter((g)=>g.classId === classId);\n    }\n    // === إدارة الحضور ===\n    getAttendance() {\n        return this.getItem(STORAGE_KEYS.ATTENDANCE) || [];\n    }\n    saveAttendance(attendance) {\n        this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);\n    }\n    addAttendance(attendance) {\n        const attendanceRecords = this.getAttendance();\n        attendanceRecords.push(attendance);\n        this.saveAttendance(attendanceRecords);\n    }\n    updateAttendance(attendanceId, updatedAttendance) {\n        const attendanceRecords = this.getAttendance();\n        const index = attendanceRecords.findIndex((a)=>a.id === attendanceId);\n        if (index !== -1) {\n            attendanceRecords[index] = {\n                ...attendanceRecords[index],\n                ...updatedAttendance,\n                updatedAt: new Date()\n            };\n            this.saveAttendance(attendanceRecords);\n        }\n    }\n    getAttendanceByStudent(studentId) {\n        const attendance = this.getAttendance();\n        return attendance.filter((a)=>a.studentId === studentId);\n    }\n    getAttendanceByDate(date) {\n        const attendance = this.getAttendance();\n        const dateString = date.toDateString();\n        return attendance.filter((a)=>new Date(a.date).toDateString() === dateString);\n    }\n    // === إدارة المستخدمين ===\n    getUsers() {\n        return this.getItem(STORAGE_KEYS.USERS) || [];\n    }\n    saveUsers(users) {\n        this.setItem(STORAGE_KEYS.USERS, users);\n    }\n    getCurrentUser() {\n        return this.getItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    setCurrentUser(user) {\n        this.setItem(STORAGE_KEYS.CURRENT_USER, user);\n    }\n    logout() {\n        this.removeItem(STORAGE_KEYS.CURRENT_USER);\n    }\n    // === إدارة الإعدادات ===\n    getSettings() {\n        return this.getItem(STORAGE_KEYS.SETTINGS);\n    }\n    saveSettings(settings) {\n        this.setItem(STORAGE_KEYS.SETTINGS, settings);\n    }\n    // === إدارة الأحداث ===\n    getEvents() {\n        return this.getItem(STORAGE_KEYS.EVENTS) || [];\n    }\n    saveEvents(events) {\n        this.setItem(STORAGE_KEYS.EVENTS, events);\n    }\n    addEvent(event) {\n        const events = this.getEvents();\n        events.push(event);\n        this.saveEvents(events);\n    }\n    // === إدارة الإشعارات ===\n    getNotifications() {\n        return this.getItem(STORAGE_KEYS.NOTIFICATIONS) || [];\n    }\n    saveNotifications(notifications) {\n        this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);\n    }\n    addNotification(notification) {\n        const notifications = this.getNotifications();\n        notifications.push(notification);\n        this.saveNotifications(notifications);\n    }\n    markNotificationAsRead(notificationId) {\n        const notifications = this.getNotifications();\n        const index = notifications.findIndex((n)=>n.id === notificationId);\n        if (index !== -1) {\n            notifications[index].isRead = true;\n            notifications[index].readAt = new Date();\n            this.saveNotifications(notifications);\n        }\n    }\n}\n// إنشاء مثيل واحد للاستخدام في التطبيق\nconst localStorageManager = new LocalStorageManager();\n// دوال مساعدة للبحث والتصفية\nconst searchUtils = {\n    searchStudents: (query, students)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return students.filter((student)=>{\n            var _student_email;\n            return student.name.toLowerCase().includes(lowercaseQuery) || student.studentId.toLowerCase().includes(lowercaseQuery) || ((_student_email = student.email) === null || _student_email === void 0 ? void 0 : _student_email.toLowerCase().includes(lowercaseQuery));\n        });\n    },\n    searchTeachers: (query, teachers)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return teachers.filter((teacher)=>{\n            var _teacher_name, _teacher_fullName, _teacher_teacherId, _teacher_serialNumber, _teacher_email, _teacher_specialization;\n            return ((_teacher_name = teacher.name) === null || _teacher_name === void 0 ? void 0 : _teacher_name.toLowerCase().includes(lowercaseQuery)) || ((_teacher_fullName = teacher.fullName) === null || _teacher_fullName === void 0 ? void 0 : _teacher_fullName.toLowerCase().includes(lowercaseQuery)) || ((_teacher_teacherId = teacher.teacherId) === null || _teacher_teacherId === void 0 ? void 0 : _teacher_teacherId.toLowerCase().includes(lowercaseQuery)) || ((_teacher_serialNumber = teacher.serialNumber) === null || _teacher_serialNumber === void 0 ? void 0 : _teacher_serialNumber.toLowerCase().includes(lowercaseQuery)) || ((_teacher_email = teacher.email) === null || _teacher_email === void 0 ? void 0 : _teacher_email.toLowerCase().includes(lowercaseQuery)) || ((_teacher_specialization = teacher.specialization) === null || _teacher_specialization === void 0 ? void 0 : _teacher_specialization.toLowerCase().includes(lowercaseQuery));\n        });\n    },\n    searchEmployees: (query, employees)=>{\n        const lowercaseQuery = query.toLowerCase();\n        return employees.filter((employee)=>{\n            var _employee_name, _employee_fullName, _employee_employeeId, _employee_serialNumber, _employee_email, _employee_department, _employee_position;\n            return ((_employee_name = employee.name) === null || _employee_name === void 0 ? void 0 : _employee_name.toLowerCase().includes(lowercaseQuery)) || ((_employee_fullName = employee.fullName) === null || _employee_fullName === void 0 ? void 0 : _employee_fullName.toLowerCase().includes(lowercaseQuery)) || ((_employee_employeeId = employee.employeeId) === null || _employee_employeeId === void 0 ? void 0 : _employee_employeeId.toLowerCase().includes(lowercaseQuery)) || ((_employee_serialNumber = employee.serialNumber) === null || _employee_serialNumber === void 0 ? void 0 : _employee_serialNumber.toLowerCase().includes(lowercaseQuery)) || ((_employee_email = employee.email) === null || _employee_email === void 0 ? void 0 : _employee_email.toLowerCase().includes(lowercaseQuery)) || ((_employee_department = employee.department) === null || _employee_department === void 0 ? void 0 : _employee_department.toLowerCase().includes(lowercaseQuery)) || ((_employee_position = employee.position) === null || _employee_position === void 0 ? void 0 : _employee_position.toLowerCase().includes(lowercaseQuery));\n        });\n    },\n    filterStudentsByClass: (classId, students)=>{\n        return students.filter((student)=>student.classId === classId);\n    },\n    filterGradesByDateRange: (startDate, endDate, grades)=>{\n        return grades.filter((grade)=>{\n            const gradeDate = new Date(grade.examDate);\n            return gradeDate >= startDate && gradeDate <= endDate;\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/localStorage.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%86%D8%B8%D8%A7%D9%85%20%D9%85%D8%AF%D8%B1%D8%B3%D8%A9%5C%5Cschool-management%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);