'use client';

import React, { useState, useEffect } from 'react';
import { Teacher, Subject } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface ExportFunctions {
  exportIndividualPDF: (teacher: Teacher) => void;
  exportIndividualWord: (teacher: Teacher) => void;
  exportIndividualHTML: (teacher: Teacher) => void;
  isExporting: boolean;
}

interface AddTeacherModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  editingTeacher?: Teacher | null;
  exportFunctions?: ExportFunctions;
}

const AddTeacherModal: React.FC<AddTeacherModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingTeacher,
  exportFunctions
}) => {
  const [formData, setFormData] = useState<Partial<Teacher>>({
    fullName: '',
    title: '',
    specialization: '',
    firstAppointmentDate: new Date(),
    schoolStartDate: new Date(),
    dateOfBirth: new Date(),
    address: '',
    phone: '',
    employmentType: 'permanent',
    status: 'active',
    subjects: [],
    classes: []
  });

  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      loadSubjects();
      if (editingTeacher) {
        setFormData({
          ...editingTeacher,
          dateOfBirth: new Date(editingTeacher.dateOfBirth),
          firstAppointmentDate: new Date(editingTeacher.firstAppointmentDate),
          schoolStartDate: new Date(editingTeacher.schoolStartDate)
        });
      } else {
        resetForm();
      }
    }
  }, [isOpen, editingTeacher]);

  const loadSubjects = () => {
    const subjectsData = localStorageManager.getSubjects();
    console.log('تحميل المواد للمعلم:', subjectsData); // للتأكد من تحميل المواد
    setSubjects(subjectsData);
  };

  const resetForm = () => {
    setFormData({
      fullName: '',
      title: '',
      specialization: '',
      firstAppointmentDate: new Date(),
      schoolStartDate: new Date(),
      dateOfBirth: new Date(),
      address: '',
      phone: '',
      employmentType: 'permanent',
      status: 'active',
      subjects: [],
      classes: []
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.fullName?.trim()) {
      newErrors.fullName = 'الاسم الرباعي مطلوب';
    } else {
      // التحقق من عدم تكرار اسم المعلم
      const existingTeachers = localStorageManager.getTeachers();
      const isDuplicate = existingTeachers.some(t =>
        t.id !== editingTeacher?.id && // استثناء المعلم الحالي عند التعديل
        t.fullName.toLowerCase().trim() === formData.fullName!.toLowerCase().trim()
      );

      if (isDuplicate) {
        newErrors.fullName = 'اسم المعلم موجود مسبقاً';
      }
    }

    if (!formData.title?.trim()) {
      newErrors.title = 'اللقب مطلوب';
    }

    if (!formData.specialization?.trim()) {
      newErrors.specialization = 'الاختصاص مطلوب';
    }

    if (!formData.address?.trim()) {
      newErrors.address = 'عنوان السكن مطلوب';
    }

    if (!formData.phone?.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    } else if (!/^07[0-9]{9}$/.test(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 07 ويحتوي على 11 رقم)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // توليد التسلسل تلقائياً للمعلمين الجدد
      let serialNumber = formData.serialNumber;
      if (!editingTeacher) {
        const existingTeachers = localStorageManager.getTeachers();
        const maxSerial = existingTeachers.reduce((max, teacher) => {
          const currentSerial = parseInt(teacher.serialNumber || teacher.teacherId || '0');
          return currentSerial > max ? currentSerial : max;
        }, 0);
        serialNumber = String(maxSerial + 1).padStart(3, '0');
      }

      const teacherData: Teacher = {
        id: editingTeacher?.id || `teacher-${Date.now()}`,
        serialNumber: serialNumber!,
        fullName: formData.fullName!,
        shortName: formData.shortName,
        title: formData.title!,
        specialization: formData.specialization!,
        firstAppointmentDate: formData.firstAppointmentDate!,
        schoolStartDate: formData.schoolStartDate!,
        dateOfBirth: formData.dateOfBirth!,
        address: formData.address!,
        phone: formData.phone!,
        employmentType: formData.employmentType!,
        status: formData.status!,
        subjects: formData.subjects!,
        classes: formData.classes!,
        createdAt: editingTeacher?.createdAt || new Date(),
        updatedAt: new Date(),
        // للتوافق مع النظام القديم
        teacherId: serialNumber,
        name: formData.shortName || formData.fullName
      };

      if (editingTeacher) {
        localStorageManager.updateTeacher(editingTeacher.id, teacherData);
      } else {
        localStorageManager.addTeacher(teacherData);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ المعلم:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Teacher, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // إزالة رسالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubjectToggle = (subjectId: string) => {
    const currentSubjects = formData.subjects || [];
    const newSubjects = currentSubjects.includes(subjectId)
      ? currentSubjects.filter(id => id !== subjectId)
      : [...currentSubjects, subjectId];
    
    handleInputChange('subjects', newSubjects);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <h2 className="text-xl font-bold text-gray-900">
                {editingTeacher ? 'تعديل المعلم' : 'إضافة معلم جديد'}
              </h2>

              {/* أزرار التصدير - تظهر فقط عند التعديل */}
              {editingTeacher && exportFunctions && (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="text-sm text-gray-600">تصدير التقرير:</span>
                  <button
                    type="button"
                    onClick={() => exportFunctions.exportIndividualPDF(editingTeacher)}
                    disabled={exportFunctions.isExporting}
                    className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50 flex items-center space-x-1 space-x-reverse"
                    title="تصدير PDF"
                  >
                    <span>📄</span>
                    <span>PDF</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => exportFunctions.exportIndividualWord(editingTeacher)}
                    disabled={exportFunctions.isExporting}
                    className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50 flex items-center space-x-1 space-x-reverse"
                    title="تصدير Word"
                  >
                    <span>📝</span>
                    <span>Word</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => exportFunctions.exportIndividualHTML(editingTeacher)}
                    disabled={exportFunctions.isExporting}
                    className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 disabled:opacity-50 flex items-center space-x-1 space-x-reverse"
                    title="تصدير HTML"
                  >
                    <span>🌐</span>
                    <span>HTML</span>
                  </button>
                </div>
              )}
            </div>

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 1. الاسم الرباعي واللقب */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات الشخصية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* الاسم الرباعي */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم الرباعي *
                  </label>
                  <input
                    type="text"
                    value={formData.fullName || ''}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.fullName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="أدخل الاسم الرباعي"
                  />
                  {errors.fullName && (
                    <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>
                  )}
                </div>

                {/* اللقب */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اللقب *
                  </label>
                  <input
                    type="text"
                    value={formData.title || ''}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.title ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="أستاذ، مدرس، معلم..."
                  />
                  {errors.title && (
                    <p className="text-red-500 text-xs mt-1">{errors.title}</p>
                  )}
                </div>
              </div>
            </div>

            {/* 2. اسم الأم الثلاثي */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم الأم الثلاثي
              </label>
              <input
                type="text"
                value={formData.shortName || ''}
                onChange={(e) => handleInputChange('shortName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="اسم الأم الثلاثي"
              />
              <p className="text-xs text-gray-500 mt-1">اسم الأم مطلوب للوثائق الرسمية</p>
            </div>

            {/* 3. تاريخ التولد */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ التولد
              </label>
              <input
                type="date"
                value={formData.dateOfBirth ? formData.dateOfBirth.toISOString().split('T')[0] : ''}
                onChange={(e) => handleInputChange('dateOfBirth', new Date(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 4. رقم الهاتف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                رقم الهاتف *
              </label>
              <input
                type="tel"
                value={formData.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="07901234567"
              />
              {errors.phone && (
                <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
              )}
            </div>

            {/* 5. الاختصاص */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الاختصاص *
              </label>
              <input
                type="text"
                value={formData.specialization || ''}
                onChange={(e) => handleInputChange('specialization', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.specialization ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="الرياضيات، العلوم، اللغة العربية..."
              />
              {errors.specialization && (
                <p className="text-red-500 text-xs mt-1">{errors.specialization}</p>
              )}
            </div>

            {/* 6. تاريخ أول تعيين */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ أول تعيين
              </label>
              <input
                type="date"
                value={formData.firstAppointmentDate ? formData.firstAppointmentDate.toISOString().split('T')[0] : ''}
                onChange={(e) => handleInputChange('firstAppointmentDate', new Date(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 7. تاريخ المباشرة في المدرسة الحالية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ المباشرة في المدرسة الحالية
              </label>
              <input
                type="date"
                value={formData.schoolStartDate ? formData.schoolStartDate.toISOString().split('T')[0] : ''}
                onChange={(e) => handleInputChange('schoolStartDate', new Date(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* عنوان السكن */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                عنوان السكن *
              </label>
              <textarea
                value={formData.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.address ? 'border-red-500' : 'border-gray-300'
                }`}
                rows={2}
                placeholder="أدخل عنوان السكن الكامل"
              />
              {errors.address && (
                <p className="text-red-500 text-xs mt-1">{errors.address}</p>
              )}
            </div>

            {/* 8. الدروس التي بعهدته */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الدروس التي بعهدته (اختياري)
              </label>
              {subjects.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 p-4 bg-gray-50 rounded-lg border">
                  {subjects.map((subject) => (
                    <label key={subject.id} className="flex items-center space-x-2 space-x-reverse cursor-pointer hover:bg-gray-100 p-2 rounded">
                      <input
                        type="checkbox"
                        checked={(formData.subjects || []).includes(subject.id)}
                        onChange={() => handleSubjectToggle(subject.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{subject.name}</span>
                    </label>
                  ))}
                </div>
              ) : (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
                  <p className="text-yellow-700 text-sm">
                    لا توجد مواد متاحة. يرجى إضافة المواد أولاً من قسم "إدارة المواد"
                  </p>
                </div>
              )}
              <p className="text-xs text-gray-500 mt-1">
                اختر المواد التي سيقوم المعلم بتدريسها
              </p>
            </div>

            {/* 9. نوع الموظف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                نوع الموظف
              </label>
              <select
                value={formData.employmentType || 'permanent'}
                onChange={(e) => handleInputChange('employmentType', e.target.value as 'permanent' | 'assignment' | 'contract')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="permanent">دائم</option>
                <option value="assignment">تنسيب</option>
                <option value="contract">عقد</option>
              </select>
            </div>
            
            <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                )}
                {loading ? 'جاري الحفظ...' : (editingTeacher ? 'تحديث' : 'إضافة')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddTeacherModal;
