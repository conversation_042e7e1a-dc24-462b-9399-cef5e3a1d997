'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/Button';
import { useAuth } from '@/hooks/useAuth';
import { initializeSampleData } from '@/utils/sampleData';
import { getDeveloperInfo, getAppInfo } from '@/utils/appInfo';
import { localStorageManager } from '@/utils/localStorage';
import { User } from '@/types';

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  // إزالة وضع إنشاء الحسابات - متاح للمطور فقط من صفحة إدارة المستخدمين
  const [isDeveloperMode, setIsDeveloperMode] = useState(false);

  // حقول إنشاء الحساب الجديد
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();
  const { login, loading, isAuthenticated } = useAuth();

  // معلومات المطور والتطبيق
  const developerInfo = getDeveloperInfo();
  const appInfo = getAppInfo();

  useEffect(() => {
    // تحميل البيانات التجريبية إذا لم تكن موجودة
    initializeSampleData();

    // التحقق من وجود مستخدم مسجل دخول مسبقاً
    if (isAuthenticated) {
      router.push('/');
    }

    // التحقق من وضع المطور من URL
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('developer') === 'true') {
      setIsDeveloperMode(true);
    }
  }, [isAuthenticated, router]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    const result = await login(username, password);

    if (result.success) {
      // حفظ حالة "تذكرني" إذا كانت مفعلة
      if (rememberMe) {
        localStorage.setItem('rememberLogin', 'true');
      }

      // التحقق من آخر تسجيل دخول
      const lastLogin = localStorage.getItem('lastLogin');
      const isFirstLogin = !lastLogin;

      // حفظ وقت تسجيل الدخول الحالي
      localStorage.setItem('lastLogin', new Date().toISOString());

      // الانتقال إلى صفحة الترحيب للمستخدمين الجدد أو الصفحة الرئيسية
      if (isFirstLogin) {
        router.push('/welcome');
      } else {
        router.push('/');
      }
    } else {
      setError(result.error || 'حدث خطأ أثناء تسجيل الدخول');
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // التحقق من صحة البيانات
    if (!fullName.trim()) {
      setError('يرجى إدخال الاسم الكامل');
      return;
    }

    if (!email.trim()) {
      setError('يرجى إدخال البريد الإلكتروني');
      return;
    }

    if (!username.trim()) {
      setError('يرجى إدخال اسم المستخدم');
      return;
    }

    if (password.length < 4) {
      setError('كلمة المرور يجب أن تكون 4 أحرف على الأقل');
      return;
    }

    if (password !== confirmPassword) {
      setError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
      return;
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUsers = localStorageManager.getUsers();
    const userExists = existingUsers.some(u => u.email === email || u.name === username);

    if (userExists) {
      setError('المستخدم موجود بالفعل، يرجى استخدام بريد إلكتروني أو اسم مستخدم مختلف');
      return;
    }

    try {
      // إنشاء المستخدم الجديد
      const newUser: User = {
        id: `user-${Date.now()}`,
        name: fullName,
        email: email,
        password: password,
        role: 'student', // الدور الافتراضي
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // حفظ المستخدم الجديد
      const updatedUsers = [...existingUsers, newUser];
      localStorageManager.saveUsers(updatedUsers);

      // تسجيل دخول تلقائي
      localStorageManager.setCurrentUser(newUser);

      // إضافة إشعار ترحيب
      const notification = {
        id: `notification-${Date.now()}`,
        title: 'مرحباً بك في النظام',
        message: `تم إنشاء حسابك بنجاح، مرحباً ${fullName}`,
        type: 'success' as const,
        recipientId: newUser.id,
        recipientType: 'user' as const,
        isRead: false,
        createdAt: new Date()
      };
      localStorageManager.addNotification(notification);

      // الانتقال إلى صفحة الترحيب
      router.push('/welcome');
    } catch (error) {
      console.error('خطأ في إنشاء الحساب:', error);
      setError('حدث خطأ أثناء إنشاء الحساب');
    }
  };

  const resetForm = () => {
    setUsername('');
    setPassword('');
    setFullName('');
    setEmail('');
    setConfirmPassword('');
    setError('');
    setRememberMe(false);
  };

  // تم إزالة وضع إنشاء الحسابات

  // const demoAccounts = [
  //   { username: 'admin', role: 'مدير النظام', name: 'عبيدة العيثاوي' },
  //   { username: 'teacher', role: 'معلمة', name: 'فاطمة أحمد المعلمة' },
  //   { username: 'student', role: 'طالب', name: 'علي أحمد محمد' },
  //   { username: 'parent', role: 'ولي أمر', name: 'أحمد محمد علي' }
  // ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4" dir="rtl">
      {/* خلفية متحركة */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-br from-green-400 to-blue-600 rounded-full opacity-10 animate-pulse delay-500"></div>
      </div>

      <div className="relative w-full max-w-6xl">
        {/* بطاقة تسجيل الدخول الأفقية */}
        <div className="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20">
          <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">

            {/* القسم الأيسر - معلومات المدرسة */}
            <div className="bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 p-8 lg:p-12 flex flex-col justify-center items-center text-white relative overflow-hidden">
              {/* خلفية زخرفية */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-10 right-10 w-32 h-32 border-2 border-white rounded-full"></div>
                <div className="absolute bottom-10 left-10 w-24 h-24 border-2 border-white rounded-full"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-white rounded-full"></div>
              </div>

              <div className="relative z-10 text-center">
                {/* شعار المدرسة */}
                <div className="w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <span className="text-white text-4xl">🏫</span>
                </div>

                <h1 className="text-3xl lg:text-4xl font-bold mb-4">نظام إدارة المدرسة</h1>
                <p className="text-lg lg:text-xl mb-6 text-white/90">
                  نظام شامل لإدارة المدارس باللغة العربية
                </p>

                <div className="space-y-4 text-white/80 mb-8">
                  <div className="flex items-center justify-center">
                    <span className="ml-2">✨</span>
                    <span>إدارة متكاملة للطلاب والمعلمين</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <span className="ml-2">📊</span>
                    <span>تقارير مفصلة ومتقدمة</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <span className="ml-2">🔒</span>
                    <span>أمان وحماية عالية للبيانات</span>
                  </div>
                </div>

                {/* معلومات المطور - أفقي */}
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                  <div className="flex items-center justify-between">
                    {/* معلومات المطور الأساسية */}
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center ml-3">
                        <span className="text-white text-xl">👨‍💻</span>
                      </div>
                      <div>
                        <h4 className="text-white font-bold text-sm">{appInfo.contact.developer}</h4>
                        <p className="text-white/80 text-xs">مطور النظام</p>
                      </div>
                    </div>

                    {/* معلومات الاتصال */}
                    <div className="text-left">
                      <div className="flex items-center mb-1">
                        <span className="text-white/80 text-xs ml-2">📱</span>
                        <span className="text-white text-xs font-medium">{appInfo.contact.phone}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-white/80 text-xs ml-2">✈️</span>
                        <span className="text-white/80 text-xs">@ob992</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* القسم الأيمن - نموذج تسجيل الدخول */}
            <div className="p-8 lg:p-12 flex flex-col justify-center">
              <div className="max-w-md mx-auto w-full">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    مرحباً بك
                  </h2>
                  <p className="text-gray-600">
                    يرجى تسجيل الدخول للمتابعة
                  </p>
                </div>

                {/* نموذج تسجيل الدخول */}
                <form onSubmit={handleLogin} className="space-y-6">




                  {/* حقل البريد الإلكتروني أو اسم المستخدم */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني أو اسم المستخدم
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        placeholder="أدخل البريد الإلكتروني أو اسم المستخدم"
                        required
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* حقل كلمة المرور */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        placeholder="أدخل كلمة المرور"
                        required
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="hover:text-gray-600 transition-colors"
                        >
                          {showPassword ? (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>



                  {/* خيارات إضافية */}
                  <div className="flex items-center justify-between">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="mr-2 text-sm text-gray-600">تذكرني</span>
                    </label>
                    <button
                      type="button"
                      className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      نسيت كلمة المرور؟
                    </button>
                  </div>

                  {/* رسالة الخطأ */}
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-red-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-sm text-red-700">{error}</span>
                      </div>
                    </div>
                  )}

                  {/* زر الإرسال */}
                  <Button
                    type="submit"
                    variant="primary"
                    loading={loading}
                    fullWidth
                    className="h-12 text-lg font-semibold"
                  >
                    {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                  </Button>
                </form>

                {/* رسالة للمستخدمين الجدد */}
                <div className="mt-6 text-center">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-800 mb-2">
                      <span className="font-medium">للحصول على حساب جديد:</span>
                    </p>
                    <p className="text-xs text-blue-700">
                      يرجى التواصل مع المطور لإنشاء حساب جديد
                    </p>
                    <p className="text-xs text-blue-600 mt-2">
                      📱 {appInfo.contact.phone} | ✈️ @ob992
                    </p>
                  </div>
                </div>


              </div>
            </div>
          </div>
        </div>

        {/* رابط إعادة تعيين البيانات */}
        <div className="text-center mt-6">
          <a
            href="/reset-data"
            className="text-sm text-red-600 hover:text-red-800 underline"
          >
            نسيت بيانات تسجيل الدخول؟ إعادة تعيين البيانات
          </a>
        </div>

        {/* تذييل بسيط */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            جميع الحقوق محفوظة © 2025 - نظام إدارة المدرسة
          </p>
        </div>
      </div>
    </div>
  );
}
