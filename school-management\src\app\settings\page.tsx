'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { localStorageManager } from '@/utils/localStorage';
import { Settings } from '@/types';
import { getAppInfo, getDeveloperInfo } from '@/utils/appInfo';
import NavigationButtons from '@/components/NavigationButtons';

export default function SettingsPage() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const appInfo = getAppInfo();
  const developerInfo = getDeveloperInfo();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    try {
      const currentSettings = localStorageManager.getSettings();
      setSettings(currentSettings);
      setLogoPreview(currentSettings?.schoolLogo || null);
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error);
      setMessage({ type: 'error', text: 'فشل في تحميل الإعدادات' });
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setMessage({ type: 'error', text: 'يرجى اختيار ملف صورة صالح' });
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        setMessage({ type: 'error', text: 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت' });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        if (settings) {
          setSettings({
            ...settings,
            schoolLogo: result
          });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => {
    setLogoPreview(null);
    if (settings) {
      setSettings({
        ...settings,
        schoolLogo: undefined
      });
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const updatedSettings = {
        ...settings,
        updatedAt: new Date()
      };

      localStorageManager.saveSettings(updatedSettings);
      setSettings(updatedSettings);
      setMessage({ type: 'success', text: 'تم حفظ الإعدادات وتطبيق التغييرات بنجاح' });

      applySettingsToApp(updatedSettings);
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      setMessage({ type: 'error', text: 'فشل في حفظ الإعدادات' });
    } finally {
      setSaving(false);
    }
  };

  const applySettingsToApp = (newSettings: Settings) => {
    try {
      document.title = `${newSettings.schoolName} - نظام إدارة المدرسة`;
      const sidebarTitle = document.querySelector('[data-sidebar-title]');
      if (sidebarTitle) {
        sidebarTitle.textContent = newSettings.schoolName;
      }
      window.dispatchEvent(new CustomEvent('settingsUpdated', {
        detail: newSettings
      }));
      console.log('تم تطبيق الإعدادات الجديدة في التطبيق');
    } catch (error) {
      console.error('خطأ في تطبيق الإعدادات:', error);
    }
  };

  const handleInputChange = (field: keyof Settings, value: any) => {
    if (!settings) return;
    
    setSettings({
      ...settings,
      [field]: value
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 text-lg">فشل في تحميل الإعدادات</p>
          <button 
            onClick={loadSettings}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="p-6 max-w-7xl mx-auto">
        <NavigationButtons className="mb-6" />

        <div className="mb-8 text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
            <span className="text-3xl text-white">⚙️</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            إعدادات النظام
          </h1>
          <p className="text-gray-600 text-lg mt-1">إدارة وتخصيص إعدادات المدرسة والنظام</p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-4 rounded-full"></div>
        </div>

        {message && (
          <div className={`mb-8 p-6 rounded-2xl shadow-lg border-l-4 ${
            message.type === 'success' 
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 text-green-800' 
              : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-500 text-red-800'
          }`}>
            <div className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ml-4 ${
                message.type === 'success' ? 'bg-green-100' : 'bg-red-100'
              }`}>
                <span className="text-xl">
                  {message.type === 'success' ? '✅' : '❌'}
                </span>
              </div>
              <div>
                <h3 className="font-semibold text-lg">
                  {message.type === 'success' ? 'تم بنجاح!' : 'خطأ!'}
                </h3>
                <p className="text-sm opacity-90">{message.text}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center ml-4 shadow-lg">
                <span className="text-xl text-white">🏫</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-800">معلومات المدرسة</h2>
                <p className="text-gray-500 text-sm">البيانات الأساسية للمؤسسة التعليمية</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  شعار المدرسة
                </label>
                <div className="flex items-center justify-center space-x-6 space-x-reverse">
                  {logoPreview ? (
                    <div className="relative">
                      <img
                        src={logoPreview}
                        alt="شعار المدرسة"
                        className="w-24 h-24 object-contain border-2 border-gray-200 rounded-lg bg-gray-50"
                      />
                      <button
                        onClick={handleRemoveLogo}
                        className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                        title="حذف الشعار"
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                      <div className="text-center">
                        <span className="text-gray-400 text-xl">🏫</span>
                        <p className="text-xs text-gray-500 mt-1">لا يوجد شعار</p>
                      </div>
                    </div>
                  )}

                  <div className="flex flex-col items-center">
                    <input
                      type="file"
                      id="logoUpload"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <label
                      htmlFor="logoUpload"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors mb-2"
                    >
                      <span className="ml-2">📁</span>
                      {logoPreview ? 'تغيير الشعار' : 'رفع شعار'}
                    </label>
                    <p className="text-xs text-gray-500 text-center">
                      الحد الأقصى: 2 ميجابايت<br />
                      الصيغ المدعومة: JPG, PNG, GIF
                    </p>
                  </div>
                </div>
              </div>

              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                  اسم المدرسة
                </label>
                <input
                  type="text"
                  value={settings.schoolName}
                  onChange={(e) => handleInputChange('schoolName', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300"
                  placeholder="أدخل اسم المدرسة"
                />
              </div>

              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full ml-2"></span>
                  العنوان
                </label>
                <textarea
                  value={settings.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-green-100 focus:border-green-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300 resize-none"
                  placeholder="أدخل عنوان المدرسة"
                />
              </div>

              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <span className="w-2 h-2 bg-purple-500 rounded-full ml-2"></span>
                  رقم الهاتف
                </label>
                <input
                  type="tel"
                  value={settings.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-purple-100 focus:border-purple-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300"
                  placeholder="أدخل رقم الهاتف"
                />
              </div>

              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <span className="w-2 h-2 bg-orange-500 rounded-full ml-2"></span>
                  العام الدراسي
                </label>
                <input
                  type="text"
                  value={settings.academicYear}
                  onChange={(e) => handleInputChange('academicYear', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-orange-100 focus:border-orange-500 transition-all duration-200 bg-gray-50 focus:bg-white group-hover:border-gray-300"
                  placeholder="مثال: 2024-2025"
                />
              </div>
            </div>
          </div>

          {/* معلومات التطبيق والمطور */}
          <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center ml-4 shadow-lg">
                <span className="text-xl text-white">💻</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-800">معلومات التطبيق والمطور</h2>
                <p className="text-gray-500 text-sm">تفاصيل النظام ومطور التطبيق</p>
              </div>
            </div>

            <div className="space-y-4">
              {/* بطاقة المطور الرئيسية */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3 shadow-lg">
                    <span className="text-xl text-white">👨‍💻</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">{developerInfo.name}</h3>
                    <p className="text-gray-600 text-sm">مطور النظام</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                    <span className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="ml-2">📱</span>
                      رقم الهاتف:
                    </span>
                    <span className="text-sm text-blue-600 font-mono font-semibold">07813332882</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                    <span className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="ml-2">💼</span>
                      التخصص:
                    </span>
                    <span className="text-sm text-purple-600 font-semibold">مطور تطبيقات ويب</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                    <span className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="ml-2">🛠️</span>
                      التقنيات:
                    </span>
                    <span className="text-sm text-indigo-600 font-semibold">React, Next.js, TypeScript</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                    <span className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="ml-2">📅</span>
                      سنة التطوير:
                    </span>
                    <span className="text-sm text-green-600 font-semibold">2025</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                    <span className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="ml-2">✈️</span>
                      تلكرام:
                    </span>
                    <a
                      href="https://t.me/ob992"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 font-mono font-semibold hover:text-blue-800 transition-colors"
                    >
                      ob992
                    </a>
                  </div>
                </div>

                {/* معلومات النظام داخل نفس القسم */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center ml-3 shadow-lg">
                      <span className="text-lg text-white">💻</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-gray-800">معلومات النظام</h4>
                      <p className="text-gray-600 text-sm">تفاصيل التطبيق والإصدار</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                      <span className="text-sm font-medium text-gray-700">إصدار النظام:</span>
                      <span className="text-sm text-purple-600 font-semibold">{appInfo.version}</span>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                      <span className="text-sm font-medium text-gray-700">آخر تحديث:</span>
                      <span className="text-sm text-gray-600 font-semibold">
                        {settings.updatedAt ? new Date(settings.updatedAt).toLocaleDateString('en-GB') : 'غير محدد'}
                      </span>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                      <span className="text-sm font-medium text-gray-700">نوع النظام:</span>
                      <span className="text-sm text-pink-600 font-semibold">نظام إدارة مدرسة</span>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border border-gray-200">
                      <span className="text-sm font-medium text-gray-700">حقوق الطبع:</span>
                      <span className="text-xs text-gray-500 font-semibold">جميع الحقوق محفوظة 2025</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 flex justify-center space-x-6 space-x-reverse">
          <button
            onClick={() => window.location.reload()}
            className="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-2xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-semibold flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            <span className="ml-2">↩️</span>
            إلغاء التغييرات
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            {saving && (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
            )}
            <span className="ml-2">{saving ? '⏳' : '💾'}</span>
            {saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
          </button>
        </div>
      </div>
    </div>
  );
}
