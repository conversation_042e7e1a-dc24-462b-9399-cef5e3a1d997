# نظام إدارة المدرسة 🏫

نظام شامل لإدارة المدارس باللغة العربية مع دعم RTL وتصميم نيومورفيك عصري.

## ✨ المميزات

### 🌟 المميزات الأساسية
- **واجهة عربية كاملة** مع دعم RTL (من اليمين إلى اليسار)
- **تصميم نيومورفيك عصري** بظلال ناعمة وتدرجات لونية دقيقة
- **تصميم متجاوب** يعمل على جميع الأجهزة (كمبيوتر، تابلت، هاتف)
- **العمل بدون اتصال** باستخدام localStorage
- **واجهة مستخدم بديهية** وسهلة الاستخدام

### 📚 وحدات النظام
1. **إدارة الطلاب** - إضافة وتعديل وإدارة بيانات الطلاب
2. **إدارة المعلمين** - إدارة بيانات المعلمين والموظفين
3. **إدارة الصفوف** - تنظيم الصفوف الدراسية والشعب
4. **إدارة المواد** - إدارة المواد الدراسية والمناهج
5. **إدارة الدرجات** - تسجيل ومتابعة درجات الطلاب
6. **إدارة الحضور** - تسجيل ومتابعة حضور الطلاب
7. **التقارير** - إنشاء تقارير شاملة ومفصلة
8. **الإعدادات** - إعدادات النظام والمدرسة

## 🚀 التقنيات المستخدمة

- **Next.js 15** - إطار عمل React للتطبيقات الحديثة
- **TypeScript** - للكتابة الآمنة والموثوقة
- **Tailwind CSS** - للتصميم السريع والمرن
- **React Hooks** - لإدارة الحالة والتفاعل
- **localStorage** - للتخزين المحلي والعمل بدون اتصال

## 📦 التثبيت والتشغيل

### متطلبات النظام
- Node.js 18+
- npm أو yarn أو pnpm

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd school-management
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **تشغيل المشروع**
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

4. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## 🔧 طرق التشغيل البديلة

### الطريقة الأولى: ملفات التشغيل المباشر (Windows)

#### تشغيل بملف Batch:
```bash
# انقر نقراً مزدوجاً على الملف
start.bat
```

#### تشغيل بملف PowerShell:
```bash
# انقر بالزر الأيمن واختر "Run with PowerShell"
.\start.ps1
```

### الطريقة الثانية: حل مشاكل المسار

إذا واجهت مشكلة "directory does not exist":

```bash
# تأكد من المسار الصحيح
cd /d "C:\path\to\school-management"

# أو استخدم المسار المطلق
npm run dev --prefix "C:\path\to\school-management"
```

### الطريقة الثالثة: تشغيل الإنتاج

```bash
# بناء المشروع
npm run build

# تشغيل الإنتاج
npm run start

# أو الاثنين معاً
npm run start:prod
```

### حل المشاكل الشائعة

#### مشكلة المكتبات:
```bash
# إعادة تثبيت المكتبات
npm run reinstall

# تنظيف الملفات المؤقتة
npm run clean
```

#### مشكلة البورت:
```bash
# تشغيل على بورت مختلف
npm run dev -- --port 3001
```

## 🎨 التصميم والواجهة

### نظام الألوان
- **الأساسي**: تدرجات الأزرق والبنفسجي
- **الثانوي**: ألوان متنوعة للحالات المختلفة
- **الخلفية**: تدرجات ناعمة مع شفافية

### الخطوط
- **الخط الأساسي**: Cairo - خط عربي عصري وواضح
- **الأحجام**: متدرجة ومتناسقة مع التصميم

### التصميم النيومورفيك
- ظلال ناعمة ثلاثية الأبعاد
- تأثيرات تفاعلية عند التمرير
- انتقالات سلسة ومريحة للعين

## 📱 التوافق مع الأجهزة

- **أجهزة الكمبيوتر**: دعم كامل مع جميع المميزات
- **الأجهزة اللوحية**: واجهة محسنة للمس
- **الهواتف الذكية**: تصميم متجاوب مع قوائم منسدلة

## 💾 إدارة البيانات

### التخزين المحلي
- جميع البيانات تُحفظ في localStorage
- إمكانية العمل بدون اتصال بالإنترنت
- نسخ احتياطي تلقائي للبيانات

### أنواع البيانات
- بيانات الطلاب والمعلمين
- الصفوف والمواد الدراسية
- الدرجات وسجلات الحضور
- الإعدادات والتفضيلات

## 🔧 الإعدادات والتخصيص

### إعدادات المدرسة
- اسم المدرسة وشعارها
- العنوان ومعلومات الاتصال
- السنة الدراسية والفصل الحالي
- نظام الدرجات والتقييم

### إعدادات النظام
- أيام العمل وساعات الدوام
- الحد الأقصى للغياب
- متطلبات الحضور
- إعدادات الإشعارات

## 📊 التقارير والإحصائيات

### تقارير الطلاب
- تقارير الدرجات الفردية
- تقارير الحضور والغياب
- تقارير الأداء الأكاديمي

### تقارير الصفوف
- إحصائيات الصف العامة
- متوسط الدرجات
- معدلات الحضور

### تقارير المعلمين
- أداء المعلمين
- الصفوف والمواد المُدرَّسة
- إحصائيات التدريس

## 🛡️ الأمان والخصوصية

- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي بيانات لخوادم خارجية
- إمكانية تصدير واستيراد البيانات
- نسخ احتياطي آمن

## 🤝 المساهمة في المشروع

نرحب بمساهماتكم لتطوير النظام:

1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة الوثائق
- التواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 👨‍💻 المطور

تم تطوير هذا النظام بواسطة **عبيدة العيثاوي**

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام ولكل من ساعد في تحسينه وتطويره.

---

**تم تطوير هذا النظام بعناية لخدمة المجتمع التعليمي العربي** 🎓
