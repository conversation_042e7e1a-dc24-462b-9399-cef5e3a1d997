'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import AddStudentModal from '@/components/AddStudentModal';
import ExportDropdown from '@/components/ExportDropdown';
import { Student, Class } from '@/types';
import { localStorageManager, searchUtils } from '@/utils/localStorage';

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [loading, setLoading] = useState(true);

  // حالة التصدير
  const [isExporting, setIsExporting] = useState(false);

  // دوال التصدير المبسطة
  const exportIndividualPDF = (student: Student) => {
    setIsExporting(true);
    // تصدير PDF بسيط
    const content = `
      تقرير الطالب: ${student.name}
      الصف: ${getClassName(student.classId)}
      الجنس: ${student.gender === 'male' ? 'ذكر' : 'أنثى'}
      ولي الأمر: ${student.parentName}
      رقم الهاتف: ${student.parentPhone}
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head><title>تقرير الطالب - ${student.name}</title></head>
          <body style="font-family: Arial; direction: rtl; padding: 20px;">
            <h1>تقرير الطالب</h1>
            <pre>${content}</pre>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
    setIsExporting(false);
  };

  const exportIndividualWord = (student: Student) => {
    exportIndividualPDF(student); // نفس الوظيفة مؤقتاً
  };

  const exportClassReport = (classId: string) => {
    setIsExporting(true);
    const classStudents = students.filter(s => s.classId === classId);
    const className = getClassName(classId);

    let content = `تقرير الصف: ${className}\n\n`;
    classStudents.forEach((student, index) => {
      content += `${index + 1}. ${student.name} - ${student.gender === 'male' ? 'ذكر' : 'أنثى'}\n`;
    });

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head><title>تقرير الصف - ${className}</title></head>
          <body style="font-family: Arial; direction: rtl; padding: 20px;">
            <h1>تقرير الصف</h1>
            <pre>${content}</pre>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
    setIsExporting(false);
  };

  const exportAllStudentsReport = () => {
    setIsExporting(true);
    let content = `تقرير جميع الطلاب\n\n`;
    students.forEach((student, index) => {
      content += `${index + 1}. ${student.name} - ${getClassName(student.classId)} - ${student.gender === 'male' ? 'ذكر' : 'أنثى'}\n`;
    });

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head><title>تقرير جميع الطلاب</title></head>
          <body style="font-family: Arial; direction: rtl; padding: 20px;">
            <h1>تقرير جميع الطلاب</h1>
            <pre>${content}</pre>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
    setIsExporting(false);
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const studentsData = localStorageManager.getStudents();
      const classesData = localStorageManager.getClasses();
      setStudents(studentsData);
      setClasses(classesData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = searchQuery === '' || 
      searchUtils.searchStudents(searchQuery, [student]).length > 0;
    const matchesClass = selectedClass === '' || student.classId === selectedClass;
    return matchesSearch && matchesClass;
  });

  const getClassName = (classId: string) => {
    const classData = classes.find(c => c.id === classId);
    return classData ? classData.name : 'غير محدد';
  };

  const handleDeleteStudent = (studentId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      localStorageManager.deleteStudent(studentId);
      loadData();
    }
  };

  const handleEditStudent = (student: Student) => {
    setEditingStudent(student);
    setShowAddModal(true);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الطلاب</h1>
            <p className="text-gray-600 mt-1">إدارة وتتبع بيانات الطلاب</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
            <Button
              variant="primary"
              onClick={() => setShowAddModal(true)}
            >
              إضافة طالب جديد
            </Button>

            <ExportDropdown
              label={isExporting ? 'جاري التصدير...' : 'تصدير تقرير جماعي'}
              disabled={isExporting || students.length === 0}
              options={[
                {
                  label: 'تصدير جميع الطلاب (Excel)',
                  icon: '📊',
                  action: exportAllStudentsReport,
                  color: 'text-green-600 hover:text-green-700'
                }
              ]}
            />
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث
              </label>
              <input
                type="text"
                placeholder="ابحث بالاسم أو رقم الطالب أو البريد الإلكتروني..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب الصف
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الصفوف</option>
                {classes.map(classItem => (
                  <option key={classItem.id} value={classItem.id}>
                    {classItem.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تقارير الصفوف
              </label>
              <ExportDropdown
                label={isExporting ? 'جاري التصدير...' : 'تصدير تقرير صف'}
                disabled={isExporting || !selectedClass}
                options={[
                  {
                    label: 'تقرير الصف المحدد (Excel)',
                    icon: '📊',
                    action: () => selectedClass && exportClassReport(selectedClass),
                    color: 'text-green-600 hover:text-green-700'
                  }
                ]}
              />
              {!selectedClass && (
                <p className="text-xs text-gray-500 mt-1">اختر صف لتصدير تقريره</p>
              )}
            </div>
          </div>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">{students.length}</div>
              <div className="text-blue-600 text-sm">إجمالي الطلاب</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">
                {students.filter(s => s.status === 'active').length}
              </div>
              <div className="text-green-600 text-sm">الطلاب النشطون</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">
                {students.filter(s => s.gender === 'male').length}
              </div>
              <div className="text-purple-600 text-sm">الطلاب الذكور</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-pink-50 to-pink-100 border-pink-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-900">
                {students.filter(s => s.gender === 'female').length}
              </div>
              <div className="text-pink-600 text-sm">الطالبات الإناث</div>
            </div>
          </Card>
        </div>

        {/* قائمة الطلاب */}
        <Card title="قائمة الطلاب" subtitle={`عدد النتائج: ${filteredStudents.length}`}>
          {filteredStudents.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">👨‍🎓</div>
              <p className="text-gray-600">لا توجد نتائج مطابقة للبحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-center py-3 px-2 font-semibold text-gray-700 w-16">ت</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الاسم</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الصف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الجنس</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">ولي الأمر</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الحالة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStudents.map((student, index) => (
                    <tr key={student.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-2 text-center text-sm font-medium text-gray-600 w-16">{index + 1}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-bold">
                              {student.name.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{student.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">{getClassName(student.classId)}</td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          student.gender === 'male' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-pink-100 text-pink-800'
                        }`}>
                          {student.gender === 'male' ? 'ذكر' : 'أنثى'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{student.parentName}</div>
                          <div className="text-sm text-gray-600">{student.parentPhone}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          student.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {student.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEditStudent(student)}
                          >
                            تعديل
                          </Button>

                          <ExportDropdown
                            label="تقرير"
                            disabled={isExporting}
                            options={[
                              {
                                label: 'تقرير PDF',
                                icon: '📄',
                                action: () => exportIndividualPDF(student),
                                color: 'text-red-600 hover:text-red-700'
                              },
                              {
                                label: 'تقرير Word',
                                icon: '📝',
                                action: () => exportIndividualWord(student),
                                color: 'text-blue-600 hover:text-blue-700'
                              },
                              {
                                label: 'تقرير الصف (Excel)',
                                icon: '📊',
                                action: () => exportClassReport(student.classId),
                                color: 'text-green-600 hover:text-green-700'
                              }
                            ]}
                          />

                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteStudent(student.id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>

        {/* مكون إضافة/تعديل الطالب */}
        <AddStudentModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setEditingStudent(null);
          }}
          onSave={loadData}
          editingStudent={editingStudent}
        />
      </div>
    </Layout>
  );
}
