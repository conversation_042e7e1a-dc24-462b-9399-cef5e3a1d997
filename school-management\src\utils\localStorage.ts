import {
  Student,
  Teacher,
  Employee,
  Class,
  Subject,
  Grade,
  Attendance,
  User,
  Settings,
  Event,
  Notification
} from '@/types';

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  STUDENTS: 'school_students',
  TEACHERS: 'school_teachers',
  EMPLOYEES: 'school_employees',
  CLASSES: 'school_classes',
  SUBJECTS: 'school_subjects',
  GRADES: 'school_grades',
  ATTENDANCE: 'school_attendance',
  USERS: 'school_users',
  SETTINGS: 'school_settings',
  EVENTS: 'school_events',
  NOTIFICATIONS: 'school_notifications',
  CURRENT_USER: 'school_current_user',
  ACADEMIC_YEAR: 'school_academic_year',
  CURRENT_SEMESTER: 'school_current_semester'
};

// فئة إدارة التخزين المحلي
class LocalStorageManager {
  // حفظ البيانات
  private setItem<T>(key: string, data: T): void {
    try {
      const serializedData = JSON.stringify(data);
      localStorage.setItem(key, serializedData);
    } catch (error) {
      console.error(`خطأ في حفظ البيانات للمفتاح ${key}:`, error);
    }
  }

  // استرجاع البيانات
  private getItem<T>(key: string): T | null {
    try {
      const serializedData = localStorage.getItem(key);
      if (serializedData === null) {
        return null;
      }
      return JSON.parse(serializedData) as T;
    } catch (error) {
      console.error(`خطأ في استرجاع البيانات للمفتاح ${key}:`, error);
      return null;
    }
  }

  // حذف البيانات
  private removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`خطأ في حذف البيانات للمفتاح ${key}:`, error);
    }
  }

  // مسح جميع البيانات
  clearAll(): void {
    Object.values(STORAGE_KEYS).forEach(key => {
      this.removeItem(key);
    });
  }

  // === إدارة الطلاب ===
  getStudents(): Student[] {
    return this.getItem<Student[]>(STORAGE_KEYS.STUDENTS) || [];
  }

  saveStudents(students: Student[]): void {
    this.setItem(STORAGE_KEYS.STUDENTS, students);
  }

  addStudent(student: Student): void {
    const students = this.getStudents();
    students.push(student);
    this.saveStudents(students);
  }

  updateStudent(studentId: string, updatedStudent: Partial<Student>): void {
    const students = this.getStudents();
    const index = students.findIndex(s => s.id === studentId);
    if (index !== -1) {
      students[index] = { ...students[index], ...updatedStudent, updatedAt: new Date() };
      this.saveStudents(students);
    }
  }

  deleteStudent(studentId: string): void {
    const students = this.getStudents();
    const filteredStudents = students.filter(s => s.id !== studentId);
    this.saveStudents(filteredStudents);
  }

  getStudentById(studentId: string): Student | null {
    const students = this.getStudents();
    return students.find(s => s.id === studentId) || null;
  }

  // === إدارة المعلمين ===
  getTeachers(): Teacher[] {
    return this.getItem<Teacher[]>(STORAGE_KEYS.TEACHERS) || [];
  }

  saveTeachers(teachers: Teacher[]): void {
    this.setItem(STORAGE_KEYS.TEACHERS, teachers);
  }

  addTeacher(teacher: Teacher): void {
    const teachers = this.getTeachers();
    teachers.push(teacher);
    this.saveTeachers(teachers);
  }

  updateTeacher(teacherId: string, updatedTeacher: Partial<Teacher>): void {
    const teachers = this.getTeachers();
    const index = teachers.findIndex(t => t.id === teacherId);
    if (index !== -1) {
      teachers[index] = { ...teachers[index], ...updatedTeacher, updatedAt: new Date() };
      this.saveTeachers(teachers);
    }
  }

  deleteTeacher(teacherId: string): void {
    const teachers = this.getTeachers();
    const filteredTeachers = teachers.filter(t => t.id !== teacherId);
    this.saveTeachers(filteredTeachers);
  }

  getTeacherById(teacherId: string): Teacher | null {
    const teachers = this.getTeachers();
    return teachers.find(t => t.id === teacherId) || null;
  }

  // === إدارة الموظفين ===
  getEmployees(): Employee[] {
    return this.getItem<Employee[]>(STORAGE_KEYS.EMPLOYEES) || [];
  }

  saveEmployees(employees: Employee[]): void {
    this.setItem(STORAGE_KEYS.EMPLOYEES, employees);
  }

  addEmployee(employee: Employee): void {
    const employees = this.getEmployees();
    employees.push(employee);
    this.saveEmployees(employees);
  }

  updateEmployee(employeeId: string, updatedEmployee: Partial<Employee>): void {
    const employees = this.getEmployees();
    const index = employees.findIndex(e => e.id === employeeId);
    if (index !== -1) {
      employees[index] = { ...employees[index], ...updatedEmployee, updatedAt: new Date() };
      this.saveEmployees(employees);
    }
  }

  deleteEmployee(employeeId: string): void {
    const employees = this.getEmployees();
    const filteredEmployees = employees.filter(e => e.id !== employeeId);
    this.saveEmployees(filteredEmployees);
  }

  getEmployeeById(employeeId: string): Employee | null {
    const employees = this.getEmployees();
    return employees.find(e => e.id === employeeId) || null;
  }

  // === إدارة الصفوف ===
  getClasses(): Class[] {
    return this.getItem<Class[]>(STORAGE_KEYS.CLASSES) || [];
  }

  saveClasses(classes: Class[]): void {
    this.setItem(STORAGE_KEYS.CLASSES, classes);
  }

  addClass(classData: Class): void {
    const classes = this.getClasses();
    classes.push(classData);
    this.saveClasses(classes);
  }

  updateClass(classId: string, updatedClass: Partial<Class>): void {
    const classes = this.getClasses();
    const index = classes.findIndex(c => c.id === classId);
    if (index !== -1) {
      classes[index] = { ...classes[index], ...updatedClass, updatedAt: new Date() };
      this.saveClasses(classes);
    }
  }

  deleteClass(classId: string): void {
    const classes = this.getClasses();
    const filteredClasses = classes.filter(c => c.id !== classId);
    this.saveClasses(filteredClasses);
  }

  getClassById(classId: string): Class | null {
    const classes = this.getClasses();
    return classes.find(c => c.id === classId) || null;
  }

  // === إدارة المواد ===
  getSubjects(): Subject[] {
    return this.getItem<Subject[]>(STORAGE_KEYS.SUBJECTS) || [];
  }

  saveSubjects(subjects: Subject[]): void {
    this.setItem(STORAGE_KEYS.SUBJECTS, subjects);
  }

  addSubject(subject: Subject): void {
    const subjects = this.getSubjects();
    subjects.push(subject);
    this.saveSubjects(subjects);
  }

  updateSubject(subjectId: string, updatedSubject: Partial<Subject>): void {
    const subjects = this.getSubjects();
    const index = subjects.findIndex(s => s.id === subjectId);
    if (index !== -1) {
      subjects[index] = { ...subjects[index], ...updatedSubject, updatedAt: new Date() };
      this.saveSubjects(subjects);
    }
  }

  deleteSubject(subjectId: string): void {
    const subjects = this.getSubjects();
    const filteredSubjects = subjects.filter(s => s.id !== subjectId);
    this.saveSubjects(filteredSubjects);
  }

  getSubjectById(subjectId: string): Subject | null {
    const subjects = this.getSubjects();
    return subjects.find(s => s.id === subjectId) || null;
  }

  // === إدارة الدرجات ===
  getGrades(): Grade[] {
    return this.getItem<Grade[]>(STORAGE_KEYS.GRADES) || [];
  }

  saveGrades(grades: Grade[]): void {
    this.setItem(STORAGE_KEYS.GRADES, grades);
  }

  addGrade(grade: Grade): void {
    const grades = this.getGrades();
    grades.push(grade);
    this.saveGrades(grades);
  }

  updateGrade(gradeId: string, updatedGrade: Partial<Grade>): void {
    const grades = this.getGrades();
    const index = grades.findIndex(g => g.id === gradeId);
    if (index !== -1) {
      grades[index] = { ...grades[index], ...updatedGrade, updatedAt: new Date() };
      this.saveGrades(grades);
    }
  }

  deleteGrade(gradeId: string): void {
    const grades = this.getGrades();
    const filteredGrades = grades.filter(g => g.id !== gradeId);
    this.saveGrades(filteredGrades);
  }

  getGradesByStudent(studentId: string): Grade[] {
    const grades = this.getGrades();
    return grades.filter(g => g.studentId === studentId);
  }

  getGradesByClass(classId: string): Grade[] {
    const grades = this.getGrades();
    return grades.filter(g => g.classId === classId);
  }

  // === إدارة الحضور ===
  getAttendance(): Attendance[] {
    return this.getItem<Attendance[]>(STORAGE_KEYS.ATTENDANCE) || [];
  }

  saveAttendance(attendance: Attendance[]): void {
    this.setItem(STORAGE_KEYS.ATTENDANCE, attendance);
  }

  addAttendance(attendance: Attendance): void {
    const attendanceRecords = this.getAttendance();
    attendanceRecords.push(attendance);
    this.saveAttendance(attendanceRecords);
  }

  updateAttendance(attendanceId: string, updatedAttendance: Partial<Attendance>): void {
    const attendanceRecords = this.getAttendance();
    const index = attendanceRecords.findIndex(a => a.id === attendanceId);
    if (index !== -1) {
      attendanceRecords[index] = { ...attendanceRecords[index], ...updatedAttendance, updatedAt: new Date() };
      this.saveAttendance(attendanceRecords);
    }
  }

  getAttendanceByStudent(studentId: string): Attendance[] {
    const attendance = this.getAttendance();
    return attendance.filter(a => a.studentId === studentId);
  }

  getAttendanceByDate(date: Date): Attendance[] {
    const attendance = this.getAttendance();
    const dateString = date.toDateString();
    return attendance.filter(a => new Date(a.date).toDateString() === dateString);
  }

  // === إدارة المستخدمين ===
  getUsers(): User[] {
    return this.getItem<User[]>(STORAGE_KEYS.USERS) || [];
  }

  saveUsers(users: User[]): void {
    this.setItem(STORAGE_KEYS.USERS, users);
  }

  getCurrentUser(): User | null {
    return this.getItem<User>(STORAGE_KEYS.CURRENT_USER);
  }

  setCurrentUser(user: User): void {
    this.setItem(STORAGE_KEYS.CURRENT_USER, user);
  }

  logout(): void {
    this.removeItem(STORAGE_KEYS.CURRENT_USER);
  }

  // === إدارة الإعدادات ===
  getSettings(): Settings | null {
    return this.getItem<Settings>(STORAGE_KEYS.SETTINGS);
  }

  saveSettings(settings: Settings): void {
    this.setItem(STORAGE_KEYS.SETTINGS, settings);
  }

  // === إدارة الأحداث ===
  getEvents(): Event[] {
    return this.getItem<Event[]>(STORAGE_KEYS.EVENTS) || [];
  }

  saveEvents(events: Event[]): void {
    this.setItem(STORAGE_KEYS.EVENTS, events);
  }

  addEvent(event: Event): void {
    const events = this.getEvents();
    events.push(event);
    this.saveEvents(events);
  }

  // === إدارة الإشعارات ===
  getNotifications(): Notification[] {
    return this.getItem<Notification[]>(STORAGE_KEYS.NOTIFICATIONS) || [];
  }

  saveNotifications(notifications: Notification[]): void {
    this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);
  }

  addNotification(notification: Notification): void {
    const notifications = this.getNotifications();
    notifications.push(notification);
    this.saveNotifications(notifications);
  }

  markNotificationAsRead(notificationId: string): void {
    const notifications = this.getNotifications();
    const index = notifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      notifications[index].isRead = true;
      notifications[index].readAt = new Date();
      this.saveNotifications(notifications);
    }
  }
}

// إنشاء مثيل واحد للاستخدام في التطبيق
export const localStorageManager = new LocalStorageManager();

// دوال مساعدة للبحث والتصفية
export const searchUtils = {
  searchStudents: (query: string, students: Student[]): Student[] => {
    const lowercaseQuery = query.toLowerCase();
    return students.filter(student => 
      student.name.toLowerCase().includes(lowercaseQuery) ||
      student.studentId.toLowerCase().includes(lowercaseQuery) ||
      student.email?.toLowerCase().includes(lowercaseQuery)
    );
  },

  searchTeachers: (query: string, teachers: Teacher[]): Teacher[] => {
    const lowercaseQuery = query.toLowerCase();
    return teachers.filter(teacher =>
      teacher.name?.toLowerCase().includes(lowercaseQuery) ||
      teacher.fullName?.toLowerCase().includes(lowercaseQuery) ||
      teacher.teacherId?.toLowerCase().includes(lowercaseQuery) ||
      teacher.serialNumber?.toLowerCase().includes(lowercaseQuery) ||
      teacher.email?.toLowerCase().includes(lowercaseQuery) ||
      teacher.specialization?.toLowerCase().includes(lowercaseQuery)
    );
  },

  searchEmployees: (query: string, employees: Employee[]): Employee[] => {
    const lowercaseQuery = query.toLowerCase();
    return employees.filter(employee =>
      employee.name?.toLowerCase().includes(lowercaseQuery) ||
      employee.fullName?.toLowerCase().includes(lowercaseQuery) ||
      employee.employeeId?.toLowerCase().includes(lowercaseQuery) ||
      employee.serialNumber?.toLowerCase().includes(lowercaseQuery) ||
      employee.email?.toLowerCase().includes(lowercaseQuery) ||
      employee.department?.toLowerCase().includes(lowercaseQuery) ||
      employee.position?.toLowerCase().includes(lowercaseQuery)
    );
  },

  filterStudentsByClass: (classId: string, students: Student[]): Student[] => {
    return students.filter(student => student.classId === classId);
  },

  filterGradesByDateRange: (startDate: Date, endDate: Date, grades: Grade[]): Grade[] => {
    return grades.filter(grade => {
      const gradeDate = new Date(grade.examDate);
      return gradeDate >= startDate && gradeDate <= endDate;
    });
  }
};
