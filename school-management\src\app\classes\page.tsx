'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import AddClassModal from '@/components/AddClassModal';
import { Class, Teacher, Student } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

export default function ClassesPage() {
  const [classes, setClasses] = useState<Class[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGrade, setSelectedGrade] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const classesData = localStorageManager.getClasses();
      const teachersData = localStorageManager.getTeachers();
      const studentsData = localStorageManager.getStudents();
      setClasses(classesData);
      setTeachers(teachersData);
      setStudents(studentsData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredClasses = classes.filter(classItem => {
    const matchesSearch = searchQuery === '' ||
      classItem.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesGrade = selectedGrade === '' ||
      classItem.grade.toString() === selectedGrade;
    return matchesSearch && matchesGrade;
  }).sort((a, b) => {
    // ترتيب حسب المرحلة أولاً، ثم حسب الشعبة
    if (a.grade !== b.grade) {
      return a.grade - b.grade;
    }
    // إذا كانت المرحلة نفسها، ترتيب حسب الشعبة أبجدياً
    return a.section.localeCompare(b.section, 'ar');
  });

  const getTeacherName = (teacherId?: string) => {
    if (!teacherId) return 'غير محدد';
    const teacher = teachers.find(t => t.id === teacherId);
    return teacher ? (teacher.fullName || teacher.name || teacher.shortName || 'غير محدد') : 'غير محدد';
  };

  const getGradeName = (grade: number) => {
    const gradeNames = {
      1: 'الأول',
      2: 'الثاني',
      3: 'الثالث',
      4: 'الرابع',
      5: 'الخامس',
      6: 'السادس'
    };
    return gradeNames[grade as keyof typeof gradeNames] || `الصف ${grade}`;
  };

  const getStudentCount = (classId: string) => {
    return students.filter(s => s.classId === classId).length;
  };

  const handleDeleteClass = (classId: string) => {
    const studentsInClass = students.filter(s => s.classId === classId);
    if (studentsInClass.length > 0) {
      alert('لا يمكن حذف الصف لأنه يحتوي على طلاب. يرجى نقل الطلاب أولاً.');
      return;
    }
    
    if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
      localStorageManager.deleteClass(classId);
      loadData();
    }
  };

  const handleEditClass = (classItem: Class) => {
    setEditingClass(classItem);
    setShowAddModal(true);
  };

  const getUniqueGrades = () => {
    const grades = classes.map(c => c.grade);
    return [...new Set(grades)].sort((a, b) => a - b);
  };

  const getCapacityStatus = (currentStudents: number, capacity: number) => {
    const percentage = (currentStudents / capacity) * 100;
    if (percentage >= 90) return { color: 'red', text: 'ممتلئ تقريباً' };
    if (percentage >= 70) return { color: 'yellow', text: 'مشغول' };
    return { color: 'green', text: 'متاح' };
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الصفوف</h1>
            <p className="text-gray-600 mt-1">إدارة وتنظيم الصفوف الدراسية</p>
          </div>
          <Button 
            variant="primary" 
            onClick={() => setShowAddModal(true)}
            className="mt-4 md:mt-0"
          >
            إضافة صف جديد
          </Button>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث
              </label>
              <input
                type="text"
                placeholder="ابحث باسم الصف..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب الصف
              </label>
              <select
                value={selectedGrade}
                onChange={(e) => setSelectedGrade(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الصفوف</option>
                {getUniqueGrades().map(grade => (
                  <option key={grade} value={grade.toString()}>
                    الصف {getGradeName(grade)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">{classes.length}</div>
              <div className="text-purple-600 text-sm">إجمالي الصفوف</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">
                {classes.filter(c => c.status === 'active').length}
              </div>
              <div className="text-blue-600 text-sm">الصفوف النشطة</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">
                {classes.reduce((sum, c) => sum + getStudentCount(c.id), 0)}
              </div>
              <div className="text-green-600 text-sm">إجمالي الطلاب</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-900">
                {Math.round(classes.reduce((sum, c) => sum + getStudentCount(c.id), 0) / classes.length) || 0}
              </div>
              <div className="text-orange-600 text-sm">متوسط الطلاب/صف</div>
            </div>
          </Card>
        </div>

        {/* قائمة الصفوف */}
        <Card title="قائمة الصفوف" subtitle={`عدد النتائج: ${filteredClasses.length}`}>
          {filteredClasses.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">🏫</div>
              <p className="text-gray-600">لا توجد نتائج مطابقة للبحث</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredClasses.map((classItem) => {
                const currentStudents = getStudentCount(classItem.id);
                const capacityStatus = getCapacityStatus(currentStudents, classItem.capacity);
                
                return (
                  <Card key={classItem.id} hoverable className="relative">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{classItem.name} - {classItem.section}</h3>
                        <p className="text-sm text-gray-600">المرحلة {classItem.grade}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        classItem.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {classItem.status === 'active' ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">معلم الصف:</span>
                        <span className="text-sm font-medium">{getTeacherName(classItem.classTeacherId)}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">عدد الطلاب:</span>
                        <span className="text-sm font-medium">{currentStudents} / {classItem.capacity}</span>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            capacityStatus.color === 'red' ? 'bg-red-500' :
                            capacityStatus.color === 'yellow' ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${(currentStudents / classItem.capacity) * 100}%` }}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">الحالة:</span>
                        <span className={`text-xs font-medium ${
                          capacityStatus.color === 'red' ? 'text-red-600' :
                          capacityStatus.color === 'yellow' ? 'text-yellow-600' : 'text-green-600'
                        }`}>
                          {capacityStatus.text}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">السنة الدراسية:</span>
                        <span className="text-sm font-medium">{classItem.academicYear}</span>
                      </div>
                    </div>

                    <div className="flex space-x-2 space-x-reverse mt-4 pt-4 border-t border-gray-200">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => handleEditClass(classItem)}
                        fullWidth
                      >
                        تعديل
                      </Button>
                      <Button
                        size="sm"
                        variant="danger"
                        onClick={() => handleDeleteClass(classItem.id)}
                        fullWidth
                      >
                        حذف
                      </Button>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </Card>

        {/* مكون إضافة/تعديل الصف */}
        <AddClassModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setEditingClass(null);
          }}
          onSave={loadData}
          editingClass={editingClass}
        />
      </div>
    </Layout>
  );
}
