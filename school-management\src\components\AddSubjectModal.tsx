'use client';

import React, { useState, useEffect } from 'react';
import { Subject } from '@/types';
import { localStorageManager } from '@/utils/localStorage';

interface AddSubjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  editingSubject?: Subject | null;
}

const AddSubjectModal: React.FC<AddSubjectModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingSubject
}) => {
  const [formData, setFormData] = useState<Partial<Subject>>({
    name: '',
    grade: 0 // 0 يعني جميع الصفوف
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // المواد الأساسية المحددة مسبقاً
  const predefinedSubjects = [
    'اللغة العربية',
    'الرياضيات',
    'العلوم',
    'التربية الإسلامية',
    'التاريخ',
    'الجغرافية',
    'الاجتماعيات',
    'اللغة الإنجليزية',
    'التربية الفنية',
    'التربية الرياضية',
    'الحاسوب',
    'الموسيقى',
    'التربية المهنية'
  ];

  useEffect(() => {
    if (isOpen) {
      if (editingSubject) {
        setFormData({
          ...editingSubject
        });
      } else {
        resetForm();
      }
    }
  }, [isOpen, editingSubject]);

  const resetForm = () => {
    setFormData({
      name: '',
      grade: 0 // 0 يعني جميع الصفوف
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'اسم المادة مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const subjectData: Subject = {
        id: editingSubject?.id || `subject-${Date.now()}`,
        name: formData.name!,
        code: formData.name!.replace(/\s+/g, '').toUpperCase().substring(0, 10), // توليد رمز تلقائي
        grade: formData.grade!,
        credits: 1, // قيمة افتراضية
        type: 'core', // افتراضي أساسية
        status: 'active', // افتراضي نشط
        createdAt: editingSubject?.createdAt || new Date(),
        updatedAt: new Date()
      };

      if (editingSubject) {
        localStorageManager.updateSubject(editingSubject.id, subjectData);
      } else {
        localStorageManager.addSubject(subjectData);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ المادة:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Subject, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // إزالة رسالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const getGradeName = (grade: number) => {
    const gradeNames = {
      1: 'الأول',
      2: 'الثاني', 
      3: 'الثالث',
      4: 'الرابع',
      5: 'الخامس',
      6: 'السادس'
    };
    return gradeNames[grade as keyof typeof gradeNames] || `الصف ${grade}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900">
              {editingSubject ? 'تعديل المادة' : 'إضافة مادة جديدة'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* معلومات المادة الأساسية */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات المادة</h3>
              <div className="space-y-4">
                {/* اسم المادة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم المادة *
                  </label>
                  <select
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">اختر المادة</option>
                    {predefinedSubjects.map(subject => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                    <option value="أخرى">أخرى (مادة مخصصة)</option>
                  </select>
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name}</p>
                  )}

                  {/* حقل إدخال مخصص إذا اختار "أخرى" */}
                  {formData.name === 'أخرى' && (
                    <input
                      type="text"
                      placeholder="أدخل اسم المادة المخصصة"
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mt-2"
                    />
                  )}
                </div>



                {/* الصف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الصف
                  </label>
                  <select
                    value={formData.grade || 0}
                    onChange={(e) => handleInputChange('grade', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={0}>جميع الصفوف (افتراضي)</option>
                    <option value={1}>الصف الأول فقط</option>
                    <option value={2}>الصف الثاني فقط</option>
                    <option value={3}>الصف الثالث فقط</option>
                    <option value={4}>الصف الرابع فقط</option>
                    <option value={5}>الصف الخامس فقط</option>
                    <option value={6}>الصف السادس فقط</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    المواد الأساسية عادة تُدرس لجميع الصفوف
                  </p>
                </div>


              </div>
            </div>





            <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                )}
                {loading ? 'جاري الحفظ...' : (editingSubject ? 'تحديث' : 'إضافة')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddSubjectModal;
