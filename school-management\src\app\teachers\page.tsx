'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Card from '@/components/Card';
import Button from '@/components/Button';
import NavigationButtons from '@/components/NavigationButtons';
import AddTeacherModal from '@/components/AddTeacherModal';
import TeacherReportExport from '@/components/TeacherReportExport';
import ExportDropdown from '@/components/ExportDropdown';
import { Teacher, Subject } from '@/types';
import { localStorageManager, searchUtils } from '@/utils/localStorage';

export default function TeachersPage() {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [loading, setLoading] = useState(true);

  // استخدام مكون التصدير
  const {
    exportIndividualPDF,
    exportIndividualWord,
    exportIndividualHTML,
    exportGroupPDF,
    exportGroupExcel,
    exportGroupWord,
    exportGroupHTML,
    isExporting
  } = TeacherReportExport({ teachers, subjects });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const teachersData = localStorageManager.getTeachers();
      const subjectsData = localStorageManager.getSubjects();
      setTeachers(teachersData);
      setSubjects(subjectsData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setLoading(false);
    }
  };

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = searchQuery === '' || 
      searchUtils.searchTeachers(searchQuery, [teacher]).length > 0;
    const matchesSpecialization = selectedSpecialization === '' || 
      teacher.specialization === selectedSpecialization;
    return matchesSearch && matchesSpecialization;
  });

  const getSubjectNames = (subjectIds: string[]) => {
    return subjectIds.map(id => {
      const subject = subjects.find(s => s.id === id);
      return subject ? subject.name : 'غير محدد';
    }).join(', ');
  };

  const handleDeleteTeacher = (teacherId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المعلم؟')) {
      localStorageManager.deleteTeacher(teacherId);
      loadData();
    }
  };

  const handleEditTeacher = (teacher: Teacher) => {
    setEditingTeacher(teacher);
    setShowAddModal(true);
  };

  const getUniqueSpecializations = () => {
    const specializations = teachers.map(t => t.specialization);
    return [...new Set(specializations)];
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* أزرار التنقل */}
        <NavigationButtons />

        {/* رأس الصفحة */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المعلمين</h1>
            <p className="text-gray-600 mt-1">إدارة وتتبع بيانات المعلمين والموظفين</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
            <ExportDropdown
              label={isExporting ? 'جاري التصدير...' : 'تصدير تقرير جماعي'}
              disabled={isExporting || teachers.length === 0}
              options={[
                {
                  label: 'تصدير PDF',
                  icon: '📄',
                  action: exportGroupPDF,
                  color: 'text-red-600 hover:text-red-700'
                },
                {
                  label: 'تصدير Excel',
                  icon: '📊',
                  action: exportGroupExcel,
                  color: 'text-green-600 hover:text-green-700'
                },
                {
                  label: 'تصدير Word',
                  icon: '📝',
                  action: exportGroupWord,
                  color: 'text-blue-600 hover:text-blue-700'
                },
                {
                  label: 'تصدير HTML',
                  icon: '🌐',
                  action: exportGroupHTML,
                  color: 'text-purple-600 hover:text-purple-700'
                }
              ]}
            />
            <Button
              variant="primary"
              onClick={() => setShowAddModal(true)}
            >
              إضافة معلم جديد
            </Button>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث
              </label>
              <input
                type="text"
                placeholder="ابحث بالاسم أو رقم المعلم أو التخصص..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تصفية حسب التخصص
              </label>
              <select
                value={selectedSpecialization}
                onChange={(e) => setSelectedSpecialization(e.target.value)}
                className="input-field"
              >
                <option value="">جميع التخصصات</option>
                {getUniqueSpecializations().map(specialization => (
                  <option key={specialization} value={specialization}>
                    {specialization}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">{teachers.length}</div>
              <div className="text-green-600 text-sm">إجمالي المعلمين</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">
                {teachers.filter(t => t.employmentType === 'permanent').length}
              </div>
              <div className="text-blue-600 text-sm">المعلمون الدائمون</div>
            </div>
          </Card>
          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-900">
                {teachers.filter(t => t.employmentType === 'assignment' || t.employmentType === 'contract').length}
              </div>
              <div className="text-orange-600 text-sm">التنسيب والعقود</div>
            </div>
          </Card>
        </div>

        {/* قائمة المعلمين */}
        <Card title="قائمة المعلمين" subtitle={`عدد النتائج: ${filteredTeachers.length}`}>
          {filteredTeachers.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">👨‍🏫</div>
              <p className="text-gray-600">لا توجد نتائج مطابقة للبحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-2 font-semibold text-gray-700 w-16">ت</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الاسم والاختصاص</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">اللقب</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">رقم الهاتف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الدروس التي بعهدته</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">نوع التوظيف</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTeachers.map((teacher, index) => (
                    <tr key={teacher.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-2 text-center text-sm font-medium text-gray-600 w-16">{index + 1}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-bold">
                              {(teacher.fullName || teacher.name || '').charAt(0)}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{teacher.fullName || teacher.name}</div>
                            <div className="text-sm text-gray-600">{teacher.specialization}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">{teacher.title}</td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-gray-900">{teacher.phone}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {getSubjectNames(teacher.subjects)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          teacher.employmentType === 'permanent'
                            ? 'bg-blue-100 text-blue-800'
                            : teacher.employmentType === 'assignment'
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-purple-100 text-purple-800'
                        }`}>
                          {teacher.employmentType === 'permanent' ? 'دائم' :
                           teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEditTeacher(teacher)}
                          >
                            تعديل
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteTeacher(teacher.id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>

        {/* مكون إضافة/تعديل المعلم */}
        <AddTeacherModal
          isOpen={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setEditingTeacher(null);
          }}
          onSave={loadData}
          editingTeacher={editingTeacher}
          exportFunctions={{
            exportIndividualPDF,
            exportIndividualWord,
            exportIndividualHTML,
            isExporting
          }}
        />
      </div>
    </Layout>
  );
}
