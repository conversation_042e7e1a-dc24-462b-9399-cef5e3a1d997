import React, { useState } from 'react';
import { Teacher, Subject } from '@/types';
import Button from './Button';
import { localStorageManager } from '@/utils/localStorage';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'exceljs';
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, AlignmentType, WidthType } from 'docx';
import { saveAs } from 'file-saver';

interface TeacherReportExportProps {
  teachers: Teacher[];
  subjects: Subject[];
}

const TeacherReportExport: React.FC<TeacherReportExportProps> = ({ teachers, subjects }) => {
  const [isExporting, setIsExporting] = useState(false);

  // دالة للحصول على أسماء المواد
  const getSubjectNames = (subjectIds: string[]): string => {
    if (!subjectIds || subjectIds.length === 0) return 'لا توجد مواد';
    
    const subjectNames = subjectIds
      .map(id => subjects.find(s => s.id === id)?.name)
      .filter(Boolean);
    
    return subjectNames.length > 0 ? subjectNames.join(', ') : 'لا توجد مواد';
  };

  // دالة لتنسيق التاريخ بالأرقام فقط
  const formatDate = (date: Date): string => {
    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // دالة لتصدير تقرير فردي PDF
  const exportIndividualPDF = (teacher: Teacher) => {
    setIsExporting(true);

    const doc = new jsPDF();

    // إعداد الخط العربي
    doc.setFont('Arial', 'normal');
    doc.setFontSize(16);

    // العنوان
    doc.text('تقرير معلومات المعلم', 105, 20, { align: 'center' });
    doc.setFontSize(14);
    doc.text(teacher.fullName || teacher.name || '', 105, 35, { align: 'center' });

    // المعلومات الشخصية
    let yPos = 60;
    doc.setFontSize(12);
    doc.text('المعلومات الشخصية:', 20, yPos);
    yPos += 15;

    const personalInfo = [
      ['التسلسل', teacher.serialNumber || teacher.teacherId || 'غير محدد'],
      ['الاسم الرباعي', teacher.fullName || teacher.name || 'غير محدد'],
      ['اللقب', teacher.title || 'غير محدد'],
      ['اسم الأم الثلاثي', teacher.shortName || 'غير محدد'],
      ['تاريخ التولد', formatDate(teacher.dateOfBirth)],
      ['رقم الهاتف', teacher.phone || 'غير محدد'],
      ['عنوان السكن', teacher.address || 'غير محدد']
    ];

    (doc as any).autoTable({
      startY: yPos,
      head: [['البيان', 'القيمة']],
      body: personalInfo,
      styles: { font: 'Arial', fontSize: 10 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 20, right: 20 }
    });

    yPos = (doc as any).lastAutoTable.finalY + 20;

    // المعلومات الوظيفية
    doc.text('المعلومات الوظيفية:', 20, yPos);
    yPos += 10;

    const workInfo = [
      ['الاختصاص', teacher.specialization || 'غير محدد'],
      ['نوع التوظيف', teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد'],
      ['تاريخ أول تعيين', formatDate(teacher.firstAppointmentDate)],
      ['تاريخ المباشرة في المدرسة الحالية', formatDate(teacher.schoolStartDate)],
      ['الدروس التي بعهدته', getSubjectNames(teacher.subjects || [])]
    ];

    (doc as any).autoTable({
      startY: yPos,
      head: [['البيان', 'القيمة']],
      body: workInfo,
      styles: { font: 'Arial', fontSize: 10 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 20, right: 20 }
    });

    // إضافة معلومات المطور في الأسفل
    const pageHeight = doc.internal.pageSize.height;
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text('تم تطوير هذا النظام بواسطة: عبيدة العيثاوي', 105, pageHeight - 20, { align: 'center' });
    doc.text('رقم الهاتف: 07813332882', 105, pageHeight - 10, { align: 'center' });

    // حفظ الملف
    doc.save(`تقرير_المعلم_${teacher.fullName || teacher.name}_${new Date().toISOString().split('T')[0]}.pdf`);
    setIsExporting(false);
  };

  // دالة لتصدير تقرير فردي Word
  const exportIndividualWord = async (teacher: Teacher) => {
    setIsExporting(true);

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: 'تقرير معلومات المعلم',
                bold: true,
                size: 32
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: teacher.fullName || teacher.name || '',
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'المعلومات الشخصية',
                bold: true,
                size: 20
              })
            ]
          }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('البيان')] }),
                  new TableCell({ children: [new Paragraph('القيمة')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('التسلسل')] }),
                  new TableCell({ children: [new Paragraph(teacher.serialNumber || teacher.teacherId || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('الاسم الرباعي')] }),
                  new TableCell({ children: [new Paragraph(teacher.fullName || teacher.name || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('اللقب')] }),
                  new TableCell({ children: [new Paragraph(teacher.title || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('اسم الأم الثلاثي')] }),
                  new TableCell({ children: [new Paragraph(teacher.shortName || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('تاريخ التولد')] }),
                  new TableCell({ children: [new Paragraph(formatDate(teacher.dateOfBirth))] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('رقم الهاتف')] }),
                  new TableCell({ children: [new Paragraph(teacher.phone || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('عنوان السكن')] }),
                  new TableCell({ children: [new Paragraph(teacher.address || 'غير محدد')] })
                ]
              })
            ]
          }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'المعلومات الوظيفية',
                bold: true,
                size: 20
              })
            ]
          }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('البيان')] }),
                  new TableCell({ children: [new Paragraph('القيمة')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('الاختصاص')] }),
                  new TableCell({ children: [new Paragraph(teacher.specialization || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('نوع التوظيف')] }),
                  new TableCell({ children: [new Paragraph(teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('تاريخ أول تعيين')] }),
                  new TableCell({ children: [new Paragraph(formatDate(teacher.firstAppointmentDate))] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('تاريخ المباشرة في المدرسة الحالية')] }),
                  new TableCell({ children: [new Paragraph(formatDate(teacher.schoolStartDate))] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('الدروس التي بعهدته')] }),
                  new TableCell({ children: [new Paragraph(getSubjectNames(teacher.subjects || []))] })
                ]
              })
            ]
          }),
          new Paragraph({ text: '' }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'تم تطوير هذا النظام بواسطة: عبيدة العيثاوي',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'رقم الهاتف: 07813332882',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          })
        ]
      }]
    });

    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
    saveAs(blob, `تقرير_المعلم_${teacher.fullName || teacher.name}_${new Date().toISOString().split('T')[0]}.docx`);
    setIsExporting(false);
  };

  // دالة لتصدير تقرير فردي HTML (الدالة الأصلية)
  const exportIndividualHTML = (teacher: Teacher) => {
    setIsExporting(true);
    
    const reportContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المعلم - ${teacher.fullName || teacher.name}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1e40af;
            margin: 0;
            font-size: 28px;
        }
        .header h2 {
            color: #64748b;
            margin: 10px 0 0 0;
            font-size: 18px;
            font-weight: normal;
        }
        .info-section {
            margin-bottom: 25px;
        }
        .info-title {
            background: #eff6ff;
            color: #1e40af;
            padding: 10px 15px;
            border-right: 4px solid #2563eb;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .info-item {
            display: flex;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            min-width: 120px;
        }
        .info-value {
            color: #6b7280;
        }
        .subjects-list {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .employment-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .permanent { background: #dbeafe; color: #1e40af; }
        .assignment { background: #fed7aa; color: #ea580c; }
        .contract { background: #e9d5ff; color: #7c3aed; }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير معلومات المعلم</h1>
            <h2>${teacher.fullName || teacher.name}</h2>
        </div>

        <div class="info-section">
            <div class="info-title">المعلومات الشخصية</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">التسلسل:</span>
                    <span class="info-value">${teacher.serialNumber || teacher.teacherId || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الاسم الرباعي:</span>
                    <span class="info-value">${teacher.fullName || teacher.name || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">اللقب:</span>
                    <span class="info-value">${teacher.title || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">اسم الأم الثلاثي:</span>
                    <span class="info-value">${teacher.shortName || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ التولد:</span>
                    <span class="info-value">${formatDate(teacher.dateOfBirth)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">${teacher.phone || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عنوان السكن:</span>
                    <span class="info-value">${teacher.address || 'غير محدد'}</span>
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">المعلومات الوظيفية</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">الاختصاص:</span>
                    <span class="info-value">${teacher.specialization || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع التوظيف:</span>
                    <span class="info-value">
                        <span class="employment-badge ${teacher.employmentType === 'permanent' ? 'permanent' : teacher.employmentType === 'assignment' ? 'assignment' : 'contract'}">
                            ${teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد'}
                        </span>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ أول تعيين:</span>
                    <span class="info-value">${formatDate(teacher.firstAppointmentDate)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ المباشرة في المدرسة الحالية:</span>
                    <span class="info-value">${formatDate(teacher.schoolStartDate)}</span>
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">الدروس التي بعهدته</div>
            <div class="subjects-list">
                ${getSubjectNames(teacher.subjects || [])}
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>
            <p>نظام إدارة المدرسة</p>
            <hr style="margin: 10px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="color: #9ca3af; font-size: 11px;">تم تطوير هذا النظام بواسطة: <strong>عبيدة العيثاوي</strong></p>
            <p style="color: #9ca3af; font-size: 11px;">رقم الهاتف: 07813332882</p>
        </div>
    </div>
</body>
</html>`;

    // إنشاء وتحميل الملف
    const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `تقرير_المعلم_${teacher.fullName || teacher.name}_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي PDF
  const exportGroupPDF = () => {
    setIsExporting(true);

    const doc = new jsPDF();

    // العنوان
    doc.setFont('Arial', 'normal');
    doc.setFontSize(16);
    doc.text('التقرير الجماعي للمعلمين', 105, 20, { align: 'center' });
    doc.setFontSize(12);
    doc.text(`إجمالي عدد المعلمين: ${teachers.length}`, 105, 35, { align: 'center' });

    // إعداد البيانات للجدول
    const tableData = teachers.map((teacher, index) => [
      teacher.serialNumber || teacher.teacherId || (index + 1).toString(),
      teacher.fullName || teacher.name || 'غير محدد',
      teacher.title || 'غير محدد',
      teacher.specialization || 'غير محدد',
      teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد',
      formatDate(teacher.firstAppointmentDate),
      formatDate(teacher.schoolStartDate),
      formatDate(teacher.dateOfBirth),
      teacher.phone || 'غير محدد',
      getSubjectNames(teacher.subjects || [])
    ]);

    (doc as any).autoTable({
      startY: 50,
      head: [['التسلسل', 'الاسم الرباعي', 'اللقب', 'الاختصاص', 'نوع التوظيف', 'تاريخ التعيين', 'تاريخ المباشرة', 'تاريخ التولد', 'رقم الهاتف', 'الدروس']],
      body: tableData,
      styles: { font: 'Arial', fontSize: 6 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 3, right: 3 },
      columnStyles: {
        0: { cellWidth: 15 },
        1: { cellWidth: 25 },
        2: { cellWidth: 15 },
        3: { cellWidth: 18 },
        4: { cellWidth: 15 },
        5: { cellWidth: 18 },
        6: { cellWidth: 18 },
        7: { cellWidth: 18 },
        8: { cellWidth: 18 },
        9: { cellWidth: 25 }
      }
    });

    // إضافة معلومات المطور في الأسفل
    const pageHeight = doc.internal.pageSize.height;
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text('تم تطوير هذا النظام بواسطة: عبيدة العيثاوي', 105, pageHeight - 20, { align: 'center' });
    doc.text('رقم الهاتف: 07813332882', 105, pageHeight - 10, { align: 'center' });

    doc.save(`التقرير_الجماعي_للمعلمين_${new Date().toISOString().split('T')[0]}.pdf`);
    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي Excel
  const exportGroupExcel = async () => {
    setIsExporting(true);

    const workbook = new XLSX.Workbook();
    const worksheet = workbook.addWorksheet('المعلمين');

    // إعداد العناوين
    const headers = [
      'التسلسل',
      'الاسم الرباعي',
      'اللقب',
      'اسم الأم الثلاثي',
      'تاريخ التولد',
      'الاختصاص',
      'نوع التوظيف',
      'تاريخ أول تعيين',
      'تاريخ المباشرة في المدرسة الحالية',
      'رقم الهاتف',
      'عنوان السكن',
      'الدروس التي بعهدته'
    ];

    // إضافة العناوين
    worksheet.addRow(headers);

    // تنسيق العناوين
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF428BCA' }
    };

    // إضافة البيانات
    teachers.forEach((teacher, index) => {
      worksheet.addRow([
        teacher.serialNumber || teacher.teacherId || (index + 1),
        teacher.fullName || teacher.name || 'غير محدد',
        teacher.title || 'غير محدد',
        teacher.shortName || 'غير محدد',
        formatDate(teacher.dateOfBirth),
        teacher.specialization || 'غير محدد',
        teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد',
        formatDate(teacher.firstAppointmentDate),
        formatDate(teacher.schoolStartDate),
        teacher.phone || 'غير محدد',
        teacher.address || 'غير محدد',
        getSubjectNames(teacher.subjects || [])
      ]);
    });

    // تنسيق الأعمدة
    worksheet.columns.forEach(column => {
      column.width = 20;
    });

    // إضافة معلومات المطور
    const lastRow = worksheet.rowCount + 2;
    worksheet.addRow([]);
    worksheet.addRow(['تم تطوير هذا النظام بواسطة: عبيدة العيثاوي']);
    worksheet.addRow(['رقم الهاتف: 07813332882']);

    // تنسيق معلومات المطور
    const developerRow1 = worksheet.getRow(lastRow + 1);
    const developerRow2 = worksheet.getRow(lastRow + 2);
    developerRow1.font = { italic: true, color: { argb: 'FF808080' } };
    developerRow2.font = { italic: true, color: { argb: 'FF808080' } };

    // حفظ الملف
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, `التقرير_الجماعي_للمعلمين_${new Date().toISOString().split('T')[0]}.xlsx`);
    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي Word
  const exportGroupWord = async () => {
    setIsExporting(true);

    const tableRows = [
      new TableRow({
        children: [
          new TableCell({ children: [new Paragraph('التسلسل')] }),
          new TableCell({ children: [new Paragraph('الاسم الرباعي')] }),
          new TableCell({ children: [new Paragraph('اللقب')] }),
          new TableCell({ children: [new Paragraph('الاختصاص')] }),
          new TableCell({ children: [new Paragraph('نوع التوظيف')] }),
          new TableCell({ children: [new Paragraph('تاريخ التعيين')] }),
          new TableCell({ children: [new Paragraph('تاريخ المباشرة')] }),
          new TableCell({ children: [new Paragraph('تاريخ التولد')] }),
          new TableCell({ children: [new Paragraph('رقم الهاتف')] }),
          new TableCell({ children: [new Paragraph('الدروس')] })
        ]
      }),
      ...teachers.map((teacher, index) =>
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph(teacher.serialNumber || teacher.teacherId || (index + 1).toString())] }),
            new TableCell({ children: [new Paragraph(teacher.fullName || teacher.name || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(teacher.title || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(teacher.specialization || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد')] }),
            new TableCell({ children: [new Paragraph(formatDate(teacher.firstAppointmentDate))] }),
            new TableCell({ children: [new Paragraph(formatDate(teacher.schoolStartDate))] }),
            new TableCell({ children: [new Paragraph(formatDate(teacher.dateOfBirth))] }),
            new TableCell({ children: [new Paragraph(teacher.phone || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(getSubjectNames(teacher.subjects || []))] })
          ]
        })
      )
    ];

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: 'التقرير الجماعي للمعلمين',
                bold: true,
                size: 32
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: `إجمالي عدد المعلمين: ${teachers.length}`,
                size: 20
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({ text: '' }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: tableRows
          }),
          new Paragraph({ text: '' }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'تم تطوير هذا النظام بواسطة: عبيدة العيثاوي',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'رقم الهاتف: 07813332882',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          })
        ]
      }]
    });

    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
    saveAs(blob, `التقرير_الجماعي_للمعلمين_${new Date().toISOString().split('T')[0]}.docx`);
    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي HTML (الدالة الأصلية)
  const exportGroupHTML = () => {
    setIsExporting(true);
    
    const reportContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير جماعي للمعلمين</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1e40af;
            margin: 0;
            font-size: 28px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
        }
        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-top: 5px;
        }
        .table-container {
            overflow-x: auto;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        th {
            background: #f8fafc;
            font-weight: bold;
            color: #374151;
            border-bottom: 2px solid #d1d5db;
        }
        tr:hover {
            background: #f9fafb;
        }
        .employment-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        .permanent { background: #dbeafe; color: #1e40af; }
        .assignment { background: #fed7aa; color: #ea580c; }
        .contract { background: #e9d5ff; color: #7c3aed; }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>التقرير الجماعي للمعلمين</h1>
            <p>إجمالي عدد المعلمين: ${teachers.length}</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${teachers.filter(t => t.employmentType === 'permanent').length}</div>
                <div class="stat-label">معلمون دائمون</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${teachers.filter(t => t.employmentType === 'assignment').length}</div>
                <div class="stat-label">معلمون منسبون</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${teachers.filter(t => t.employmentType === 'contract').length}</div>
                <div class="stat-label">معلمون بعقد</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${new Set(teachers.map(t => t.specialization)).size}</div>
                <div class="stat-label">التخصصات المختلفة</div>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>التسلسل</th>
                        <th>الاسم الرباعي</th>
                        <th>اللقب</th>
                        <th>الاختصاص</th>
                        <th>نوع التوظيف</th>
                        <th>تاريخ التعيين</th>
                        <th>تاريخ المباشرة</th>
                        <th>تاريخ التولد</th>
                        <th>رقم الهاتف</th>
                        <th>الدروس التي بعهدته</th>
                    </tr>
                </thead>
                <tbody>
                    ${teachers.map((teacher, index) => `
                        <tr>
                            <td>${teacher.serialNumber || teacher.teacherId || (index + 1)}</td>
                            <td>${teacher.fullName || teacher.name || 'غير محدد'}</td>
                            <td>${teacher.title || 'غير محدد'}</td>
                            <td>${teacher.specialization || 'غير محدد'}</td>
                            <td>
                                <span class="employment-badge ${teacher.employmentType === 'permanent' ? 'permanent' : teacher.employmentType === 'assignment' ? 'assignment' : 'contract'}">
                                    ${teacher.employmentType === 'permanent' ? 'دائم' : teacher.employmentType === 'assignment' ? 'تنسيب' : 'عقد'}
                                </span>
                            </td>
                            <td>${formatDate(teacher.firstAppointmentDate)}</td>
                            <td>${formatDate(teacher.schoolStartDate)}</td>
                            <td>${formatDate(teacher.dateOfBirth)}</td>
                            <td>${teacher.phone || 'غير محدد'}</td>
                            <td>${getSubjectNames(teacher.subjects || [])}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>
            <p>نظام إدارة المدرسة</p>
            <hr style="margin: 10px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="color: #9ca3af; font-size: 11px;">تم تطوير هذا النظام بواسطة: <strong>عبيدة العيثاوي</strong></p>
            <p style="color: #9ca3af; font-size: 11px;">رقم الهاتف: 07813332882</p>
        </div>
    </div>
</body>
</html>`;

    // إنشاء وتحميل الملف
    const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `التقرير_الجماعي_للمعلمين_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setIsExporting(false);
  };

  return {
    exportIndividualPDF,
    exportIndividualWord,
    exportIndividualHTML,
    exportGroupPDF,
    exportGroupExcel,
    exportGroupWord,
    exportGroupHTML,
    isExporting
  };
};

export default TeacherReportExport;
