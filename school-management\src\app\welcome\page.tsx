'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/Button';
import Card from '@/components/Card';
import { useAuth } from '@/hooks/useAuth';
import NavigationButtons from '@/components/NavigationButtons';

export default function WelcomePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  if (!user) {
    return null;
  }

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'developer':
        return {
          title: 'المطور',
          description: 'أهلاً وسهلاً عبيدة العيثاوي، مرحباً بك في نظامك المتطور',
          features: [
            'إدارة شاملة لجميع أجزاء النظام',
            'إدارة المستخدمين والصلاحيات',
            'تطوير وصيانة النظام',
            'إدارة قواعد البيانات',
            'مراقبة أداء النظام',
            'إضافة ميزات جديدة'
          ]
        };
      case 'admin':
        return {
          title: user.name === 'عبيدة العيثاوي' ? 'مالك المدرسة ومدير النظام' : 'مدير النظام',
          description: user.name === 'عبيدة العيثاوي' ?
            'أهلاً وسهلاً عبيدة العيثاوي، مرحباً بك في مدرستك النموذجية' :
            'أهلاً وسهلاً، لديك صلاحية كاملة لإدارة جميع أجزاء النظام',
          features: [
            'إدارة الطلاب والمعلمين',
            'إدارة الصفوف والمواد',
            'عرض التقارير والإحصائيات',
            'إدارة إعدادات النظام',
            'إدارة صلاحيات المستخدمين'
          ]
        };
      case 'teacher':
        return {
          title: 'معلم',
          description: 'يمكنك إدارة صفوفك وطلابك وتسجيل الدرجات',
          features: [
            'عرض وإدارة طلاب صفوفك',
            'تسجيل الحضور والغياب',
            'إدخال وتعديل الدرجات',
            'عرض تقارير الطلاب',
            'إدارة المواد المُدرَّسة'
          ]
        };
      case 'student':
        return {
          title: 'طالب',
          description: 'يمكنك عرض درجاتك وحضورك ومتابعة تقدمك الأكاديمي',
          features: [
            'عرض الدرجات والتقديرات',
            'متابعة سجل الحضور',
            'عرض الجدول الدراسي',
            'متابعة الواجبات والامتحانات',
            'التواصل مع المعلمين'
          ]
        };
      case 'parent':
        return {
          title: 'ولي أمر',
          description: 'يمكنك متابعة تقدم أطفالك الأكاديمي',
          features: [
            'متابعة درجات الأطفال',
            'عرض سجل الحضور والغياب',
            'التواصل مع المعلمين',
            'عرض التقارير الأكاديمية',
            'متابعة الأنشطة المدرسية'
          ]
        };
      default:
        return {
          title: 'مستخدم',
          description: 'مرحباً بك في النظام',
          features: []
        };
    }
  };

  const roleInfo = getRoleDescription(user.role);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4" dir="rtl">
      {/* خلفية متحركة */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-20 animate-pulse delay-1000"></div>
      </div>

      <div className="relative w-full max-w-2xl">
        {/* أزرار التنقل */}
        <NavigationButtons
          className="mb-6"
          showBackButton={false}
          customHomeAction={() => router.push('/')}
        />

        <Card className="text-center p-8">
          {/* ترحيب */}
          <div className="mb-8">
            <div className="w-24 h-24 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <span className="text-white text-4xl">🎉</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              مرحباً بك، {user.name}!
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              تم تسجيل دخولك بنجاح إلى نظام إدارة المدرسة
            </p>
            <p className="text-gray-500">
              دورك في النظام: <span className="font-semibold text-blue-600">{roleInfo.title}</span>
            </p>
          </div>

          {/* معلومات الدور */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">
              ما يمكنك فعله في النظام
            </h2>
            <p className="text-gray-600 mb-4">
              {roleInfo.description}
            </p>
            
            {roleInfo.features.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {roleInfo.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2 space-x-reverse text-right">
                    <span className="text-green-500 text-lg">✓</span>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* روابط سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            {user.role === 'developer' && (
              <>
                <Button
                  variant="primary"
                  onClick={() => router.push('/users')}
                  className="h-12"
                >
                  إدارة المستخدمين
                </Button>
                <Button
                  variant="success"
                  onClick={() => router.push('/settings')}
                  className="h-12"
                >
                  إعدادات النظام
                </Button>
              </>
            )}

            {user.role === 'admin' && (
              <>
                <Button
                  variant="primary"
                  onClick={() => router.push('/students')}
                  className="h-12"
                >
                  إدارة الطلاب
                </Button>
                <Button
                  variant="success"
                  onClick={() => router.push('/teachers')}
                  className="h-12"
                >
                  إدارة المعلمين
                </Button>
              </>
            )}
            
            {user.role === 'teacher' && (
              <>
                <Button
                  variant="primary"
                  onClick={() => router.push('/students')}
                  className="h-12"
                >
                  طلابي
                </Button>
                <Button
                  variant="info"
                  onClick={() => router.push('/grades')}
                  className="h-12"
                >
                  الدرجات
                </Button>
              </>
            )}
            
            {(user.role === 'student' || user.role === 'parent') && (
              <>
                <Button
                  variant="info"
                  onClick={() => router.push('/grades')}
                  className="h-12"
                >
                  الدرجات
                </Button>
                <Button
                  variant="warning"
                  onClick={() => router.push('/attendance')}
                  className="h-12"
                >
                  الحضور
                </Button>
              </>
            )}
          </div>

          {/* زر المتابعة */}
          <Button
            variant="primary"
            onClick={() => router.push('/')}
            size="lg"
            className="w-full md:w-auto px-8"
          >
            الانتقال إلى لوحة التحكم
          </Button>

          {/* نصائح */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-center mb-2">
              <span className="text-yellow-500 text-xl ml-2">💡</span>
              <h3 className="font-semibold text-yellow-800">نصيحة</h3>
            </div>
            <p className="text-sm text-yellow-700">
              يمكنك الوصول إلى جميع الوظائف من خلال القائمة الجانبية. 
              إذا كنت تحتاج مساعدة، لا تتردد في التواصل مع الدعم الفني.
            </p>
          </div>
        </Card>

        {/* تذييل */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            © 2024 نظام إدارة المدرسة - نتمنى لك تجربة ممتعة ومفيدة
          </p>
        </div>
      </div>
    </div>
  );
}
