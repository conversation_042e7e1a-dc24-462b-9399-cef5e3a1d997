import React, { useState } from 'react';
import { Employee } from '@/types';
import Button from './Button';
import { localStorageManager } from '@/utils/localStorage';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'exceljs';
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, AlignmentType, WidthType } from 'docx';
import { saveAs } from 'file-saver';

interface EmployeeReportExportProps {
  employees: Employee[];
}

const EmployeeReportExport: React.FC<EmployeeReportExportProps> = ({ employees }) => {
  const [isExporting, setIsExporting] = useState(false);

  // دالة للحصول على أسماء المسؤوليات
  const getResponsibilityNames = (responsibilities: string[]): string => {
    if (!responsibilities || responsibilities.length === 0) return 'لا توجد مسؤوليات';
    return responsibilities.join(', ');
  };

  // دالة لتنسيق التاريخ بالأرقام فقط
  const formatDate = (date: Date): string => {
    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // دالة لتصدير تقرير فردي PDF
  const exportIndividualPDF = (employee: Employee) => {
    setIsExporting(true);
    
    const doc = new jsPDF();
    
    // إعداد الخط العربي
    doc.setFont('Arial', 'normal');
    doc.setFontSize(16);
    
    // العنوان
    doc.text('تقرير معلومات الموظف', 105, 20, { align: 'center' });
    doc.setFontSize(14);
    doc.text(employee.fullName || employee.name || '', 105, 35, { align: 'center' });
    
    // المعلومات الشخصية
    let yPos = 60;
    doc.setFontSize(12);
    doc.text('المعلومات الشخصية:', 20, yPos);
    yPos += 15;
    
    const personalInfo = [
      ['التسلسل', employee.serialNumber || employee.employeeId || 'غير محدد'],
      ['الاسم الرباعي', employee.fullName || employee.name || 'غير محدد'],
      ['اللقب', employee.title || 'غير محدد'],
      ['اسم الأم الثلاثي', employee.shortName || 'غير محدد'],
      ['تاريخ التولد', formatDate(employee.dateOfBirth)],
      ['رقم الهاتف', employee.phone || 'غير محدد'],
      ['عنوان السكن', employee.address || 'غير محدد']
    ];
    
    (doc as any).autoTable({
      startY: yPos,
      head: [['البيان', 'القيمة']],
      body: personalInfo,
      styles: { font: 'Arial', fontSize: 10 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 20, right: 20 }
    });
    
    yPos = (doc as any).lastAutoTable.finalY + 20;
    
    // المعلومات الوظيفية
    doc.text('المعلومات الوظيفية:', 20, yPos);
    yPos += 10;
    
    const workInfo = [
      ['القسم', employee.department || 'غير محدد'],
      ['نوع التوظيف', employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد'],
      ['تاريخ أول تعيين', formatDate(employee.firstAppointmentDate)],
      ['تاريخ المباشرة في المدرسة الحالية', formatDate(employee.schoolStartDate)],
      ['المسؤوليات', getResponsibilityNames(employee.responsibilities || [])]
    ];
    
    (doc as any).autoTable({
      startY: yPos,
      head: [['البيان', 'القيمة']],
      body: workInfo,
      styles: { font: 'Arial', fontSize: 10 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 20, right: 20 }
    });
    
    // إضافة معلومات المطور في الأسفل
    const pageHeight = doc.internal.pageSize.height;
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text('تم تطوير هذا النظام بواسطة: عبيدة العيثاوي', 105, pageHeight - 20, { align: 'center' });
    doc.text('رقم الهاتف: 07813332882', 105, pageHeight - 10, { align: 'center' });

    // حفظ الملف
    doc.save(`تقرير_الموظف_${employee.fullName || employee.name}_${new Date().toISOString().split('T')[0]}.pdf`);
    setIsExporting(false);
  };

  // دالة لتصدير تقرير فردي Word
  const exportIndividualWord = async (employee: Employee) => {
    setIsExporting(true);
    
    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: 'تقرير معلومات الموظف',
                bold: true,
                size: 32
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: employee.fullName || employee.name || '',
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'المعلومات الشخصية',
                bold: true,
                size: 20
              })
            ]
          }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('البيان')] }),
                  new TableCell({ children: [new Paragraph('القيمة')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('التسلسل')] }),
                  new TableCell({ children: [new Paragraph(employee.serialNumber || employee.employeeId || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('الاسم الرباعي')] }),
                  new TableCell({ children: [new Paragraph(employee.fullName || employee.name || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('اللقب')] }),
                  new TableCell({ children: [new Paragraph(employee.title || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('اسم الأم الثلاثي')] }),
                  new TableCell({ children: [new Paragraph(employee.shortName || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('تاريخ التولد')] }),
                  new TableCell({ children: [new Paragraph(formatDate(employee.dateOfBirth))] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('رقم الهاتف')] }),
                  new TableCell({ children: [new Paragraph(employee.phone || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('عنوان السكن')] }),
                  new TableCell({ children: [new Paragraph(employee.address || 'غير محدد')] })
                ]
              })
            ]
          }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'المعلومات الوظيفية',
                bold: true,
                size: 20
              })
            ]
          }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('البيان')] }),
                  new TableCell({ children: [new Paragraph('القيمة')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('القسم')] }),
                  new TableCell({ children: [new Paragraph(employee.department || 'غير محدد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('نوع التوظيف')] }),
                  new TableCell({ children: [new Paragraph(employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد')] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('تاريخ أول تعيين')] }),
                  new TableCell({ children: [new Paragraph(formatDate(employee.firstAppointmentDate))] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('تاريخ المباشرة في المدرسة الحالية')] }),
                  new TableCell({ children: [new Paragraph(formatDate(employee.schoolStartDate))] })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({ children: [new Paragraph('المسؤوليات')] }),
                  new TableCell({ children: [new Paragraph(getResponsibilityNames(employee.responsibilities || []))] })
                ]
              })
            ]
          }),
          new Paragraph({ text: '' }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'تم تطوير هذا النظام بواسطة: عبيدة العيثاوي',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'رقم الهاتف: 07813332882',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          })
        ]
      }]
    });
    
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
    saveAs(blob, `تقرير_الموظف_${employee.fullName || employee.name}_${new Date().toISOString().split('T')[0]}.docx`);
    setIsExporting(false);
  };

  // دالة لتصدير تقرير فردي HTML
  const exportIndividualHTML = (employee: Employee) => {
    setIsExporting(true);
    
    const reportContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الموظف - ${employee.fullName || employee.name}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1e40af;
            margin: 0;
            font-size: 28px;
        }
        .header h2 {
            color: #64748b;
            margin: 10px 0 0 0;
            font-size: 18px;
            font-weight: normal;
        }
        .info-section {
            margin-bottom: 25px;
        }
        .info-title {
            background: #eff6ff;
            color: #1e40af;
            padding: 10px 15px;
            border-right: 4px solid #2563eb;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .info-item {
            display: flex;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            min-width: 120px;
        }
        .info-value {
            color: #6b7280;
        }
        .responsibilities-list {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .employment-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .permanent { background: #dbeafe; color: #1e40af; }
        .assignment { background: #fed7aa; color: #ea580c; }
        .contract { background: #e9d5ff; color: #7c3aed; }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير معلومات الموظف</h1>
            <h2>${employee.fullName || employee.name}</h2>
        </div>

        <div class="info-section">
            <div class="info-title">المعلومات الشخصية</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">التسلسل:</span>
                    <span class="info-value">${employee.serialNumber || employee.employeeId || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الاسم الرباعي:</span>
                    <span class="info-value">${employee.fullName || employee.name || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">اللقب:</span>
                    <span class="info-value">${employee.title || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">اسم الأم الثلاثي:</span>
                    <span class="info-value">${employee.shortName || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ التولد:</span>
                    <span class="info-value">${formatDate(employee.dateOfBirth)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">${employee.phone || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عنوان السكن:</span>
                    <span class="info-value">${employee.address || 'غير محدد'}</span>
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">المعلومات الوظيفية</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">القسم:</span>
                    <span class="info-value">${employee.department || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع التوظيف:</span>
                    <span class="info-value">
                        <span class="employment-badge ${employee.employmentType === 'permanent' ? 'permanent' : employee.employmentType === 'assignment' ? 'assignment' : 'contract'}">
                            ${employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد'}
                        </span>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ أول تعيين:</span>
                    <span class="info-value">${formatDate(employee.firstAppointmentDate)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ المباشرة في المدرسة الحالية:</span>
                    <span class="info-value">${formatDate(employee.schoolStartDate)}</span>
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">المسؤوليات</div>
            <div class="responsibilities-list">
                ${getResponsibilityNames(employee.responsibilities || [])}
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>
            <p>نظام إدارة المدرسة</p>
            <hr style="margin: 10px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="color: #9ca3af; font-size: 11px;">تم تطوير هذا النظام بواسطة: <strong>عبيدة العيثاوي</strong></p>
            <p style="color: #9ca3af; font-size: 11px;">رقم الهاتف: 07813332882</p>
        </div>
    </div>
</body>
</html>`;

    // إنشاء وتحميل الملف
    const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `تقرير_الموظف_${employee.fullName || employee.name}_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي PDF
  const exportGroupPDF = () => {
    setIsExporting(true);

    const doc = new jsPDF();

    // العنوان
    doc.setFont('Arial', 'normal');
    doc.setFontSize(16);
    doc.text('التقرير الجماعي للموظفين', 105, 20, { align: 'center' });
    doc.setFontSize(12);
    doc.text(`إجمالي عدد الموظفين: ${employees.length}`, 105, 35, { align: 'center' });

    // إعداد البيانات للجدول
    const tableData = employees.map((employee, index) => [
      employee.serialNumber || employee.employeeId || (index + 1).toString(),
      employee.fullName || employee.name || 'غير محدد',
      employee.title || 'غير محدد',
      employee.department || 'غير محدد',
      employee.position || 'غير محدد',
      employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد',
      formatDate(employee.firstAppointmentDate),
      formatDate(employee.schoolStartDate),
      formatDate(employee.dateOfBirth),
      employee.phone || 'غير محدد'
    ]);

    (doc as any).autoTable({
      startY: 50,
      head: [['التسلسل', 'الاسم الرباعي', 'اللقب', 'القسم', 'المنصب', 'نوع التوظيف', 'تاريخ التعيين', 'تاريخ المباشرة', 'تاريخ التولد', 'رقم الهاتف']],
      body: tableData,
      styles: { font: 'Arial', fontSize: 6 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 3, right: 3 },
      columnStyles: {
        0: { cellWidth: 15 },
        1: { cellWidth: 25 },
        2: { cellWidth: 15 },
        3: { cellWidth: 18 },
        4: { cellWidth: 18 },
        5: { cellWidth: 15 },
        6: { cellWidth: 18 },
        7: { cellWidth: 18 },
        8: { cellWidth: 18 },
        9: { cellWidth: 18 }
      }
    });

    // إضافة معلومات المطور في الأسفل
    const pageHeight = doc.internal.pageSize.height;
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text('تم تطوير هذا النظام بواسطة: عبيدة العيثاوي', 105, pageHeight - 20, { align: 'center' });
    doc.text('رقم الهاتف: 07813332882', 105, pageHeight - 10, { align: 'center' });

    doc.save(`التقرير_الجماعي_للموظفين_${new Date().toISOString().split('T')[0]}.pdf`);
    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي Excel
  const exportGroupExcel = async () => {
    setIsExporting(true);

    const workbook = new XLSX.Workbook();
    const worksheet = workbook.addWorksheet('الموظفين');

    // إعداد العناوين
    const headers = [
      'التسلسل',
      'الاسم الرباعي',
      'اللقب',
      'اسم الأم الثلاثي',
      'تاريخ التولد',
      'القسم',
      'المنصب الوظيفي',
      'نوع التوظيف',
      'تاريخ أول تعيين',
      'تاريخ المباشرة في المدرسة الحالية',
      'رقم الهاتف',
      'عنوان السكن',
      'المسؤوليات'
    ];

    // إضافة العناوين
    worksheet.addRow(headers);

    // تنسيق العناوين
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF428BCA' }
    };

    // إضافة البيانات
    employees.forEach((employee, index) => {
      worksheet.addRow([
        employee.serialNumber || employee.employeeId || (index + 1),
        employee.fullName || employee.name || 'غير محدد',
        employee.title || 'غير محدد',
        employee.shortName || 'غير محدد',
        formatDate(employee.dateOfBirth),
        employee.department || 'غير محدد',
        employee.position || 'غير محدد',
        employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد',
        formatDate(employee.firstAppointmentDate),
        formatDate(employee.schoolStartDate),
        employee.phone || 'غير محدد',
        employee.address || 'غير محدد',
        getResponsibilityNames(employee.responsibilities || [])
      ]);
    });

    // تنسيق الأعمدة
    worksheet.columns.forEach(column => {
      column.width = 20;
    });

    // إضافة معلومات المطور
    const lastRow = worksheet.rowCount + 2;
    worksheet.addRow([]);
    worksheet.addRow(['تم تطوير هذا النظام بواسطة: عبيدة العيثاوي']);
    worksheet.addRow(['رقم الهاتف: 07813332882']);

    // تنسيق معلومات المطور
    const developerRow1 = worksheet.getRow(lastRow + 1);
    const developerRow2 = worksheet.getRow(lastRow + 2);
    developerRow1.font = { italic: true, color: { argb: 'FF808080' } };
    developerRow2.font = { italic: true, color: { argb: 'FF808080' } };

    // حفظ الملف
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, `التقرير_الجماعي_للموظفين_${new Date().toISOString().split('T')[0]}.xlsx`);
    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي Word
  const exportGroupWord = async () => {
    setIsExporting(true);

    const tableRows = [
      new TableRow({
        children: [
          new TableCell({ children: [new Paragraph('التسلسل')] }),
          new TableCell({ children: [new Paragraph('الاسم الرباعي')] }),
          new TableCell({ children: [new Paragraph('اللقب')] }),
          new TableCell({ children: [new Paragraph('القسم')] }),
          new TableCell({ children: [new Paragraph('نوع التوظيف')] }),
          new TableCell({ children: [new Paragraph('تاريخ التعيين')] }),
          new TableCell({ children: [new Paragraph('تاريخ المباشرة')] }),
          new TableCell({ children: [new Paragraph('تاريخ التولد')] }),
          new TableCell({ children: [new Paragraph('رقم الهاتف')] })
        ]
      }),
      ...employees.map((employee, index) =>
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph(employee.serialNumber || employee.employeeId || (index + 1).toString())] }),
            new TableCell({ children: [new Paragraph(employee.fullName || employee.name || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(employee.title || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(employee.department || 'غير محدد')] }),
            new TableCell({ children: [new Paragraph(employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد')] }),
            new TableCell({ children: [new Paragraph(formatDate(employee.firstAppointmentDate))] }),
            new TableCell({ children: [new Paragraph(formatDate(employee.schoolStartDate))] }),
            new TableCell({ children: [new Paragraph(formatDate(employee.dateOfBirth))] }),
            new TableCell({ children: [new Paragraph(employee.phone || 'غير محدد')] })
          ]
        })
      )
    ];

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: 'التقرير الجماعي للموظفين',
                bold: true,
                size: 32
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: `إجمالي عدد الموظفين: ${employees.length}`,
                size: 20
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({ text: '' }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: tableRows
          }),
          new Paragraph({ text: '' }),
          new Paragraph({ text: '' }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'تم تطوير هذا النظام بواسطة: عبيدة العيثاوي',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: 'رقم الهاتف: 07813332882',
                size: 16,
                color: '808080'
              })
            ],
            alignment: AlignmentType.CENTER
          })
        ]
      }]
    });

    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
    saveAs(blob, `التقرير_الجماعي_للموظفين_${new Date().toISOString().split('T')[0]}.docx`);
    setIsExporting(false);
  };

  // دالة لتصدير التقرير الجماعي HTML
  const exportGroupHTML = () => {
    setIsExporting(true);

    const reportContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير الجماعي للموظفين</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1e40af;
            margin: 0;
            font-size: 28px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
        }
        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-top: 5px;
        }
        .table-container {
            overflow-x: auto;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        th {
            background: #f8fafc;
            font-weight: bold;
            color: #374151;
            border-bottom: 2px solid #d1d5db;
        }
        tr:hover {
            background: #f9fafb;
        }
        .employment-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        .permanent { background: #dbeafe; color: #1e40af; }
        .assignment { background: #fed7aa; color: #ea580c; }
        .contract { background: #e9d5ff; color: #7c3aed; }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>التقرير الجماعي للموظفين</h1>
            <p>إجمالي عدد الموظفين: ${employees.length}</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${employees.filter(e => e.employmentType === 'permanent').length}</div>
                <div class="stat-label">موظفون دائمون</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${employees.filter(e => e.employmentType === 'assignment').length}</div>
                <div class="stat-label">موظفون منسبون</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${employees.filter(e => e.employmentType === 'contract').length}</div>
                <div class="stat-label">موظفون بعقد</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${new Set(employees.map(e => e.department)).size}</div>
                <div class="stat-label">الأقسام المختلفة</div>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>التسلسل</th>
                        <th>الاسم الرباعي</th>
                        <th>اللقب</th>
                        <th>القسم</th>
                        <th>نوع التوظيف</th>
                        <th>تاريخ التعيين</th>
                        <th>تاريخ المباشرة</th>
                        <th>تاريخ التولد</th>
                        <th>رقم الهاتف</th>
                    </tr>
                </thead>
                <tbody>
                    ${employees.map((employee, index) => `
                        <tr>
                            <td>${employee.serialNumber || employee.employeeId || (index + 1)}</td>
                            <td>${employee.fullName || employee.name || 'غير محدد'}</td>
                            <td>${employee.title || 'غير محدد'}</td>
                            <td>${employee.department || 'غير محدد'}</td>
                            <td>
                                <span class="employment-badge ${employee.employmentType === 'permanent' ? 'permanent' : employee.employmentType === 'assignment' ? 'assignment' : 'contract'}">
                                    ${employee.employmentType === 'permanent' ? 'دائم' : employee.employmentType === 'assignment' ? 'تنسيب' : 'عقد'}
                                </span>
                            </td>
                            <td>${formatDate(employee.firstAppointmentDate)}</td>
                            <td>${formatDate(employee.schoolStartDate)}</td>
                            <td>${formatDate(employee.dateOfBirth)}</td>
                            <td>${employee.phone || 'غير محدد'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>
            <p>نظام إدارة المدرسة</p>
            <hr style="margin: 10px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="color: #9ca3af; font-size: 11px;">تم تطوير هذا النظام بواسطة: <strong>عبيدة العيثاوي</strong></p>
            <p style="color: #9ca3af; font-size: 11px;">رقم الهاتف: 07813332882</p>
        </div>
    </div>
</body>
</html>`;

    // إنشاء وتحميل الملف
    const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `التقرير_الجماعي_للموظفين_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setIsExporting(false);
  };

  return {
    exportIndividualPDF,
    exportIndividualWord,
    exportIndividualHTML,
    exportGroupPDF,
    exportGroupExcel,
    exportGroupWord,
    exportGroupHTML,
    isExporting
  };
};

export default EmployeeReportExport;
