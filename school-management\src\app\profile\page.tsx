'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { localStorageManager } from '@/utils/localStorage';
import { User } from '@/types';
import { getDeveloperInfo } from '@/utils/appInfo';
import NavigationButtons from '@/components/NavigationButtons';

export default function ProfilePage() {
  const { user } = useAuth();
  const [profileData, setProfileData] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // حالة تغيير كلمة المرور
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordSaving, setPasswordSaving] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  // معلومات المطور
  const developerInfo = getDeveloperInfo();

  useEffect(() => {
    if (user) {
      setProfileData({ ...user });
      setLoading(false);
    }
  }, [user]);

  const handleSave = async () => {
    if (!profileData) return;

    setSaving(true);
    try {
      // تحديث بيانات المستخدم في localStorage
      const users = localStorageManager.getUsers();
      const updatedUsers = users.map(u => 
        u.id === profileData.id ? { ...profileData, updatedAt: new Date() } : u
      );
      
      localStorageManager.saveUsers(updatedUsers);
      localStorageManager.setCurrentUser(profileData);
      
      setMessage({ type: 'success', text: 'تم حفظ الملف الشخصي بنجاح' });
      setIsEditing(false);
      
      // إخفاء الرسالة بعد 3 ثوان
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الملف الشخصي:', error);
      setMessage({ type: 'error', text: 'فشل في حفظ الملف الشخصي' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof User, value: any) => {
    if (!profileData) return;

    setProfileData({
      ...profileData,
      [field]: value
    });
  };

  const handlePasswordChange = async () => {
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'يرجى ملء جميع حقول كلمة المرور' });
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتين' });
      return;
    }

    if (!user) {
      setMessage({ type: 'error', text: 'لم يتم العثور على بيانات المستخدم' });
      return;
    }

    // التحقق من كلمة المرور الحالية
    if (user.password !== passwordData.currentPassword) {
      setMessage({ type: 'error', text: 'كلمة المرور الحالية غير صحيحة' });
      return;
    }

    setPasswordSaving(true);
    try {
      // تحديث كلمة المرور في localStorage
      const users = localStorageManager.getUsers();
      const updatedUsers = users.map(u =>
        u.id === user.id ? { ...u, password: passwordData.newPassword, updatedAt: new Date() } : u
      );

      localStorageManager.saveUsers(updatedUsers);

      // تحديث المستخدم الحالي
      const updatedUser = { ...user, password: passwordData.newPassword, updatedAt: new Date() };
      localStorageManager.setCurrentUser(updatedUser);

      setMessage({ type: 'success', text: 'تم تغيير كلمة المرور بنجاح' });
      setIsChangingPassword(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      // إخفاء الرسالة بعد 3 ثوان
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      setMessage({ type: 'error', text: 'حدث خطأ أثناء تغيير كلمة المرور' });
    } finally {
      setPasswordSaving(false);
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'developer': return 'المطور';
      case 'admin': return 'مدير النظام';
      case 'teacher': return 'معلم';
      case 'student': return 'طالب';
      case 'parent': return 'ولي أمر';
      default: return 'غير محدد';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'developer': return '👨‍💻';
      case 'admin': return '👑';
      case 'teacher': return '👨‍🏫';
      case 'student': return '👨‍🎓';
      case 'parent': return '👨‍👩‍👧‍👦';
      default: return '👤';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'developer': return 'from-purple-500 to-violet-600';
      case 'admin': return 'from-red-500 to-pink-600';
      case 'teacher': return 'from-green-500 to-emerald-600';
      case 'student': return 'from-blue-500 to-cyan-600';
      case 'parent': return 'from-orange-500 to-amber-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الملف الشخصي...</p>
        </div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 text-lg">فشل في تحميل الملف الشخصي</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* أزرار التنقل */}
      <NavigationButtons className="mb-6" />

      {/* العنوان */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">👤 الملف الشخصي</h1>
        <p className="text-gray-600">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
      </div>

      {/* رسائل التنبيه */}
      {message && (
        <div className={`mb-6 p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-800' 
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          <div className="flex items-center">
            <span className="ml-2">
              {message.type === 'success' ? '✅' : '❌'}
            </span>
            {message.text}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* بطاقة المستخدم */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className={`w-24 h-24 bg-gradient-to-br ${getRoleColor(profileData.role)} rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg`}>
              <span className="text-white text-3xl">{getRoleIcon(profileData.role)}</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">{profileData.name}</h2>
            <p className="text-gray-600 mb-4">{getRoleDisplayName(profileData.role)}</p>
            <p className="text-sm text-gray-500 mb-4">{profileData.email}</p>
            
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <div className="text-sm text-gray-600 space-y-2">
                <div className="flex justify-between">
                  <span>تاريخ الإنشاء:</span>
                  <span>{new Date(profileData.createdAt).toLocaleDateString('ar-SA')}</span>
                </div>
                <div className="flex justify-between">
                  <span>آخر تحديث:</span>
                  <span>{new Date(profileData.updatedAt).toLocaleDateString('ar-SA')}</span>
                </div>
              </div>
            </div>

            <button
              onClick={() => setIsEditing(!isEditing)}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {isEditing ? 'إلغاء التعديل' : 'تعديل الملف الشخصي'}
            </button>
          </div>
        </div>

        {/* معلومات المستخدم */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
              <span className="ml-2">📝</span>
              المعلومات الشخصية
            </h3>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الاسم الكامل
                </label>
                <input
                  type="text"
                  value={profileData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={!isEditing}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    !isEditing ? 'bg-gray-50 cursor-not-allowed' : ''
                  }`}
                  placeholder="أدخل الاسم الكامل"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!isEditing}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    !isEditing ? 'bg-gray-50 cursor-not-allowed' : ''
                  }`}
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الدور في النظام
                </label>
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`w-8 h-8 bg-gradient-to-br ${getRoleColor(profileData.role)} rounded-full flex items-center justify-center`}>
                    <span className="text-white text-sm">{getRoleIcon(profileData.role)}</span>
                  </div>
                  <span className="text-gray-900 font-medium">{getRoleDisplayName(profileData.role)}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">لا يمكن تغيير الدور - يرجى التواصل مع المدير</p>
              </div>

              {isEditing && (
                <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
                  <button
                    onClick={() => {
                      setIsEditing(false);
                      setProfileData({ ...user! });
                    }}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                  >
                    {saving && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    )}
                    {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* تغيير كلمة المرور */}
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
              <span className="ml-2">🔒</span>
              تغيير كلمة المرور
            </h3>

            {!isChangingPassword ? (
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  لحماية حسابك، يُنصح بتغيير كلمة المرور بانتظام
                </p>
                <button
                  onClick={() => setIsChangingPassword(true)}
                  className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  تغيير كلمة المرور
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الحالية
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.current ? 'text' : 'password'}
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10"
                      placeholder="أدخل كلمة المرور الحالية"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords({...showPasswords, current: !showPasswords.current})}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPasswords.current ? '🙈' : '👁️'}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.new ? 'text' : 'password'}
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10"
                      placeholder="أدخل كلمة المرور الجديدة"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords({...showPasswords, new: !showPasswords.new})}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPasswords.new ? '🙈' : '👁️'}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    يجب أن تكون كلمة المرور 3 أحرف على الأقل
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirm ? 'text' : 'password'}
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10"
                      placeholder="أعد إدخال كلمة المرور الجديدة"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords({...showPasswords, confirm: !showPasswords.confirm})}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPasswords.confirm ? '🙈' : '👁️'}
                    </button>
                  </div>
                </div>

                <div className="flex justify-end space-x-4 space-x-reverse pt-4 border-t border-gray-200">
                  <button
                    onClick={() => {
                      setIsChangingPassword(false);
                      setPasswordData({
                        currentPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                      });
                      setMessage(null);
                    }}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handlePasswordChange}
                    disabled={passwordSaving}
                    className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                  >
                    {passwordSaving && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    )}
                    {passwordSaving ? 'جاري التغيير...' : 'تغيير كلمة المرور'}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* معلومات إضافية */}
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
              <span className="ml-2">ℹ️</span>
              معلومات إضافية
            </h3>
            
            <div className="bg-blue-50 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-800 mb-2">نصائح الأمان</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• تأكد من استخدام كلمة مرور قوية</li>
                <li>• لا تشارك بيانات تسجيل الدخول مع أحد</li>
                <li>• قم بتسجيل الخروج عند الانتهاء من الاستخدام</li>
              </ul>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-2">معلومات التطوير</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p>المطور: {developerInfo.name}</p>
                <p>رقم التواصل: 07813332882</p>
                <p>جميع الحقوق محفوظة 2025</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
